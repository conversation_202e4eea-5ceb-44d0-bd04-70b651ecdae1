diff --git a/node_modules/react-native-biometrics/android/.gradle/5.6.4/fileChanges/last-build.bin b/node_modules/react-native-biometrics/android/.gradle/5.6.4/fileChanges/last-build.bin
new file mode 100644
index 0000000..f76dd23
Binary files /dev/null and b/node_modules/react-native-biometrics/android/.gradle/5.6.4/fileChanges/last-build.bin differ
diff --git a/node_modules/react-native-biometrics/android/.gradle/5.6.4/fileHashes/fileHashes.lock b/node_modules/react-native-biometrics/android/.gradle/5.6.4/fileHashes/fileHashes.lock
new file mode 100644
index 0000000..bab9314
Binary files /dev/null and b/node_modules/react-native-biometrics/android/.gradle/5.6.4/fileHashes/fileHashes.lock differ
diff --git a/node_modules/react-native-biometrics/android/.gradle/5.6.4/gc.properties b/node_modules/react-native-biometrics/android/.gradle/5.6.4/gc.properties
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-biometrics/android/.gradle/nb-cache/trust/44D2D7ECC4C64B7720410ECE715364660377843910D54F43E652959907F03C24 b/node_modules/react-native-biometrics/android/.gradle/nb-cache/trust/44D2D7ECC4C64B7720410ECE715364660377843910D54F43E652959907F03C24
new file mode 100644
index 0000000..6140cf5
--- /dev/null
+++ b/node_modules/react-native-biometrics/android/.gradle/nb-cache/trust/44D2D7ECC4C64B7720410ECE715364660377843910D54F43E652959907F03C24
@@ -0,0 +1 @@
+A727D1C0E1FBBA65678FC014D94FCDB8BBFF2BB4E2A31E206DB8CE9D4D339847
diff --git a/node_modules/react-native-biometrics/android/.gradle/nb-cache/trust/AE926C1EFA6B4D47FE89B7F98C8F64F9C413A8D38A84721F045D57BC1BBC1930 b/node_modules/react-native-biometrics/android/.gradle/nb-cache/trust/AE926C1EFA6B4D47FE89B7F98C8F64F9C413A8D38A84721F045D57BC1BBC1930
new file mode 100644
index 0000000..b7dbda3
--- /dev/null
+++ b/node_modules/react-native-biometrics/android/.gradle/nb-cache/trust/AE926C1EFA6B4D47FE89B7F98C8F64F9C413A8D38A84721F045D57BC1BBC1930
@@ -0,0 +1 @@
+62C7B5E1DBFC79840416B6B5CEB49AC324EB6B646841FEFA0AE722B99C7F588B
diff --git a/node_modules/react-native-biometrics/android/build.gradle b/node_modules/react-native-biometrics/android/build.gradle
index 7e6d6ac..09939ba 100644
--- a/node_modules/react-native-biometrics/android/build.gradle
+++ b/node_modules/react-native-biometrics/android/build.gradle
@@ -2,34 +2,21 @@ apply plugin: 'com.android.library'
 
 description = 'react-native-biometrics'
 
-buildscript {
-    // The Android Gradle plugin is only required when opening the android folder stand-alone.
-    // This avoids unnecessary downloads and potential conflicts when the library is included as a
-    // module dependency in an application project.
-    if (project == rootProject) {
-        repositories {
-            mavenCentral()
-            maven { url "$rootDir/../node_modules/react-native/android" }
-            google()
-        }
-
-        dependencies {
-            classpath("com.android.tools.build:gradle:3.6.2")
-
-        }
-    }
-}
-
 def safeExtGet(prop, fallback) {
     rootProject.ext.has(prop) ? rootProject.ext.get(prop) : fallback
 }
 
 android {
-    compileSdkVersion safeExtGet('compileSdkVersion', 29)
+    def agpVersion = com.android.Version.ANDROID_GRADLE_PLUGIN_VERSION
+    if (agpVersion.tokenize('.')[0].toInteger() >= 7) {
+        namespace "com.rnbiometrics"
+    }
+    
+    compileSdkVersion safeExtGet('compileSdkVersion', 34)
 
     defaultConfig {
-        minSdkVersion safeExtGet('minSdkVersion', 16)
-        targetSdkVersion safeExtGet('targetSdkVersion', 29)
+        minSdkVersion safeExtGet('minSdkVersion', 21)
+        targetSdkVersion safeExtGet('targetSdkVersion', 34)
     }
     lintOptions {
         abortOnError false
