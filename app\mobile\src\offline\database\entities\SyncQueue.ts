// Temporarily using mock TypeORM - will be replaced with proper database solution
// Note: Decorators removed for React Native compatibility

export class SyncQueue {
  id: string;

  entityType: string;

  entityId: string;

  action: 'create' | 'update' | 'delete';

  data: string; // JSON string of the data to sync

  retryCount: number;

  priority: number; // Lower number = higher priority

  status: 'pending' | 'processing' | 'completed' | 'failed';

  error: string;

  createdAt: Date;

  processedAt: string;

  metadata: string; // JSON string
}