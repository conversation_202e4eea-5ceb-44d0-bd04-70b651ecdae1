import React, { Component, ErrorInfo, ReactNode } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, DevSettings } from 'react-native';
import { Center, VStack, Heading, Button } from 'native-base';
import { logger } from '../../utils/logger';
// import RNRestart from 'react-native-restart';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    logger.error('ErrorBoundary caught an error:', error, errorInfo);
    // You can log the error to an error reporting service here
  }

  handleRestart = () => {
    // Use DevSettings.reload() in development
    // In production, you might want to handle this differently
    if (__DEV__ && DevSettings) {
      DevSettings.reload();
    } else {
      // In production, you might want to navigate to home screen
      // or show a message to manually restart the app
      logger.info('App restart requested');
    }
  };

  render() {
    if (this.state.hasError) {
      return (
        <Center flex={1} bg="white" px={4}>
          <VStack space={4} alignItems="center">
            <Heading size="lg" color="danger.500">
              Oops! Something went wrong
            </Heading>
            <Text style={styles.errorText}>
              {this.state.error?.message || 'An unexpected error occurred'}
            </Text>
            <Button onPress={this.handleRestart} colorScheme="primary">
              Restart App
            </Button>
          </VStack>
        </Center>
      );
    }

    return this.props.children;
  }
}

const styles = StyleSheet.create({
  errorText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginHorizontal: 20,
  },
});