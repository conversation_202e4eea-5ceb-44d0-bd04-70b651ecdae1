/**
 * Prisma Query Optimization Patterns
 * 
 * This file contains best practices and patterns for optimizing Prisma queries
 * in the electrical contracting application.
 */

import { Prisma } from '@prisma/client';
import { logger } from './logger';

/**
 * 1. Use SELECT to load only required fields
 * Instead of loading entire records, specify only the fields you need
 */
export const selectOptimizationExample = {
  // BAD - Loads all fields
  bad: async (prisma: any) => {
    return await prisma.project.findMany();
  },
  
  // GOOD - Loads only required fields
  good: async (prisma: any) => {
    return await prisma.project.findMany({
      select: {
        id: true,
        name: true,
        status: true,
        customer: {
          select: {
            name: true,
            email: true
          }
        }
      }
    });
  }
};

/**
 * 2. Use _count instead of loading relations for counting
 */
export const countOptimizationExample = {
  // BAD - Loads all related records just to count
  bad: async (prisma: any, projectId: string) => {
    const project = await prisma.project.findUnique({
      where: { id: projectId },
      include: { estimates: true }
    });
    return project.estimates.length;
  },
  
  // GOOD - Uses _count aggregate
  good: async (prisma: any, projectId: string) => {
    const project = await prisma.project.findUnique({
      where: { id: projectId },
      select: {
        _count: {
          select: { estimates: true }
        }
      }
    });
    return project._count.estimates;
  }
};

/**
 * 3. Use take/skip for pagination instead of loading all records
 */
export const paginationOptimizationExample = {
  // BAD - Loads all records then slices in memory
  bad: async (prisma: any, page: number, limit: number) => {
    const allProjects = await prisma.project.findMany();
    return allProjects.slice((page - 1) * limit, page * limit);
  },
  
  // GOOD - Database-level pagination
  good: async (prisma: any, page: number, limit: number) => {
    return await prisma.project.findMany({
      skip: (page - 1) * limit,
      take: limit
    });
  }
};

/**
 * 4. Use findFirst for existence checks
 */
export const existenceCheckOptimization = {
  // BAD - Loads entire record for existence check
  bad: async (prisma: any, customerId: string) => {
    const customer = await prisma.customer.findUnique({
      where: { id: customerId }
    });
    return customer !== null;
  },
  
  // GOOD - Only checks existence
  good: async (prisma: any, customerId: string) => {
    const customer = await prisma.customer.findFirst({
      where: { id: customerId },
      select: { id: true }
    });
    return customer !== null;
  }
};

/**
 * 5. Batch operations instead of N+1 queries
 */
export const batchOperationOptimization = {
  // BAD - N+1 query problem
  bad: async (prisma: any, projectIds: string[]) => {
    const results = [];
    for (const id of projectIds) {
      const project = await prisma.project.findUnique({
        where: { id }
      });
      results.push(project);
    }
    return results;
  },
  
  // GOOD - Single query
  good: async (prisma: any, projectIds: string[]) => {
    return await prisma.project.findMany({
      where: {
        id: { in: projectIds }
      }
    });
  }
};

/**
 * 6. Use transactions for related operations
 */
export const transactionOptimization = {
  // BAD - Multiple separate queries
  bad: async (prisma: any, estimateId: string, items: any[]) => {
    await prisma.materialItem.deleteMany({
      where: { estimate_id: estimateId }
    });
    
    for (const item of items) {
      await prisma.materialItem.create({
        data: { ...item, estimate_id: estimateId }
      });
    }
  },
  
  // GOOD - Atomic transaction
  good: async (prisma: any, estimateId: string, items: any[]) => {
    return await prisma.$transaction([
      prisma.materialItem.deleteMany({
        where: { estimate_id: estimateId }
      }),
      prisma.materialItem.createMany({
        data: items.map(item => ({ ...item, estimate_id: estimateId }))
      })
    ]);
  }
};

/**
 * 7. Use raw queries for complex aggregations
 */
export const complexAggregationOptimization = {
  // GOOD - Raw query for complex aggregation
  example: async (prisma: any, projectId: string) => {
    return await prisma.$queryRaw`
      SELECT 
        c.load_type,
        COUNT(*) as circuit_count,
        SUM(c.calculated_load) as total_load,
        AVG(c.calculated_load) as avg_load
      FROM Circuit c
      JOIN Panel p ON c.panel_id = p.id
      WHERE p.project_id = ${projectId}
        AND c.is_spare = false
        AND c.is_space = false
      GROUP BY c.load_type
      ORDER BY total_load DESC
    `;
  }
};

/**
 * 8. Connection pool configuration
 */
export const connectionPoolConfig = {
  // In your Prisma client initialization
  datasourceUrl: process.env.DATABASE_URL + '?connection_limit=10&pool_timeout=20'
};

/**
 * 9. Prisma query optimization middleware
 */
export const createQueryLoggingMiddleware = () => {
  return async (params: any, next: any) => {
    const before = Date.now();
    const result = await next(params);
    const after = Date.now();
    
    // Log slow queries in development
    if (process.env.NODE_ENV === 'development' && after - before > 100) {
      logger.debug(`Slow query detected (${after - before}ms):`, {
        model: params.model,
        action: params.action,
        args: params.args
      });
    }
    
    return result;
  };
};

/**
 * 10. Optimized relation loading patterns
 */
export const relationLoadingPatterns = {
  // Load relations conditionally based on needs
  conditionalInclude: async (prisma: any, projectId: string, includeEstimates: boolean) => {
    return await prisma.project.findUnique({
      where: { id: projectId },
      include: {
        customer: true,
        ...(includeEstimates && {
          estimates: {
            orderBy: { created_at: 'desc' },
            take: 5,
            select: {
              id: true,
              version: true,
              status: true,
              total_amount: true
            }
          }
        })
      }
    });
  },
  
  // Use nested select for deep relations
  nestedSelect: async (prisma: any, estimateId: string) => {
    return await prisma.estimate.findUnique({
      where: { id: estimateId },
      select: {
        id: true,
        total_amount: true,
        project: {
          select: {
            name: true,
            customer: {
              select: {
                name: true,
                email: true
              }
            }
          }
        },
        material_items: {
          select: {
            id: true,
            description: true,
            total_amount: true
          },
          orderBy: { total_amount: 'desc' },
          take: 10
        }
      }
    });
  }
};

/**
 * Example of optimized repository pattern
 */
export class OptimizedProjectRepository {
  constructor(private prisma: any) {}
  
  async findManyWithPagination(params: {
    page: number;
    limit: number;
    search?: string;
    status?: string;
  }) {
    const where = {
      ...(params.search && {
        OR: [
          { name: { contains: params.search, mode: 'insensitive' as const } },
          { address: { contains: params.search, mode: 'insensitive' as const } }
        ]
      }),
      ...(params.status && { status: params.status })
    };
    
    const [data, total] = await Promise.all([
      this.prisma.project.findMany({
        where,
        skip: (params.page - 1) * params.limit,
        take: params.limit,
        select: {
          id: true,
          name: true,
          status: true,
          type: true,
          created_at: true,
          customer: {
            select: {
              name: true
            }
          },
          _count: {
            select: {
              estimates: true,
              panels: true
            }
          }
        }
      }),
      this.prisma.project.count({ where })
    ]);
    
    return {
      data,
      pagination: {
        page: params.page,
        limit: params.limit,
        total,
        totalPages: Math.ceil(total / params.limit)
      }
    };
  }
  
  async findByIdOptimized(id: string) {
    return await this.prisma.project.findUnique({
      where: { id },
      select: {
        id: true,
        name: true,
        address: true,
        status: true,
        type: true,
        voltage_system: true,
        service_size: true,
        customer: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true
          }
        },
        // Recent activity summary
        estimates: {
          orderBy: { created_at: 'desc' },
          take: 3,
          select: {
            id: true,
            version: true,
            status: true,
            total_amount: true,
            created_at: true
          }
        },
        panels: {
          select: {
            id: true,
            name: true,
            ampere_rating: true,
            spaces_used: true,
            spaces_total: true
          }
        },
        _count: {
          select: {
            calculations: true,
            permit_documents: true,
            inspection_checklists: true
          }
        }
      }
    });
  }
}