import React from 'react';
import ReactDOM from 'react-dom/client';
import { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { Toaster } from 'react-hot-toast';
import App from './App';
import './index.css';

// Debug: Log App component
console.log('[Main] App component:', App);
console.log('[Main] App type:', typeof App);
console.log('[Main] App name:', App?.name);

// Configure React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

// Debug: Log when main.tsx loads - Updated
console.log('[Main] Starting React app...', new Date().toISOString());

const rootElement = document.getElementById('root');

if (!rootElement) {
  console.error('[Main] Root element not found!');
  throw new Error('Root element not found!');
}

console.log('[Main] Root element found, rendering app...');

try {
  // Temporarily comment out auth-related imports to isolate issues
  // import { setupAuthHealthCheck } from './utils/auth-recovery';
  // import './utils/auth-test';
  
  ReactDOM.createRoot(rootElement).render(
    <React.StrictMode>
      <QueryClientProvider client={queryClient}>
        <BrowserRouter 
          future={{ 
            v7_startTransition: true,
            v7_relativeSplatPath: true 
          }}
        >
          <App />
          <Toaster position="top-right" />
        </BrowserRouter>
        <ReactQueryDevtools initialIsOpen={false} />
      </QueryClientProvider>
    </React.StrictMode>
  );
  
  console.log('[Main] App rendered successfully');
} catch (error) {
  console.error('[Main] Error rendering app:', error);
  // Show error in DOM
  rootElement.innerHTML = `
    <div style="padding: 20px; font-family: monospace; color: red;">
      <h1>Failed to load application</h1>
      <pre>${error}</pre>
    </div>
  `;
}