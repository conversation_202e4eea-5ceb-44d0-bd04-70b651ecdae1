import { useState } from 'react';
import { FileBarChart, Download, Calendar, Filter, TrendingUp, DollarSign, Clock, Users } from 'lucide-react';
import { Card } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { SimpleSelect } from '../../components/common/SimpleSelect';

export function ReportsPage() {
  const [selectedReport, setSelectedReport] = useState('');
  const [dateRange, setDateRange] = useState({ start: '', end: '' });

  const reportTypes = [
    { value: 'project-summary', label: 'Project Summary Report' },
    { value: 'revenue', label: 'Revenue Report' },
    { value: 'labor-hours', label: 'Labor Hours Report' },
    { value: 'material-usage', label: 'Material Usage Report' },
    { value: 'customer-activity', label: 'Customer Activity Report' },
    { value: 'permit-status', label: 'Permit Status Report' },
    { value: 'inspection-summary', label: 'Inspection Summary Report' },
  ];

  const recentReports = [
    {
      id: 1,
      name: 'Monthly Revenue Report - November 2024',
      type: 'Revenue Report',
      generatedAt: '2024-11-30',
      icon: DollarSign,
      status: 'completed',
    },
    {
      id: 2,
      name: 'Q4 Labor Hours Summary',
      type: 'Labor Hours Report',
      generatedAt: '2024-11-28',
      icon: Clock,
      status: 'completed',
    },
    {
      id: 3,
      name: 'Active Projects Status',
      type: 'Project Summary Report',
      generatedAt: '2024-11-27',
      icon: TrendingUp,
      status: 'completed',
    },
    {
      id: 4,
      name: 'Customer Engagement Analysis',
      type: 'Customer Activity Report',
      generatedAt: '2024-11-25',
      icon: Users,
      status: 'completed',
    },
  ];

  const handleGenerateReport = () => {
    if (!selectedReport) {
      alert('Please select a report type');
      return;
    }
    // TODO: Implement report generation
    console.log('Generating report:', selectedReport, dateRange);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Reports</h1>
      </div>

      {/* Report Generation Card */}
      <Card className="p-6">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Generate New Report
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Report Type
            </label>
            <SimpleSelect
              value={selectedReport}
              onChange={setSelectedReport}
              options={reportTypes}
              placeholder="Select a report type"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Start Date
            </label>
            <Input
              type="date"
              value={dateRange.start}
              onChange={(e) => setDateRange({ ...dateRange, start: e.target.value })}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              End Date
            </label>
            <Input
              type="date"
              value={dateRange.end}
              onChange={(e) => setDateRange({ ...dateRange, end: e.target.value })}
            />
          </div>
        </div>
        <div className="mt-4 flex justify-end">
          <Button onClick={handleGenerateReport}>
            <FileBarChart className="h-4 w-4 mr-2" />
            Generate Report
          </Button>
        </div>
      </Card>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Total Reports</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">156</p>
            </div>
            <FileBarChart className="h-8 w-8 text-primary-500" />
          </div>
        </Card>
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">This Month</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">24</p>
            </div>
            <Calendar className="h-8 w-8 text-green-500" />
          </div>
        </Card>
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Scheduled</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">8</p>
            </div>
            <Clock className="h-8 w-8 text-blue-500" />
          </div>
        </Card>
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Favorites</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">12</p>
            </div>
            <Filter className="h-8 w-8 text-yellow-500" />
          </div>
        </Card>
      </div>

      {/* Recent Reports */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Recent Reports</h2>
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
        </div>
        <div className="space-y-3">
          {recentReports.map((report) => (
            <div
              key={report.id}
              className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
            >
              <div className="flex items-center space-x-4">
                <div className="flex-shrink-0">
                  <report.icon className="h-8 w-8 text-gray-500 dark:text-gray-400" />
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                    {report.name}
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {report.type} • Generated on {report.generatedAt}
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="ghost"
                  size="sm"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </Button>
              </div>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
}