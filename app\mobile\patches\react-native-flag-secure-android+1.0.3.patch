diff --git a/node_modules/react-native-flag-secure-android/android/build.gradle b/node_modules/react-native-flag-secure-android/android/build.gradle
index 88fa0af..e6b2c7a 100644
--- a/node_modules/react-native-flag-secure-android/android/build.gradle
+++ b/node_modules/react-native-flag-secure-android/android/build.gradle
@@ -1,27 +1,19 @@
-buildscript {
-    repositories {
-        mavenLocal()
-        mavenCentral()
-        google()
-        jcenter()
-    }
-
-    dependencies {
-        classpath 'com.android.tools.build:gradle:1.5.0'
-    }
-}
-
 apply plugin: 'com.android.library'
 
 def safeExtGet(prop, fallback) {
     rootProject.ext.has(prop) ? rootProject.ext.get(prop) : fallback
 }
 
-def DEFAULT_COMPILE_SDK_VERSION = 23
-def DEFAULT_MIN_SDK_VERSION = 16
-def DEFAULT_TARGET_SDK_VERSION = 22
+def DEFAULT_COMPILE_SDK_VERSION = 34
+def DEFAULT_MIN_SDK_VERSION = 21
+def DEFAULT_TARGET_SDK_VERSION = 34
 
 android {
+    def agpVersion = com.android.Version.ANDROID_GRADLE_PLUGIN_VERSION
+    if (agpVersion.tokenize('.')[0].toInteger() >= 7) {
+        namespace "com.staltz.flagsecure"
+    }
+    
     compileSdkVersion safeExtGet('compileSdkVersion', DEFAULT_COMPILE_SDK_VERSION)
 
     defaultConfig {
@@ -36,5 +28,5 @@ android {
 }
 
 dependencies {
-    compile 'com.facebook.react:react-native:+'
+    implementation 'com.facebook.react:react-native:+'
 }
diff --git a/node_modules/react-native-flag-secure-android/android/src/main/AndroidManifest.xml b/node_modules/react-native-flag-secure-android/android/src/main/AndroidManifest.xml
index e5a4078..12a1c84 100644
--- a/node_modules/react-native-flag-secure-android/android/src/main/AndroidManifest.xml
+++ b/node_modules/react-native-flag-secure-android/android/src/main/AndroidManifest.xml
@@ -1,4 +1,3 @@
 <?xml version="1.0" encoding="utf-8"?>
-<manifest xmlns:android="http://schemas.android.com/apk/res/android"
-    package="com.staltz.flagsecure" >
+<manifest xmlns:android="http://schemas.android.com/apk/res/android" >
 </manifest>