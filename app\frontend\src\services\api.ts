import { ApiInterceptors, apiClient } from '../utils/api-interceptors';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';

// Use the enhanced API client with interceptors
export const api = apiClient;

// Token management
let authToken: string | null = null;

export const setAuthToken = (token: string | null): void => {
  authToken = token;
  if (token) {
    // Set on the apiClient instance, not on a generic api object
    apiClient.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    console.log('[setAuthToken] Token set:', token.substring(0, 10) + '...');
  } else {
    delete apiClient.defaults.headers.common['Authorization'];
    console.log('[setAuthToken] Token cleared');
  }
};

// Reset correlation ID on route change
export const resetCorrelationId = (): void => {
  ApiInterceptors.resetCorrelationId();
};