@echo off
echo ============================================================
echo       Electrical Contracting Application - Windows
echo ============================================================
echo.

REM Check Node.js
where node >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed!
    echo Please install Node.js 18+ from https://nodejs.org/
    pause
    exit /b 1
)

REM Navigate to app directory
cd /d "%~dp0app"

REM Check if services are already running
echo Checking running services...
netstat -an | find ":3001" | find "LISTENING" >nul
if %errorlevel% equ 0 (
    set BACKEND_RUNNING=1
    echo - Backend is already running on port 3001
) else (
    set BACKEND_RUNNING=0
    echo - Backend is not running
)

netstat -an | find ":3000" | find "LISTENING" >nul
if %errorlevel% equ 0 (
    set FRONTEND_RUNNING=1
    echo - Frontend is already running on port 3000
) else (
    set FRONTEND_RUNNING=0
    echo - Frontend is not running
)

REM If both are running, just show the URLs
if "%BACKEND_RUNNING%"=="1" if "%FRONTEND_RUNNING%"=="1" (
    echo.
    echo ============================================================
    echo Application is already running!
    echo.
    echo Open your browser to: http://localhost:3000
    echo.
    echo You can:
    echo - Register a new account by clicking "Sign up"
    echo - Or use the demo account: <EMAIL> / password123
    echo ============================================================
    echo.
    pause
    exit /b 0
)

REM Check pnpm
where pnpm >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo Installing pnpm package manager...
    call npm install -g pnpm
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install pnpm
        pause
        exit /b 1
    )
)

REM Install dependencies if needed
echo.
echo Checking dependencies...
if not exist "node_modules" goto install_deps
if not exist "backend\node_modules" goto install_deps
if not exist "frontend\node_modules" goto install_deps
echo Dependencies already installed.
goto check_database

:install_deps
echo Installing all dependencies - first time setup, may take 5-10 minutes...
call pnpm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

:check_database
REM Setup database if needed
if not exist "backend\prisma\dev.db" (
    echo.
    echo Setting up database...
    cd backend
    call npx prisma generate
    if %errorlevel% neq 0 (
        echo ERROR: Failed to generate Prisma client
        cd ..
        pause
        exit /b 1
    )
    call npx prisma migrate deploy
    if %errorlevel% neq 0 (
        echo ERROR: Failed to run migrations
        cd ..
        pause
        exit /b 1
    )
    cd ..
)

REM Start services that aren't running
echo.
echo Starting application servers...
echo.

REM Start backend if not running
if "%BACKEND_RUNNING%"=="0" (
    start "Backend Server - Port 3001" cmd /k "cd backend && echo Starting Backend Server on port 3001... && npm run dev"
    REM Wait for backend to initialize
    timeout /t 3 /nobreak > nul
)

REM Start frontend if not running
if "%FRONTEND_RUNNING%"=="0" (
    start "Frontend App - Port 3000" cmd /k "cd frontend && echo Starting Frontend App on port 3000... && npm run dev"
)

echo.
echo ============================================================
if "%BACKEND_RUNNING%"=="0" if "%FRONTEND_RUNNING%"=="0" (
    echo Application servers are starting in separate windows!
) else if "%BACKEND_RUNNING%"=="0" (
    echo Backend server is starting in a new window!
    echo Frontend was already running.
) else if "%FRONTEND_RUNNING%"=="0" (
    echo Frontend app is starting in a new window!
    echo Backend was already running.
)
echo.
echo Wait about 30 seconds, then open your browser to:
echo http://localhost:3000
echo.
echo You can:
echo - Register a new account by clicking "Sign up"
echo - Or use the demo account: <EMAIL> / password123
echo.
echo If servers don't start, check the other windows for errors.
echo ============================================================
echo.
pause