{"name": "electrical-contracting-saas", "version": "1.0.0", "description": "Electrical contracting application with AI agents", "private": true, "workspaces": ["frontend", "backend", "shared", "agents"], "scripts": {"dev": "pnpm run --parallel dev", "dev:backend": "pnpm --filter @electrical/backend dev", "dev:frontend": "pnpm --filter @electrical/frontend dev", "dev:main": "npx concurrently \"pnpm --filter @electrical/backend dev\" \"pnpm --filter @electrical/frontend dev\"", "dev:main:win": "node_modules/.bin/concurrently \"cd backend && npm run dev\" \"cd frontend && npm run dev\"", "dev:simple": "pnpm --filter @electrical/backend dev & pnpm --filter @electrical/frontend dev", "build": "pnpm run -r build", "test": "pnpm run -r test", "lint": "pnpm run -r lint", "typecheck": "pnpm run -r typecheck"}, "devDependencies": {"@types/node": "^20.11.5", "@ungap/structured-clone": "^1.3.0", "concurrently": "^8.2.2", "rxjs": "^7.8.2", "typescript": "^5.3.3"}, "engines": {"node": ">=20.0.0", "pnpm": ">=8.0.0"}}