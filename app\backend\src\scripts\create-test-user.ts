import { PrismaClient } from '@prisma/client';
import * as bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  try {
    // Check if test user exists
    const existingUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });
    
    if (existingUser) {
      console.log('Test user already exists, updating password...');
      const hashedPassword = await bcrypt.hash('Test123!', 10);
      await prisma.user.update({
        where: { email: '<EMAIL>' },
        data: { password_hash: hashedPassword }
      });
      console.log('Password updated');
    } else {
      console.log('Creating test user...');
      const hashedPassword = await bcrypt.hash('Test123!', 10);
      await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password_hash: hashedPassword,
          name: 'Test User',
          role: 'admin',
          company_id: 'default-company'
        }
      });
      console.log('Test user created');
    }
    
    console.log('\nLogin credentials:');
    console.log('Email: <EMAIL>');
    console.log('Password: Test123!');
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();