# Manual Test Instructions

## Test the /quotes/new redirect issue

1. Open browser to http://localhost:3000/login
2. Open Developer Console (F12)
3. Login with:
   - Email: <EMAIL>
   - Password: Test123!
4. After login, manually navigate to: http://localhost:3000/quotes/new
5. Check console for:
   - [RouteDebugger] messages
   - [QuoteFormPage] messages
   - Any error messages

## Expected Result
- Should stay on /quotes/new page
- Should see quote form

## Current Issue
- Redirects back to dashboard (/)

## Things to check in console:
1. Is user.companyId present?
2. Are there any 401 errors?
3. Are there any React errors?
4. What does RouteDebugger show?