org.gradle.jvmargs=-Xmx4g -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8 
org.gradle.daemon=true 
org.gradle.parallel=true 
org.gradle.configureondemand=true 
netbeans.gradle.project.disabled=true 
netbeans.gradle.maven.disabled=true

# React Native configuration
# Use this property to enable or disable the Hermes JS engine.
# If set to false, you will be using JSC instead.
hermesEnabled=true

# Enable new architecture (Fabric/TurboModules) - set to true when ready
newArchEnabled=false

# AndroidX configuration (required for React Native 0.60+)
android.useAndroidX=true
android.enableJetifier=true
