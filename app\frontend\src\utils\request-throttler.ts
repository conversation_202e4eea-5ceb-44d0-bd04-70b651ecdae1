import { AxiosRequestConfig } from 'axios';

interface ThrottleConfig {
  maxRequestsPerSecond: number;
  maxConcurrentRequests: number;
  burstSize: number;
}

interface QueuedRequest {
  config: AxiosRequestConfig;
  resolve: (value: any) => void;
  reject: (error: any) => void;
  priority: number;
  timestamp: number;
}

export class RequestThrottler {
  private queue: QueuedRequest[] = [];
  private activeRequests = 0;
  private requestTimestamps: number[] = [];
  private processing = false;
  
  private config: ThrottleConfig = {
    maxRequestsPerSecond: 10,
    maxConcurrentRequests: 5,
    burstSize: 20,
  };

  constructor(config?: Partial<ThrottleConfig>) {
    this.config = { ...this.config, ...config };
  }

  /**
   * Wait for an available slot before making a request
   * This is a simpler approach that doesn't require adapter overrides
   */
  async waitForSlot(priority: number = 0): Promise<void> {
    // For high priority requests, skip throttling
    if (priority >= 10) {
      this.activeRequests++;
      this.recordRequestTimestamp();
      
      // Schedule cleanup to decrement active requests
      setTimeout(() => {
        this.activeRequests = Math.max(0, this.activeRequests - 1);
      }, 5000); // Assume most requests complete within 5 seconds
      
      return;
    }

    // Wait until we can make a request
    while (!this.canMakeRequest()) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    this.activeRequests++;
    this.recordRequestTimestamp();
    
    // Schedule cleanup to decrement active requests
    setTimeout(() => {
      this.activeRequests = Math.max(0, this.activeRequests - 1);
    }, 5000); // Assume most requests complete within 5 seconds
  }

  /**
   * Add a request to the throttle queue
   */
  async throttle<T>(
    requestFn: () => Promise<T>,
    config?: AxiosRequestConfig,
    priority = 0
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      const queuedRequest: QueuedRequest = {
        config: config || {},
        resolve: async () => {
          try {
            const result = await requestFn();
            resolve(result);
          } catch (error) {
            reject(error);
          } finally {
            this.activeRequests--;
            this.processQueue();
          }
        },
        reject,
        priority,
        timestamp: Date.now(),
      };

      this.queue.push(queuedRequest);
      this.queue.sort((a, b) => b.priority - a.priority);
      
      this.processQueue();
    });
  }

  /**
   * Process the request queue
   */
  private async processQueue(): Promise<void> {
    if (this.processing) return;
    this.processing = true;

    while (this.queue.length > 0 && this.canMakeRequest()) {
      const request = this.queue.shift();
      if (request) {
        this.activeRequests++;
        this.recordRequestTimestamp();
        request.resolve();
      }
    }

    this.processing = false;
  }

  /**
   * Check if we can make another request
   */
  private canMakeRequest(): boolean {
    // Check concurrent request limit
    if (this.activeRequests >= this.config.maxConcurrentRequests) {
      return false;
    }

    // Clean old timestamps
    const now = Date.now();
    this.requestTimestamps = this.requestTimestamps.filter(
      timestamp => now - timestamp < 1000
    );

    // Check rate limit
    if (this.requestTimestamps.length >= this.config.maxRequestsPerSecond) {
      return false;
    }

    // Check burst limit (requests in last 100ms)
    const recentRequests = this.requestTimestamps.filter(
      timestamp => now - timestamp < 100
    );
    if (recentRequests.length >= this.config.burstSize / 10) {
      return false;
    }

    return true;
  }

  /**
   * Record a request timestamp
   */
  private recordRequestTimestamp(): void {
    this.requestTimestamps.push(Date.now());
  }

  /**
   * Clear the queue
   */
  clearQueue(): void {
    this.queue.forEach(request => {
      request.reject(new Error('Request queue cleared'));
    });
    this.queue = [];
  }

  /**
   * Get queue statistics
   */
  getStats(): {
    queueLength: number;
    activeRequests: number;
    requestsPerSecond: number;
  } {
    const now = Date.now();
    const recentRequests = this.requestTimestamps.filter(
      timestamp => now - timestamp < 1000
    );

    return {
      queueLength: this.queue.length,
      activeRequests: this.activeRequests,
      requestsPerSecond: recentRequests.length,
    };
  }

  /**
   * Adjust throttle configuration dynamically
   */
  adjustThrottle(adjustment: 'increase' | 'decrease'): void {
    if (adjustment === 'decrease') {
      this.config.maxRequestsPerSecond = Math.max(1, this.config.maxRequestsPerSecond - 2);
      this.config.maxConcurrentRequests = Math.max(1, this.config.maxConcurrentRequests - 1);
    } else {
      this.config.maxRequestsPerSecond = Math.min(20, this.config.maxRequestsPerSecond + 2);
      this.config.maxConcurrentRequests = Math.min(10, this.config.maxConcurrentRequests + 1);
    }
  }
}

// Create a singleton instance
export const requestThrottler = new RequestThrottler();