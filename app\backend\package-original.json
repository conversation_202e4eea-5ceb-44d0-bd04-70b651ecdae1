{"name": "electrical-backend-standalone", "version": "1.0.0", "description": "Standalone backend for electrical contracting application", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js"}, "dependencies": {"@google/genai": "^1.9.0", "@google/generative-ai": "^0.24.1", "@prisma/client": "^5.8.1", "axios": "^1.6.5", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "date-fns": "^3.3.1", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "prisma": "^5.8.1", "uuid": "^9.0.1", "winston": "^3.11.0", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.11.5", "@types/uuid": "^9.0.7", "tsx": "^4.7.0", "typescript": "^5.3.3"}}