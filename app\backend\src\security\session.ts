import { Request, Response, NextFunction } from 'express';
import { getRedis } from '../services/redis';
import { AppError } from '../middleware/errorHandler';
import { generateSecureToken } from './crypto';
import { createAuditLog, AUDIT_ACTIONS } from './audit';
import { logger } from '../utils/logger';

export interface SessionData {
  userId: string;
  email: string;
  role: string;
  ipAddress: string;
  userAgent: string;
  createdAt: Date;
  lastActivityAt: Date;
  expiresAt: Date;
}

// SessionData interface is already added to Express.Request in types/express.d.ts

const SESSION_PREFIX = 'session:';
const SESSION_DURATION = 3600; // 1 hour
const SESSION_EXTENSION_THRESHOLD = 900; // Extend if less than 15 minutes left
const MAX_CONCURRENT_SESSIONS = 5;

// In-memory session store fallback
const inMemorySessions = new Map<string, SessionData>();
const userSessionMap = new Map<string, Set<string>>();

// Cleanup expired sessions periodically
setInterval(() => {
  const now = new Date();
  for (const [sessionId, session] of inMemorySessions.entries()) {
    if (session.expiresAt < now) {
      inMemorySessions.delete(sessionId);
      const userSessions = userSessionMap.get(session.userId);
      if (userSessions) {
        userSessions.delete(sessionId);
        if (userSessions.size === 0) {
          userSessionMap.delete(session.userId);
        }
      }
    }
  }
}, 60000); // Clean up every minute

// Create new session
export async function createSession(
  userId: string,
  email: string,
  role: string,
  req: Request
): Promise<{ sessionId: string; expiresAt: Date }> {
  const redis = getRedis();
  
  const sessionId = generateSecureToken(32);
  const now = new Date();
  const expiresAt = new Date(now.getTime() + SESSION_DURATION * 1000);
  
  const sessionData: SessionData = {
    userId,
    email,
    role,
    ipAddress: getClientIp(req),
    userAgent: req.headers['user-agent'] || 'unknown',
    createdAt: now,
    lastActivityAt: now,
    expiresAt
  };
  
  // Check concurrent sessions
  await enforceSessionLimit(userId);
  
  if (redis) {
    // Store session in Redis
    await redis.setex(
      `${SESSION_PREFIX}${sessionId}`,
      SESSION_DURATION,
      JSON.stringify(sessionData)
    );
    
    // Track user sessions
    await redis.sadd(`user_sessions:${userId}`, sessionId);
    await redis.expire(`user_sessions:${userId}`, SESSION_DURATION);
  } else {
    // Use in-memory fallback
    logger.warn('Using in-memory session storage (Redis not available)');
    inMemorySessions.set(sessionId, sessionData);
    
    // Track user sessions
    if (!userSessionMap.has(userId)) {
      userSessionMap.set(userId, new Set());
    }
    userSessionMap.get(userId)!.add(sessionId);
  }
  
  return { sessionId, expiresAt };
}

// Session middleware
export function sessionMiddleware(options?: {
  extendOnActivity?: boolean;
  requireFreshSession?: boolean;
}) {
  const extendOnActivity = options?.extendOnActivity ?? true;
  const requireFreshSession = options?.requireFreshSession ?? false;
  
  return async (req: Request, res: Response, next: NextFunction) => {
    const redis = getRedis();
    
    const sessionId = extractSessionId(req);
    
    if (!sessionId) {
      return next(new AppError(401, 'No session found', true, 'NO_SESSION'));
    }
    
    try {
      const sessionData = await getSession(sessionId);
      
      if (!sessionData) {
        return next(new AppError(401, 'Invalid session', true, 'INVALID_SESSION'));
      }
      
      // Check if session expired
      if (new Date() > new Date(sessionData.expiresAt)) {
        await destroySession(sessionId);
        return next(new AppError(401, 'Session expired', true, 'SESSION_EXPIRED'));
      }
      
      // Check if fresh session required
      if (requireFreshSession) {
        const sessionAge = Date.now() - new Date(sessionData.createdAt).getTime();
        const maxAge = 300000; // 5 minutes
        
        if (sessionAge > maxAge) {
          return next(new AppError(401, 'Fresh session required', true, 'FRESH_SESSION_REQUIRED'));
        }
      }
      
      // Check IP address change
      const currentIp = getClientIp(req);
      if (sessionData.ipAddress !== currentIp) {
        await createAuditLog({
          action: AUDIT_ACTIONS.SUSPICIOUS_ACTIVITY,
          userId: sessionData.userId,
          resourceType: 'session',
          resourceId: sessionId,
          details: {
            reason: 'IP address changed',
            originalIp: sessionData.ipAddress,
            newIp: currentIp
          }
        });
        
        // Optionally invalidate session on IP change
        // await destroySession(sessionId);
        // return next(new AppError(401, 'Session invalid due to IP change', true, 'SESSION_IP_MISMATCH'));
      }
      
      // Extend session if needed
      if (extendOnActivity) {
        if (redis) {
          const ttl = await redis.ttl(`${SESSION_PREFIX}${sessionId}`);
          if (ttl < SESSION_EXTENSION_THRESHOLD) {
            await extendSession(sessionId);
          }
        } else {
          // For in-memory sessions, check time remaining
          const timeRemaining = (new Date(sessionData.expiresAt).getTime() - Date.now()) / 1000;
          if (timeRemaining < SESSION_EXTENSION_THRESHOLD) {
            await extendSession(sessionId);
          }
        }
      }
      
      // Update last activity
      sessionData.lastActivityAt = new Date();
      if (redis) {
        await redis.setex(
          `${SESSION_PREFIX}${sessionId}`,
          SESSION_DURATION,
          JSON.stringify(sessionData)
        );
      } else {
        // Update in-memory session
        inMemorySessions.set(sessionId, sessionData);
      }
      
      req.session = sessionData;
      req.sessionId = sessionId;
      
      next();
    } catch (error) {
      next(new AppError(500, 'Session validation failed', true, 'SESSION_ERROR'));
    }
  };
}

// Get session data
export async function getSession(sessionId: string): Promise<SessionData | null> {
  const redis = getRedis();
  
  if (redis) {
    const data = await redis.get(`${SESSION_PREFIX}${sessionId}`);
    return data ? JSON.parse(data) : null;
  } else {
    // Use in-memory fallback
    return inMemorySessions.get(sessionId) || null;
  }
}

// Extend session
export async function extendSession(sessionId: string): Promise<void> {
  const redis = getRedis();
  const sessionData = await getSession(sessionId);
  
  if (sessionData) {
    sessionData.expiresAt = new Date(Date.now() + SESSION_DURATION * 1000);
    
    if (redis) {
      await redis.setex(
        `${SESSION_PREFIX}${sessionId}`,
        SESSION_DURATION,
        JSON.stringify(sessionData)
      );
    } else {
      // Update in-memory session
      inMemorySessions.set(sessionId, sessionData);
    }
  }
}

// Destroy session
export async function destroySession(sessionId: string): Promise<void> {
  const redis = getRedis();
  const sessionData = await getSession(sessionId);
  
  if (sessionData) {
    if (redis) {
      await redis.del(`${SESSION_PREFIX}${sessionId}`);
      await redis.srem(`user_sessions:${sessionData.userId}`, sessionId);
    } else {
      // Remove from in-memory store
      inMemorySessions.delete(sessionId);
      const userSessions = userSessionMap.get(sessionData.userId);
      if (userSessions) {
        userSessions.delete(sessionId);
        if (userSessions.size === 0) {
          userSessionMap.delete(sessionData.userId);
        }
      }
    }
  }
}

// Destroy all user sessions
export async function destroyAllUserSessions(userId: string): Promise<void> {
  const redis = getRedis();
  
  if (redis) {
    const sessionIds = await redis.smembers(`user_sessions:${userId}`);
    
    for (const sessionId of sessionIds) {
      await redis.del(`${SESSION_PREFIX}${sessionId}`);
    }
    
    await redis.del(`user_sessions:${userId}`);
  } else {
    // Use in-memory fallback
    const userSessions = userSessionMap.get(userId);
    if (userSessions) {
      for (const sessionId of userSessions) {
        inMemorySessions.delete(sessionId);
      }
      userSessionMap.delete(userId);
    }
  }
}

// Enforce session limit
async function enforceSessionLimit(userId: string): Promise<void> {
  const redis = getRedis();
  
  let sessionIds: string[] = [];
  
  if (redis) {
    sessionIds = await redis.smembers(`user_sessions:${userId}`);
  } else {
    // Use in-memory fallback
    const userSessions = userSessionMap.get(userId);
    if (userSessions) {
      sessionIds = Array.from(userSessions);
    }
  }
  
  if (sessionIds.length >= MAX_CONCURRENT_SESSIONS) {
    // Get session details
    const sessions: Array<{ id: string; createdAt: Date }> = [];
    
    for (const sessionId of sessionIds) {
      const sessionData = await getSession(sessionId);
      if (sessionData) {
        sessions.push({
          id: sessionId,
          createdAt: sessionData.createdAt
        });
      }
    }
    
    // Sort by creation time and remove oldest
    sessions.sort((a, b) => {
      const dateA = new Date(a.createdAt);
      const dateB = new Date(b.createdAt);
      return dateA.getTime() - dateB.getTime();
    });
    const toRemove = sessions.slice(0, sessions.length - MAX_CONCURRENT_SESSIONS + 1);
    
    for (const session of toRemove) {
      await destroySession(session.id);
    }
  }
}

// Extract session ID from request
function extractSessionId(req: Request): string | null {
  // Check Authorization header
  const authHeader = req.headers.authorization;
  if (authHeader && authHeader.startsWith('Session ')) {
    return authHeader.substring(8);
  }
  
  // Check cookie
  if (req.cookies?.sessionId) {
    return req.cookies.sessionId;
  }
  
  // Check custom header
  const sessionHeader = req.headers['x-session-id'];
  if (sessionHeader && typeof sessionHeader === 'string') {
    return sessionHeader;
  }
  
  return null;
}

// Get client IP
function getClientIp(req: Request): string {
  const forwarded = req.headers['x-forwarded-for'];
  if (forwarded) {
    return (forwarded as string).split(',')[0].trim();
  }
  return req.socket.remoteAddress || 'unknown';
}

// Session timeout handler
export function sessionTimeoutHandler(
  idleTimeout: number = 1800000 // 30 minutes
) {
  return async (req: Request, res: Response, next: NextFunction) => {
    if (req.session) {
      const lastActivity = new Date(req.session.lastActivityAt).getTime();
      const now = Date.now();
      
      if (now - lastActivity > idleTimeout) {
        await destroySession(req.sessionId!);
        return next(new AppError(401, 'Session timed out due to inactivity', true, 'SESSION_TIMEOUT'));
      }
    }
    
    next();
  };
}