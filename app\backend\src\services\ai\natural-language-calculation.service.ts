import { GoogleGenAI } from '@google/genai';
import { LoadCalculationService } from '../calculations/load-calculation';
import { WireSizeCalculationService } from '../calculations/wire-size';
import { VoltageDropCalculationService } from '../calculations/voltage-drop';
import { ConduitFillCalculationService } from '../calculations/conduit-fill';
import { ShortCircuitService } from '../calculations/short-circuit';
import { ArcFlashService } from '../calculations/arc-flash';

interface CalculationIntent {
  calculationType: string;
  parameters: Record<string, any>;
  missingParameters: string[];
  confidence: number;
}

interface NaturalLanguageResult {
  success: boolean;
  calculationType: string;
  result: any;
  natural_language_summary: string;
  original_query: string;
  necReferences: string[];
  suggestions?: string[];
}

export class NaturalLanguageCalculationService {
  private genAI: GoogleGenAI;
  private model: any;

  constructor() {
    if (!process.env.GEMINI_API_KEY) {
      throw new Error('GEMINI_API_KEY environment variable is required');
    }
    
    this.genAI = new GoogleGenAI({ apiKey: process.env.GEMINI_API_KEY });
    this.model = this.genAI.models;
  }

  async processCalculationRequest(
    query: string, 
    context?: any
  ): Promise<NaturalLanguageResult> {
    try {
      // Extract calculation intent from natural language
      const intent = await this.extractIntent(query, context);
      
      if (intent.missingParameters.length > 0) {
        return {
          success: false,
          calculationType: intent.calculationType,
          result: null,
          natural_language_summary: `I need more information to perform this calculation. Please provide: ${intent.missingParameters.join(', ')}`,
          original_query: query,
          necReferences: [],
          suggestions: this.generateParameterSuggestions(intent)
        };
      }

      // Route to appropriate calculation service
      const result = await this.performCalculation(intent, query);
      
      // Generate human-readable explanation
      const explanation = await this.generateExplanation(result, intent.calculationType);
      
      return {
        success: true,
        calculationType: intent.calculationType,
        result: result,
        natural_language_summary: explanation,
        original_query: query,
        necReferences: result.necReferences || [],
      };
    } catch (error) {
      console.error('Natural language calculation error:', error);
      return {
        success: false,
        calculationType: 'UNKNOWN',
        result: null,
        natural_language_summary: 'I encountered an error processing your calculation request. Please try rephrasing or check the format.',
        original_query: query,
        necReferences: []
      };
    }
  }

  private async extractIntent(query: string, context?: any): Promise<CalculationIntent> {
    const intentPrompt = `
    You are an expert electrical engineer. Analyze this calculation request and extract the intent.
    
    Request: "${query}"
    Context: ${JSON.stringify(context || {})}
    
    Identify:
    1. Type of calculation needed (one of: LOAD_CALCULATION, WIRE_SIZE, VOLTAGE_DROP, CONDUIT_FILL, SHORT_CIRCUIT, ARC_FLASH)
    2. Parameters provided in the request
    3. Any missing required parameters
    
    Common patterns:
    - "What size wire for X amps" -> WIRE_SIZE calculation
    - "Voltage drop for X feet" -> VOLTAGE_DROP calculation
    - "Load calculation for X sq ft house" -> LOAD_CALCULATION
    - "How many wires in X conduit" -> CONDUIT_FILL
    - "Fault current at panel" -> SHORT_CIRCUIT
    - "Arc flash for X kA" -> ARC_FLASH
    
    Return a JSON object with:
    {
      "calculationType": "TYPE",
      "parameters": { extracted parameters },
      "missingParameters": [ list of missing required params ],
      "confidence": 0.0-1.0
    }
    `;

    const response = await this.model.generateContent({
      model: 'gemini-2.0-flash-001',
      contents: intentPrompt,
      generationConfig: {
        responseFormat: 'json'
      }
    });

    return JSON.parse(response.text);
  }

  private async performCalculation(intent: CalculationIntent, query: string): Promise<any> {
    switch (intent.calculationType) {
      case 'LOAD_CALCULATION':
        return await this.performLoadCalculation(intent.parameters);
      
      case 'WIRE_SIZE':
        return await this.performWireSizeCalculation(intent.parameters);
      
      case 'VOLTAGE_DROP':
        return await this.performVoltageDropCalculation(intent.parameters);
      
      case 'CONDUIT_FILL':
        return await this.performConduitFillCalculation(intent.parameters);
      
      case 'SHORT_CIRCUIT':
        return await this.performShortCircuitCalculation(intent.parameters);
      
      case 'ARC_FLASH':
        return await this.performArcFlashCalculation(intent.parameters);
      
      default:
        throw new Error(`Unknown calculation type: ${intent.calculationType}`);
    }
  }

  private async performLoadCalculation(params: any): Promise<any> {
    // Apply intelligent defaults if needed
    const enhancedParams = {
      dwelling_unit_area: params.square_footage || params.area || 2000,
      general_lighting_demand_factor: params.demand_factor || 1.0,
      small_appliance_circuits: params.small_appliance || 2,
      laundry_circuits: params.laundry || 1,
      fastened_appliances: params.appliances || [],
      kitchen_equipment_demand: params.kitchen_demand || 8000,
      hvac_heating: params.heating || 0,
      hvac_cooling: params.cooling || 0,
      largest_motor_hp: params.motor || 0
    };

    const service = new LoadCalculationService();
    return await service.calculate(enhancedParams);
  }

  private async performWireSizeCalculation(params: any): Promise<any> {
    const service = new WireSizeCalculationService();
    return await service.calculate({
      current: params.amps || params.current || params.amperage,
      voltage: params.voltage || 240,
      phase: params.phase || 'single',
      power_factor: params.power_factor || 0.8,
      conductor_material: params.material || 'COPPER',
      insulation_type: params.insulation || 'THHN',
      ambient_temperature: params.ambient_temp || 30,
      conductors_in_raceway: params.conductors || 3,
      length: params.length || 100,
      voltage_drop_limit: params.vd_limit || 3
    });
  }

  private async performVoltageDropCalculation(params: any): Promise<any> {
    const service = new VoltageDropCalculationService();
    return await service.calculate({
      voltage: params.voltage || 120,
      phase: params.phase || 'single',
      current: params.current || params.amps,
      power_factor: params.power_factor || 0.85,
      length: params.length || params.distance,
      conductor_material: params.material || 'COPPER',
      conductor_size: params.wire_size || params.awg || '12',
      conduit_type: params.conduit || 'PVC',
      conductors_per_phase: params.parallel || 1
    });
  }

  private async performConduitFillCalculation(params: any): Promise<any> {
    const service = new ConduitFillCalculationService();
    return await service.calculate({
      conduit_type: params.conduit_type || 'EMT',
      conduit_size: params.conduit_size || '1',
      conductors: params.conductors || []
    });
  }

  private async performShortCircuitCalculation(params: any): Promise<any> {
    const service = new ShortCircuitService();
    return await service.calculate(params.panel_id, {
      utilityVoltage: params.voltage || 480,
      utilityFaultCurrent: params.utility_fault || 50000,
      utilityXRRatio: params.xr_ratio || 15,
      transformerKva: params.transformer_kva,
      transformerImpedance: params.transformer_impedance,
      conductorLength: params.length || 50,
      conductorSize: params.conductor || '500',
      conductorMaterial: params.material || 'COPPER',
      conductorType: params.conductor_type || 'THHN',
      conduitType: params.conduit || 'STEEL'
    });
  }

  private async performArcFlashCalculation(params: any): Promise<any> {
    const service = new ArcFlashService();
    return await service.calculate(params.panel_id, {
      systemVoltage: params.voltage || 480,
      availableFaultCurrent: params.fault_current || 30000,
      faultClearingTime: params.clearing_time || 0.2,
      workingDistance: params.working_distance || 18,
      equipmentType: params.equipment || 'PANELBOARD',
      electrodeConfiguration: params.configuration || 'VCB',
      enclosureWidth: params.width || 20,
      enclosureHeight: params.height || 20,
      enclosureDepth: params.depth || 8
    });
  }

  private async generateExplanation(result: any, calculationType: string): Promise<string> {
    const explanationPrompt = `
    Generate a clear, professional explanation of this electrical calculation result for a contractor.
    
    Calculation Type: ${calculationType}
    Result: ${JSON.stringify(result, null, 2)}
    
    Include:
    1. Summary of what was calculated
    2. Key results in plain language
    3. Important NEC references
    4. Any safety considerations
    5. Practical recommendations
    
    Keep it concise but informative. Use electrical trade terminology appropriately.
    `;

    const response = await this.model.generateContent({
      model: 'gemini-2.0-flash-001',
      contents: explanationPrompt
    });

    return response.text;
  }

  private generateParameterSuggestions(intent: CalculationIntent): string[] {
    const suggestions: Record<string, string[]> = {
      LOAD_CALCULATION: [
        "Try: 'Calculate load for a 2500 sq ft house with 2 AC units'",
        "Or: 'What's the service size for 3000 sq ft commercial space'"
      ],
      WIRE_SIZE: [
        "Try: 'What size wire for 50 amps at 120 feet'",
        "Or: 'Wire size for 30A circuit with 3% voltage drop'"
      ],
      VOLTAGE_DROP: [
        "Try: 'Voltage drop for #12 wire, 100 feet, 20 amps'",
        "Or: 'Calculate VD for 200A feeder, 250 feet, aluminum'"
      ],
      CONDUIT_FILL: [
        "Try: 'How many #12 THHN in 3/4 EMT'",
        "Or: 'Can I fit 6 #10 wires in 1 inch PVC'"
      ],
      SHORT_CIRCUIT: [
        "Try: 'Fault current at panel fed from 1500kVA transformer'",
        "Or: 'Short circuit with 50kA available, 100 feet of 500kcmil'"
      ],
      ARC_FLASH: [
        "Try: 'Arc flash for 480V panel with 30kA fault current'",
        "Or: 'PPE category for 208V switchboard, 0.5 second clearing'"
      ]
    };

    return suggestions[intent.calculationType] || [
      "Please provide more details about your calculation needs"
    ];
  }
}