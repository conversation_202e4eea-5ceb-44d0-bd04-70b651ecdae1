import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Panel, PanelLoadCalculation } from '@electrical/shared';
import { panelService } from '../../services/panelService';
import { useAuthStore } from '../../stores/auth';
import { 
  PlusIcon, 
  BoltIcon, 
  ExclamationCircleIcon,
  DocumentTextIcon,
  ArrowPathIcon,
  FireIcon 
} from '@heroicons/react/24/outline';

interface PanelWithLoad extends Panel {
  latest_load_calculation?: PanelLoadCalculation | null;
  feeding_panels?: Panel[];
  fed_from_panel?: Panel | null;
}

export const PanelList: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const navigate = useNavigate();
  const { accessToken: token } = useAuthStore();
  const [panels, setPanels] = useState<PanelWithLoad[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Debug - ensure component is rendering
  if (!projectId) {
    return <div>No project ID provided</div>;
  }


  useEffect(() => {
    if (projectId) {
      loadPanels();
    }
  }, [projectId, token]);

  const loadPanels = async () => {
    if (!projectId) {
      setLoading(false);
      return;
    }
    
    if (!token) {
      setLoading(false);
      setError('Authentication required');
      return;
    }
    
    try {
      setLoading(true);
      const data = await panelService.getPanelsByProject(projectId);
      setPanels(Array.isArray(data) ? data : []);
      setError(null);
    } catch (err) {
      setError('Failed to load panels');
      console.error('Error loading panels:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleCalculateLoad = async (panelId: string) => {
    if (!token) return;
    
    try {
      await panelService.calculatePanelLoad(panelId);
      await loadPanels(); // Reload to get updated calculations
    } catch (err) {
      console.error('Error calculating load:', err);
    }
  };

  const getLoadStatusColor = (loadPercentage: number) => {
    if (loadPercentage >= 95) return 'text-red-600 bg-red-50';
    if (loadPercentage >= 80) return 'text-amber-600 bg-amber-50';
    if (loadPercentage >= 60) return 'text-yellow-600 bg-yellow-50';
    return 'text-green-600 bg-green-50';
  };

  const getImbalanceStatus = (imbalancePercent: number) => {
    if (imbalancePercent > 10) {
      return { color: 'text-red-600', icon: ExclamationCircleIcon, text: 'High Imbalance' };
    }
    if (imbalancePercent > 5) {
      return { color: 'text-amber-600', icon: ExclamationCircleIcon, text: 'Moderate Imbalance' };
    }
    return { color: 'text-green-600', icon: BoltIcon, text: 'Balanced' };
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-lg bg-red-50 p-4 text-red-800">
        <p className="font-medium">Error loading panels</p>
        <p className="text-sm mt-1">{error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
          Panel Schedules
        </h2>
        <div className="flex space-x-3">
          <button
            onClick={() => navigate(`/projects/${projectId}/arc-flash`)}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
          >
            <FireIcon className="h-5 w-5 mr-2" />
            Arc Flash Analysis
          </button>
          <button
            onClick={() => navigate(`/projects/${projectId}/panels/new`)}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <PlusIcon className="h-5 w-5 mr-2" />
            Add Panel
          </button>
        </div>
      </div>

      {panels.length === 0 ? (
        <div className="text-center py-12 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
          <BoltIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
            No panels found
          </h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Get started by creating a new panel for this project.
          </p>
          <div className="mt-6">
            <button
              onClick={() => navigate(`/projects/${projectId}/panels/new`)}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
            >
              <PlusIcon className="h-5 w-5 mr-2" />
              Create Panel
            </button>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          {panels.map((panel) => {
            const loadCalc = panel.latest_load_calculation;
            const imbalanceStatus = loadCalc 
              ? getImbalanceStatus(loadCalc.phase_imbalance_percent)
              : null;

            return (
              <div
                key={panel.id}
                className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow cursor-pointer"
                onClick={() => navigate(`/projects/${projectId}/panels/${panel.id}`)}
              >
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                        {panel.name}
                      </h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {panel.location}
                      </p>
                    </div>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      panel.panel_type === 'MAIN' 
                        ? 'bg-purple-100 text-purple-800' 
                        : panel.panel_type === 'SUB'
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {panel.panel_type}
                    </span>
                  </div>

                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                        Rating
                      </p>
                      <p className="mt-1 text-sm text-gray-900 dark:text-white">
                        {panel.ampere_rating}A / {panel.voltage_system}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                        Spaces
                      </p>
                      <p className="mt-1 text-sm text-gray-900 dark:text-white">
                        {panel.spaces_used || 0} / {panel.spaces_total}
                      </p>
                    </div>
                  </div>

                  {loadCalc && (
                    <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                      <div className="flex items-center justify-between mb-2">
                        <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                          Load Summary
                        </p>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleCalculateLoad(panel.id!);
                          }}
                          className="text-blue-600 hover:text-blue-700 p-1"
                          title="Recalculate load"
                        >
                          <ArrowPathIcon className="h-4 w-4" />
                        </button>
                      </div>
                      
                      <div className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-600 dark:text-gray-400">
                            Total Load
                          </span>
                          <span className={`text-sm font-medium px-2 py-1 rounded ${
                            getLoadStatusColor(loadCalc.load_percentage)
                          }`}>
                            {loadCalc.load_percentage.toFixed(1)}%
                          </span>
                        </div>
                        
                        {panel.phase_config !== 'SINGLE_PHASE' && imbalanceStatus && (
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-600 dark:text-gray-400">
                              Phase Balance
                            </span>
                            <div className="flex items-center">
                              <imbalanceStatus.icon 
                                className={`h-4 w-4 mr-1 ${imbalanceStatus.color}`} 
                              />
                              <span className={`text-sm ${imbalanceStatus.color}`}>
                                {imbalanceStatus.text}
                              </span>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {panel.fed_from_panel && (
                    <div className="mt-4 text-sm text-gray-500 dark:text-gray-400">
                      Fed from: {panel.fed_from_panel.name} 
                      {panel.fed_from_circuit && ` (Circuit ${panel.fed_from_circuit})`}
                    </div>
                  )}

                  <div className="mt-4 flex space-x-3">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        navigate(`/projects/${projectId}/panels/${panel.id}/schedule`);
                      }}
                      className="inline-flex items-center text-sm text-blue-600 hover:text-blue-700"
                    >
                      <DocumentTextIcon className="h-4 w-4 mr-1" />
                      View Schedule
                    </button>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};