import React, { useState, useRef, useEffect } from 'react';
import { Button } from '../ui/button';
import { Card } from '../ui/card';
import { Alert, AlertDescription } from '../ui/alert';
import { Sparkles, Send, X, Copy, Check } from 'lucide-react';
import { api } from '../../services/api';

interface AIResponse {
  success: boolean;
  calculationType: string;
  result: any;
  natural_language_summary: string;
  original_query: string;
  necReferences: string[];
  suggestions?: string[];
}

export const AICalculationAssistant: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [query, setQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [response, setResponse] = useState<AIResponse | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [copied, setCopied] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    if (isOpen && textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [isOpen]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!query.trim() || loading) return;

    setLoading(true);
    setError(null);
    setResponse(null);

    try {
      const result = await api.post('/ai/calculate', {
        query: query.trim(),
        context: {
          // Add any available context here
          location: 'USA',
        }
      });

      setResponse(result.data.data);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to process calculation request');
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = () => {
    if (response?.result) {
      navigator.clipboard.writeText(JSON.stringify(response.result, null, 2));
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  const exampleQueries = [
    "What size wire do I need for a 50 amp circuit running 100 feet?",
    "Calculate the load for a 2500 sq ft house with electric heat",
    "Voltage drop for #10 AWG copper wire, 150 feet, 30 amps",
    "How many #12 THHN wires can fit in a 3/4 inch EMT conduit?",
    "Arc flash calculation for 480V panel with 30kA fault current"
  ];

  return (
    <>
      {/* Floating button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="fixed bottom-6 right-6 z-50 flex items-center gap-2 bg-blue-600 text-white px-4 py-3 rounded-full shadow-lg hover:bg-blue-700 transition-all hover:scale-105"
        aria-label="Open AI Calculation Assistant"
      >
        <Sparkles className="h-5 w-5" />
        <span className="font-medium">AI Assistant</span>
      </button>

      {/* Assistant panel */}
      {isOpen && (
        <div className="fixed bottom-24 right-6 z-50 w-[420px] max-h-[600px] animate-in slide-in-from-bottom-5">
          <Card className="shadow-2xl border-gray-200">
            <div className="flex items-center justify-between p-4 border-b">
              <div className="flex items-center gap-2">
                <Sparkles className="h-5 w-5 text-blue-600" />
                <h3 className="font-semibold text-lg">AI Calculation Assistant</h3>
              </div>
              <button
                onClick={() => setIsOpen(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <div className="p-4 space-y-4 max-h-[500px] overflow-y-auto">
              {/* Examples */}
              {!response && !loading && (
                <div className="space-y-2">
                  <p className="text-sm text-gray-600">Try asking me about:</p>
                  <div className="space-y-1">
                    {exampleQueries.map((example, index) => (
                      <button
                        key={index}
                        onClick={() => setQuery(example)}
                        className="text-left w-full text-sm text-blue-600 hover:text-blue-700 hover:bg-blue-50 p-2 rounded transition-colors"
                      >
                        "{example}"
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Response */}
              {response && (
                <div className="space-y-4">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="flex items-start justify-between mb-2">
                      <h4 className="font-medium text-gray-900">Result</h4>
                      <button
                        onClick={copyToClipboard}
                        className="text-gray-500 hover:text-gray-700"
                        title="Copy result"
                      >
                        {copied ? (
                          <Check className="h-4 w-4 text-green-600" />
                        ) : (
                          <Copy className="h-4 w-4" />
                        )}
                      </button>
                    </div>
                    <p className="text-sm text-gray-700 whitespace-pre-wrap">
                      {response.natural_language_summary}
                    </p>
                  </div>

                  {/* NEC References */}
                  {response.necReferences && response.necReferences.length > 0 && (
                    <div>
                      <h5 className="font-medium text-sm mb-1">NEC References:</h5>
                      <div className="flex flex-wrap gap-1">
                        {response.necReferences.map((ref, index) => (
                          <span
                            key={index}
                            className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded"
                          >
                            {ref}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Technical Details */}
                  {response.result && (
                    <details className="text-sm">
                      <summary className="cursor-pointer text-gray-600 hover:text-gray-800">
                        View technical details
                      </summary>
                      <pre className="mt-2 p-3 bg-gray-100 rounded text-xs overflow-x-auto">
                        {JSON.stringify(response.result, null, 2)}
                      </pre>
                    </details>
                  )}

                  {/* Suggestions */}
                  {response.suggestions && response.suggestions.length > 0 && (
                    <Alert>
                      <AlertDescription>
                        <p className="font-medium mb-1">Suggestions:</p>
                        <ul className="list-disc list-inside space-y-1">
                          {response.suggestions.map((suggestion, index) => (
                            <li key={index} className="text-sm">{suggestion}</li>
                          ))}
                        </ul>
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              )}

              {/* Error */}
              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {/* Loading */}
              {loading && (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
              )}

              {/* Input form */}
              <form onSubmit={handleSubmit} className="mt-4">
                <div className="flex gap-2">
                  <textarea
                    ref={textareaRef}
                    value={query}
                    onChange={(e) => setQuery(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        handleSubmit(e);
                      }
                    }}
                    placeholder="Ask me anything about electrical calculations..."
                    className="flex-1 min-h-[80px] p-3 border rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
                    disabled={loading}
                  />
                  <button
                    type="submit"
                    disabled={!query.trim() || loading}
                    className="self-end px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    <Send className="h-5 w-5" />
                  </button>
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  Press Enter to send, Shift+Enter for new line
                </p>
              </form>
            </div>
          </Card>
        </div>
      )}
    </>
  );
};