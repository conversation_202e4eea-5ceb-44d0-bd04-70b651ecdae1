import { Router } from 'express';
import { z } from 'zod';
import { authenticate, AuthRequest } from '../middleware/auth';
import { validate } from '../middleware/validate';
import { QuoteService } from '../services/quote.service';
import { AIQuoteService } from '../services/ai-quote.service';
import { AppError } from '../utils/errors';
import { logger } from '../utils/logger';

const router = Router();
const quoteService = new QuoteService();
const aiQuoteService = new AIQuoteService();

// Validation schemas
const quoteItemSchema = z.object({
  name: z.string(),
  description: z.string().optional(),
  category: z.string().optional(),
  quantity: z.number().positive(),
  unit: z.string(),
  unitPrice: z.number().min(0).optional(),
  totalPrice: z.number().min(0).optional(),
  cost: z.number().min(0).optional(),
  margin: z.number().min(0).max(100).optional(),
  materialId: z.string().optional(),
  sku: z.string().optional(),
  manufacturer: z.string().optional(),
  imageUrl: z.string().url().optional(),
  sourceUrl: z.string().url().optional(),
  lookupStatus: z.enum(['pending', 'searching', 'options_available', 'confirmed', 'failed', 'manual']).optional(),
  lookupResults: z.array(z.any()).optional(),
  priceHistory: z.array(z.any()).optional(),
  notes: z.string().optional(),
  isOptional: z.boolean().optional(),
  groupId: z.string().optional()
});

const createQuoteSchema = z.object({
  name: z.string(),
  customerId: z.string().uuid().nullish(),
  projectId: z.string().uuid().nullish(),
  projectOverview: z.string().optional(),
  scopeOfWork: z.string().optional(),
  materialsIncluded: z.string().optional(),
  exclusions: z.string().optional(),
  termsAndConditions: z.string().optional(),
  items: z.array(quoteItemSchema).optional(),
  taxRate: z.number().min(0).max(1).optional(),
  discount: z.number().min(0).optional(),
  discountType: z.enum(['PERCENTAGE', 'FIXED']).optional(),
  expiresAt: z.string().datetime().optional(),
  customerNotes: z.string().optional(),
  internalNotes: z.string().optional()
});

const updateQuoteSchema = createQuoteSchema.partial();

const aiGenerateQuoteSchema = z.object({
  inputType: z.enum(['text', 'image', 'mixed']),
  inputData: z.string(),
  customerId: z.string().uuid().nullish(),
  projectId: z.string().uuid().nullish(),
  includeImages: z.boolean().optional()
});

const answerQuestionsSchema = z.object({
  answers: z.record(z.string())
});

const selectMaterialSchema = z.object({
  itemId: z.string(),
  selectedOption: z.object({
    title: z.string(),
    price: z.number(),
    url: z.string().optional(),
    sku: z.string().optional(),
    imageUrl: z.string().optional(),
    source: z.string()
  })
});

// List quotes with filters
router.get('/', authenticate, async (req: AuthRequest, res, next) => {
  try {
    const { 
      status, 
      customerId, 
      projectId, 
      search, 
      startDate, 
      endDate,
      page = '1',
      limit = '20',
      sortBy = 'created_at',
      sortOrder = 'desc'
    } = req.query;

    const filters = {
      status: status as string,
      customerId: customerId as string,
      projectId: projectId as string,
      search: search as string,
      startDate: startDate as string,
      endDate: endDate as string,
      companyId: req.user!.companyId
    };

    const pagination = {
      page: parseInt(page as string),
      limit: parseInt(limit as string),
      sortBy: sortBy as string,
      sortOrder: sortOrder as 'asc' | 'desc'
    };

    const result = await quoteService.listQuotes(filters, pagination);
    res.json(result);
  } catch (error) {
    next(error);
  }
});

// Get quote by ID
router.get('/:id', authenticate, async (req: AuthRequest, res, next) => {
  try {
    const quote = await quoteService.getQuoteById(req.params.id, req.user!.companyId);
    res.json(quote);
  } catch (error) {
    next(error);
  }
});

// Create new quote
router.post('/', authenticate, validate(createQuoteSchema), async (req: AuthRequest, res, next) => {
  try {
    const quoteData = {
      ...req.body,
      companyId: req.user!.companyId,
      createdById: req.user!.userId
    };
    
    const quote = await quoteService.createQuote(quoteData);
    res.status(201).json(quote);
  } catch (error) {
    next(error);
  }
});

// Update quote
router.put('/:id', authenticate, validate(updateQuoteSchema), async (req: AuthRequest, res, next) => {
  try {
    const updateData = {
      ...req.body,
      updatedById: req.user!.userId
    };
    
    const quote = await quoteService.updateQuote(
      req.params.id, 
      updateData, 
      req.user!.companyId
    );
    res.json(quote);
  } catch (error) {
    next(error);
  }
});

// Delete quote
router.delete('/:id', authenticate, async (req: AuthRequest, res, next) => {
  try {
    await quoteService.deleteQuote(req.params.id, req.user!.companyId);
    res.status(204).send();
  } catch (error) {
    next(error);
  }
});

// Duplicate quote
router.post('/:id/duplicate', authenticate, async (req: AuthRequest, res, next) => {
  try {
    const { name } = req.body;
    const quote = await quoteService.duplicateQuote(
      req.params.id, 
      name,
      req.user!.userId,
      req.user!.companyId
    );
    res.status(201).json(quote);
  } catch (error) {
    next(error);
  }
});

// Convert quote to job
router.post('/:id/convert-to-job', authenticate, async (req: AuthRequest, res, next) => {
  try {
    const { jobName, scheduledStart, scheduledEnd, assignedTo } = req.body;
    
    const job = await quoteService.convertToJob(
      req.params.id,
      {
        name: jobName,
        scheduledStart,
        scheduledEnd,
        assignedTo
      },
      req.user!.companyId
    );
    res.json(job);
  } catch (error) {
    next(error);
  }
});

// Convert quote to estimate
router.post('/:id/convert-to-estimate', authenticate, async (req: AuthRequest, res, next) => {
  try {
    const estimate = await quoteService.convertToEstimate(
      req.params.id,
      req.user!.userId,
      req.user!.companyId
    );
    res.json(estimate);
  } catch (error) {
    next(error);
  }
});

// AI Quote Generation
router.post('/generate-ai', authenticate, validate(aiGenerateQuoteSchema), async (req: AuthRequest, res, next) => {
  try {
    const { inputType, inputData, customerId, projectId, includeImages } = req.body;
    
    const result = await aiQuoteService.generateQuote({
      inputType,
      inputData,
      customerId,
      projectId,
      includeImages,
      userId: req.user!.userId,
      companyId: req.user!.companyId
    });
    
    res.json(result);
  } catch (error) {
    next(error);
  }
});

// Answer AI clarification questions
router.post('/:id/ai-questions', authenticate, validate(answerQuestionsSchema), async (req: AuthRequest, res, next) => {
  try {
    const { answers } = req.body;
    
    const result = await aiQuoteService.answerQuestions(
      req.params.id,
      answers,
      req.user!.companyId
    );
    
    res.json(result);
  } catch (error) {
    next(error);
  }
});

// Get AI suggestions for quote
router.post('/:id/ai-suggest', authenticate, async (req: AuthRequest, res, next) => {
  try {
    const { type } = req.body; // 'materials', 'pricing', 'alternatives'
    
    const suggestions = await aiQuoteService.getSuggestions(
      req.params.id,
      type,
      req.user!.companyId
    );
    
    res.json(suggestions);
  } catch (error) {
    next(error);
  }
});

// Material Price Management
router.get('/:id/prices', authenticate, async (req: AuthRequest, res, next) => {
  try {
    const priceStatus = await quoteService.getPriceStatus(
      req.params.id,
      req.user!.companyId
    );
    res.json(priceStatus);
  } catch (error) {
    next(error);
  }
});

// Refresh all material prices
router.post('/:id/prices/refresh', authenticate, async (req: AuthRequest, res, next) => {
  try {
    const { force = false } = req.body;
    
    const result = await quoteService.refreshPrices(
      req.params.id,
      force,
      req.user!.companyId
    );
    
    res.json(result);
  } catch (error) {
    next(error);
  }
});

// Update single item price
router.post('/:id/items/:itemId/price', authenticate, validate(selectMaterialSchema), async (req: AuthRequest, res, next) => {
  try {
    const { selectedOption } = req.body;
    
    const result = await quoteService.updateItemPrice(
      req.params.id,
      req.params.itemId,
      selectedOption,
      req.user!.companyId
    );
    
    res.json(result);
  } catch (error) {
    next(error);
  }
});

// Search materials
router.get('/materials/search', authenticate, async (req: AuthRequest, res, next) => {
  try {
    const { query, source, limit = '10' } = req.query;
    
    if (!query) {
      throw new AppError('Query parameter is required', 400);
    }
    
    const results = await quoteService.searchMaterials(
      query as string,
      {
        source: source as string,
        limit: parseInt(limit as string),
        companyId: req.user!.companyId
      }
    );
    
    res.json(results);
  } catch (error) {
    next(error);
  }
});

// Get material price history
router.get('/materials/price-history', authenticate, async (req: AuthRequest, res, next) => {
  try {
    const { materialId, sku, days = '30' } = req.query;
    
    const history = await quoteService.getPriceHistory({
      materialId: materialId as string,
      sku: sku as string,
      days: parseInt(days as string),
      companyId: req.user!.companyId
    });
    
    res.json(history);
  } catch (error) {
    next(error);
  }
});

// Quote Actions
router.post('/:id/send', authenticate, async (req: AuthRequest, res, next) => {
  try {
    const { to, cc, subject, message } = req.body;
    
    const result = await quoteService.sendQuote(
      req.params.id,
      {
        to,
        cc,
        subject,
        message
      },
      req.user!.companyId
    );
    
    res.json(result);
  } catch (error) {
    next(error);
  }
});

// Generate PDF
router.get('/:id/pdf', authenticate, async (req: AuthRequest, res, next) => {
  try {
    const pdfBuffer = await quoteService.generatePDF(
      req.params.id,
      req.user!.companyId
    );
    
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="quote-${req.params.id}.pdf"`);
    res.send(pdfBuffer);
  } catch (error) {
    next(error);
  }
});

// Customer approval
router.post('/:id/approve', async (req, res, next) => {
  try {
    const { token, signature } = req.body;
    
    const result = await quoteService.approveQuote(
      req.params.id,
      token,
      signature
    );
    
    res.json(result);
  } catch (error) {
    next(error);
  }
});

// Customer rejection
router.post('/:id/reject', async (req, res, next) => {
  try {
    const { token, reason } = req.body;
    
    const result = await quoteService.rejectQuote(
      req.params.id,
      token,
      reason
    );
    
    res.json(result);
  } catch (error) {
    next(error);
  }
});

// Public quote view (no auth required)
router.get('/public/:token', async (req, res, next) => {
  try {
    const quote = await quoteService.getPublicQuote(req.params.token);
    res.json(quote);
  } catch (error) {
    next(error);
  }
});

export default router;