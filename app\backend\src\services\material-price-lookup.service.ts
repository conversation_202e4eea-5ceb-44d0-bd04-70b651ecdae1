import { Queue, Worker } from 'bullmq';
import { prisma } from '../database/prisma';
import { redis } from '../services/redis-manager';
import { logger } from '../utils/logger';
import { v4 as uuidv4 } from 'uuid';
import { HomeDepotScraper } from '../scrapers/home-depot.scraper';
import { LowesScraper } from '../scrapers/lowes.scraper';
import { GoogleShoppingScraper } from '../scrapers/google-shopping.scraper';

interface MaterialSearchOptions {
  source?: string;
  limit?: number;
  companyId?: string;
  useCache?: boolean;
}

interface PriceLookupJob {
  quoteId: string;
  itemIndex: number;
  item: any;
  companyId: string;
  userId?: string;
}

export class MaterialPriceLookupService {
  private lookupQueue: Queue | null = null;
  private scrapers: Map<string, any>;
  private worker: Worker | null = null;

  constructor() {
    this.scrapers = new Map();
    this.initializeScrapers();
    this.initializeQueue();
  }

  private initializeScrapers() {
    this.scrapers.set('HOME_DEPOT', new HomeDepotScraper());
    this.scrapers.set('LOWES', new LowesScraper());
    this.scrapers.set('GOOGLE_SHOPPING', new GoogleShoppingScraper());
  }

  private initializeQueue() {
    if (!redis) {
      logger.warn('Redis not available, price lookups will be synchronous');
      return;
    }

    // Create queue
    this.lookupQueue = new Queue('material-price-lookup', {
      connection: redis,
      defaultJobOptions: {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
        removeOnComplete: {
          age: 24 * 3600, // Keep completed jobs for 24 hours
          count: 100,      // Keep last 100 completed jobs
        },
        removeOnFail: {
          age: 7 * 24 * 3600, // Keep failed jobs for 7 days
        },
      },
    });

    // Create worker
    this.worker = new Worker(
      'material-price-lookup',
      async (job) => {
        await this.processLookupJob(job.data);
      },
      {
        connection: redis,
        concurrency: 5, // Process up to 5 jobs concurrently
      }
    );

    this.worker.on('completed', (job) => {
      logger.info(`Price lookup completed for job ${job.id}`);
    });

    this.worker.on('failed', (job, err) => {
      logger.error(`Price lookup failed for job ${job?.id}:`, err);
    });
  }

  async initiateBulkLookup(quoteId: string, items: any[], companyId: string) {
    const jobs = items.map((item, index) => ({
      quoteId,
      itemIndex: index,
      item,
      companyId,
    }));

    if (this.lookupQueue) {
      // Add jobs to queue
      const bulkJobs = jobs.map(job => ({
        name: 'lookup-price',
        data: job,
        opts: {
          priority: item.priority || 0,
        },
      }));

      await this.lookupQueue.addBulk(bulkJobs);
      logger.info(`Added ${jobs.length} price lookup jobs to queue for quote ${quoteId}`);
    } else {
      // Process synchronously if no queue
      for (const job of jobs) {
        try {
          await this.processLookupJob(job);
        } catch (error) {
          logger.error('Synchronous price lookup failed:', error);
        }
      }
    }
  }

  async searchMaterials(query: string, options: MaterialSearchOptions = {}) {
    const cacheKey = `material:search:${query}:${options.source || 'all'}:${options.limit || 10}`;
    
    // Check cache if enabled
    if (options.useCache !== false && redis) {
      try {
        const cached = await redis.get(cacheKey);
        if (cached) {
          logger.debug(`Cache hit for material search: ${query}`);
          return JSON.parse(cached);
        }
      } catch (error) {
        logger.error('Cache retrieval error:', error);
      }
    }

    // Determine which scrapers to use
    const sources = options.source ? [options.source] : ['HOME_DEPOT', 'LOWES'];
    const results: any[] = [];

    // Search each source
    for (const source of sources) {
      const scraper = this.scrapers.get(source);
      if (!scraper) {
        logger.warn(`Scraper not found for source: ${source}`);
        continue;
      }

      try {
        const sourceResults = await scraper.search(query, {
          limit: options.limit || 10,
        });

        results.push(...sourceResults.map((result: any) => ({
          ...result,
          source,
        })));
      } catch (error) {
        logger.error(`Search failed for ${source}:`, error);
      }
    }

    // Sort by relevance/price
    results.sort((a, b) => {
      // Prioritize exact matches
      const aExact = a.title.toLowerCase().includes(query.toLowerCase());
      const bExact = b.title.toLowerCase().includes(query.toLowerCase());
      if (aExact && !bExact) return -1;
      if (!aExact && bExact) return 1;
      
      // Then sort by price
      return (a.price || 0) - (b.price || 0);
    });

    // Limit results
    const limitedResults = results.slice(0, options.limit || 10);

    // Cache results
    if (redis && limitedResults.length > 0) {
      try {
        await redis.setex(cacheKey, 3600, JSON.stringify(limitedResults)); // Cache for 1 hour
      } catch (error) {
        logger.error('Cache storage error:', error);
      }
    }

    // Store in price history if company provided
    if (options.companyId && limitedResults.length > 0) {
      await this.storePriceHistory(limitedResults, query, options.companyId);
    }

    return limitedResults;
  }

  private async processLookupJob(job: PriceLookupJob) {
    const startTime = Date.now();
    const requestId = uuidv4();

    // Create scraping log entry
    const log = await prisma.priceScrapingLog.create({
      data: {
        request_id: requestId,
        quote_id: job.quoteId,
        item_id: job.itemIndex.toString(),
        search_query: job.item.name,
        search_params: JSON.stringify({
          specifications: job.item.specifications,
          quantity: job.item.quantity,
        }),
        source: 'HOME_DEPOT', // Default to Home Depot first
        status: 'PROCESSING',
        start_time: new Date(),
        user_id: job.userId,
      },
    });

    try {
      // Search for the material
      const results = await this.searchMaterials(job.item.name, {
        source: job.item.preferredSource || 'HOME_DEPOT',
        limit: 5,
        companyId: job.companyId,
      });

      if (results.length === 0) {
        throw new Error('No results found');
      }

      // Update quote with results
      const quote = await prisma.quote.findUnique({
        where: { id: job.quoteId },
      });

      if (!quote) {
        throw new Error('Quote not found');
      }

      const items = JSON.parse(quote.items || '[]');
      
      // Update item with price options
      items[job.itemIndex] = {
        ...items[job.itemIndex],
        lookupStatus: 'options_available',
        lookupResults: results,
        priceHistory: [
          ...(items[job.itemIndex].priceHistory || []),
          {
            timestamp: new Date(),
            results: results.length,
            lowestPrice: Math.min(...results.map((r: any) => r.price || Infinity)),
          },
        ],
      };

      // If only one result, auto-select it
      if (results.length === 1) {
        items[job.itemIndex] = {
          ...items[job.itemIndex],
          unitPrice: results[0].price,
          totalPrice: results[0].price * items[job.itemIndex].quantity,
          sku: results[0].sku,
          sourceUrl: results[0].url,
          imageUrl: results[0].imageUrl,
          lookupStatus: 'confirmed',
        };
      }

      // Update quote
      await prisma.quote.update({
        where: { id: job.quoteId },
        data: {
          items: JSON.stringify(items),
        },
      });

      // Update scraping log
      await prisma.priceScrapingLog.update({
        where: { id: log.id },
        data: {
          status: 'SUCCESS',
          results: JSON.stringify(results),
          end_time: new Date(),
          duration: Date.now() - startTime,
        },
      });

      logger.info(`Price lookup successful for ${job.item.name}`, {
        quoteId: job.quoteId,
        itemIndex: job.itemIndex,
        resultsCount: results.length,
      });

    } catch (error: any) {
      // Update scraping log with error
      await prisma.priceScrapingLog.update({
        where: { id: log.id },
        data: {
          status: 'FAILED',
          error: JSON.stringify({
            message: error.message,
            stack: error.stack,
          }),
          end_time: new Date(),
          duration: Date.now() - startTime,
        },
      });

      // Update quote item status
      const quote = await prisma.quote.findUnique({
        where: { id: job.quoteId },
      });

      if (quote) {
        const items = JSON.parse(quote.items || '[]');
        items[job.itemIndex] = {
          ...items[job.itemIndex],
          lookupStatus: 'failed',
          lookupError: error.message,
        };

        await prisma.quote.update({
          where: { id: job.quoteId },
          data: {
            items: JSON.stringify(items),
          },
        });
      }

      throw error;
    }
  }

  private async storePriceHistory(results: any[], query: string, companyId: string) {
    const priceHistoryRecords = results.map(result => ({
      material_id: result.sku || uuidv4(),
      sku: result.sku,
      name: result.title,
      description: result.description,
      manufacturer: result.manufacturer,
      price: result.price,
      unit: result.unit || 'EA',
      source: result.source,
      url: result.url,
      confidence: result.confidence || 0.8,
      category: result.category,
      image_url: result.imageUrl,
      company_id: companyId,
      scraped_at: new Date(),
    }));

    try {
      await prisma.quoteMaterialPriceHistory.createMany({
        data: priceHistoryRecords,
        skipDuplicates: true,
      });
    } catch (error) {
      logger.error('Failed to store price history:', error);
    }
  }

  async getQueueStatus() {
    if (!this.lookupQueue) {
      return {
        available: false,
        message: 'Queue not initialized (Redis unavailable)',
      };
    }

    const [waiting, active, completed, failed] = await Promise.all([
      this.lookupQueue.getWaitingCount(),
      this.lookupQueue.getActiveCount(),
      this.lookupQueue.getCompletedCount(),
      this.lookupQueue.getFailedCount(),
    ]);

    return {
      available: true,
      waiting,
      active,
      completed,
      failed,
    };
  }

  async shutdown() {
    if (this.worker) {
      await this.worker.close();
    }
    if (this.lookupQueue) {
      await this.lookupQueue.close();
    }
  }
}