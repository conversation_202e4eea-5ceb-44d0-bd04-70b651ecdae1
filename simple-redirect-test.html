<!DOCTYPE html>
<html>
<head>
    <title>Simple Redirect Test</title>
</head>
<body>
    <h1>Simple Redirect Test</h1>
    <button onclick="testLogin()">Test Login</button>
    <button onclick="navigateToQuotesNew()">Navigate to /quotes/new</button>
    <div id="results"></div>
    
    <script>
        function log(message) {
            const div = document.getElementById('results');
            div.innerHTML += `<p>${new Date().toISOString()}: ${message}</p>`;
            console.log(message);
        }
        
        async function testLogin() {
            try {
                const response = await fetch('http://localhost:3001/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'Test123!'
                    })
                });
                
                const data = await response.json();
                log('Login response: ' + JSON.stringify(data.user));
                
                // Store auth data
                const authStorage = {
                    state: {
                        user: data.user,
                        accessToken: data.accessToken,
                        refreshToken: data.refreshToken,
                        isAuthenticated: true,
                        isLoading: false,
                        error: null,
                        hasHydrated: true
                    },
                    version: 0
                };
                localStorage.setItem('auth-storage', JSON.stringify(authStorage));
                log('Auth data stored in localStorage');
                
                // Set auth header for API
                window.authToken = data.accessToken;
                
            } catch (error) {
                log('Login error: ' + error.message);
            }
        }
        
        function navigateToQuotesNew() {
            log('Navigating to /quotes/new...');
            window.location.href = 'http://localhost:3000/quotes/new';
        }
    </script>
</body>
</html>