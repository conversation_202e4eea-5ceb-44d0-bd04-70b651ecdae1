/**
 * Auth helper utilities
 */

/**
 * Validates the stored auth state in localStorage
 * Cleans up any invalid or partial auth data
 */
export function validateStoredAuth(): boolean {
  try {
    const authStorage = localStorage.getItem('auth-storage');
    
    if (!authStorage) {
      return false;
    }
    
    const parsed = JSON.parse(authStorage);
    const state = parsed.state;
    
    if (!state) {
      localStorage.removeItem('auth-storage');
      return false;
    }
    
    // Check for required fields
    const hasValidToken = !!state.accessToken;
    const hasValidUser = !!state.user && 
                        !!state.user.id && 
                        !!state.user.email;
    
    // If we have partial data, clean it up
    if (!hasValidToken || !hasValidUser) {
      localStorage.removeItem('auth-storage');
      return false;
    }
    
    return true;
  } catch (error) {
    // If there's any error parsing, remove the corrupted data
    localStorage.removeItem('auth-storage');
    return false;
  }
}

/**
 * Clears all auth-related data from localStorage
 */
export function clearAuthStorage(): void {
  localStorage.removeItem('auth-storage');
}

/**
 * Debug function to log current auth state
 */
export function debugAuthState(): void {
  if (import.meta.env.DEV) {
    try {
      const authStorage = localStorage.getItem('auth-storage');
      if (authStorage) {
        const parsed = JSON.parse(authStorage);
        console.group('🔐 Auth State Debug');
        console.log('Has Token:', !!parsed.state?.accessToken);
        console.log('Has User:', !!parsed.state?.user);
        console.log('User Details:', {
          id: parsed.state?.user?.id,
          email: parsed.state?.user?.email,
          name: parsed.state?.user?.name,
          role: parsed.state?.user?.role,
        });
        console.groupEnd();
      } else {
        console.log('🔐 No auth state in localStorage');
      }
    } catch (error) {
      console.error('🔐 Error reading auth state:', error);
    }
  }
}