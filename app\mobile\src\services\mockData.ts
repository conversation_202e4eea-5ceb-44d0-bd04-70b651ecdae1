import { Project, User } from '@types/index';

export const mockProjects: Project[] = [
  {
    id: '1',
    name: 'Office Building Renovation',
    customer_id: 'cust-1',
    customer_name: 'ABC Construction Inc.',
    address: '123 Main St',
    city: 'Dallas',
    state: 'TX',
    zip: '75201',
    type: 'commercial',
    status: 'in_progress',
    start_date: '2024-01-15',
    end_date: '2024-06-30',
    created_at: '2024-01-10T10:00:00Z',
    updated_at: '2024-01-10T10:00:00Z',
  },
  {
    id: '2',
    name: 'Residential Rewiring',
    customer_id: 'cust-2',
    customer_name: '<PERSON>',
    address: '456 Oak Ave',
    city: 'Houston',
    state: 'TX',
    zip: '77001',
    type: 'residential',
    status: 'in_progress',
    start_date: '2024-02-01',
    end_date: '2024-02-28',
    created_at: '2024-01-28T14:30:00Z',
    updated_at: '2024-01-28T14:30:00Z',
  },
  {
    id: '3',
    name: 'Solar Panel Installation',
    customer_id: 'cust-3',
    customer_name: 'Green Energy Corp',
    address: '789 Solar Blvd',
    city: 'Austin',
    state: 'TX',
    zip: '78701',
    type: 'commercial',
    status: 'completed',
    start_date: '2023-11-01',
    end_date: '2023-12-15',
    created_at: '2023-10-25T09:15:00Z',
    updated_at: '2023-12-15T16:45:00Z',
  },
  {
    id: '4',
    name: 'Emergency Service Call',
    customer_id: 'cust-4',
    customer_name: 'Quick Fix Apartments',
    address: '321 Emergency Lane',
    city: 'Fort Worth',
    state: 'TX',
    zip: '76101',
    type: 'service',
    status: 'pending',
    start_date: '2024-02-10',
    created_at: '2024-02-09T18:00:00Z',
    updated_at: '2024-02-09T18:00:00Z',
  },
];

export const mockInspections = [
  {
    id: 'insp-1',
    project_id: '1',
    type: 'rough_in',
    status: 'scheduled',
    scheduled_date: '2024-02-15',
    inspector_name: 'Mike Inspector',
  },
  {
    id: 'insp-2',
    project_id: '2',
    type: 'final',
    status: 'pending',
    scheduled_date: '2024-02-28',
  },
];

export const mockMaterials = [
  {
    id: 'mat-1',
    name: '12 AWG THHN Wire',
    sku: 'THHN-12-BLK',
    unit: 'FT',
    unit_price: 0.25,
    quantity_on_hand: 1000,
  },
  {
    id: 'mat-2',
    name: '20A Circuit Breaker',
    sku: 'CB-20A-1P',
    unit: 'EA',
    unit_price: 12.50,
    quantity_on_hand: 50,
  },
  {
    id: 'mat-3',
    name: '1/2" EMT Conduit',
    sku: 'EMT-1/2-10',
    unit: 'FT',
    unit_price: 1.85,
    quantity_on_hand: 500,
  },
];

import NetInfo from '@react-native-community/netinfo';
import { DEMO_MODE, NODE_ENV } from '@env';

// Demo mode flag - checks if we should use mock data
export const isDemoMode = async () => {
  // Check if explicitly set to demo mode via environment variable
  const envDemoMode = DEMO_MODE === 'true';
  if (envDemoMode) {
    return true;
  }

  // Check if we have network connectivity
  try {
    const netInfo = await NetInfo.fetch();
    // Use mock data only if we're offline and not explicitly in production
    return !netInfo.isConnected && NODE_ENV !== 'production';
  } catch (error) {
    console.warn('Failed to check network status:', error);
    // Default to real API if we can't determine network status
    return false;
  }
};