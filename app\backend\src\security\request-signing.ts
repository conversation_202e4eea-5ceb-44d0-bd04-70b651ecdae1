import { Request, Response, NextFunction } from 'express';
import crypto from 'crypto';
import { AppError } from '../middleware/errorHandler';
import { getRedis } from '../services/redis';
import { config } from '../config';

export interface SignedRequest extends Request {
  signature?: {
    timestamp: number;
    nonce: string;
    signature: string;
    deviceId?: string;
  };
}

const SIGNATURE_HEADER = 'x-signature';
const TIMESTAMP_HEADER = 'x-timestamp';
const NONCE_HEADER = 'x-nonce';
const DEVICE_ID_HEADER = 'x-device-id';
const SIGNATURE_WINDOW = 300000; // 5 minutes
const NONCE_EXPIRY = 600; // 10 minutes

// Generate request signature using HMAC-SHA256
export function generateRequestSignature(
  secret: string,
  method: string,
  path: string,
  timestamp: number,
  nonce: string,
  deviceId?: string,
  body?: any
): string {
  const parts = [
    method.toUpperCase(),
    path,
    timestamp.toString(),
    nonce
  ];
  
  // Add device ID if present
  if (deviceId) {
    parts.push(deviceId);
  }
  
  // Add body hash if present
  if (body && Object.keys(body).length > 0) {
    // Sort keys for consistent hashing
    const sortedBody = JSON.stringify(sortKeyDeep(body));
    const bodyHash = crypto.createHash('sha256').update(sortedBody).digest('hex');
    parts.push(bodyHash);
  }
  
  const message = parts.join('\n');
  return crypto.createHmac('sha256', secret).update(message).digest('hex');
}

// Middleware for request signing verification with HMAC-SHA256
export function verifyRequestSignature(options?: {
  getSecret?: (req: Request) => Promise<string | null>;
  requiredPaths?: string[];
  excludePaths?: string[];
  enforceDeviceBinding?: boolean;
}) {
  const getSecret = options?.getSecret || getDefaultSecret;
  const requiredPaths = options?.requiredPaths || [];
  const excludePaths = options?.excludePaths || [];
  const enforceDeviceBinding = options?.enforceDeviceBinding ?? true;
  
  return async (req: SignedRequest, res: Response, next: NextFunction) => {
    // Check if path requires signature
    const pathRequiresSignature = requiredPaths.length === 0 || 
      requiredPaths.some(path => req.path.startsWith(path));
    
    const pathExcluded = excludePaths.some(path => req.path.startsWith(path));
    
    if (!pathRequiresSignature || pathExcluded) {
      return next();
    }
    
    // Extract signature components from headers
    const signature = req.headers[SIGNATURE_HEADER] as string;
    const timestamp = req.headers[TIMESTAMP_HEADER] as string;
    const nonce = req.headers[NONCE_HEADER] as string;
    const deviceId = req.headers[DEVICE_ID_HEADER] as string;
    
    if (!signature || !timestamp || !nonce) {
      return next(new AppError(401, 'Missing signature headers', true, 'MISSING_SIGNATURE'));
    }
    
    // Validate timestamp to prevent replay attacks
    const requestTime = parseInt(timestamp, 10);
    const now = Date.now();
    
    if (isNaN(requestTime) || Math.abs(now - requestTime) > SIGNATURE_WINDOW) {
      return next(new AppError(401, 'Invalid or expired timestamp', true, 'INVALID_TIMESTAMP'));
    }
    
    // Check nonce to prevent replay attacks
    const nonceKey = `security:nonce:${nonce}`;
    const redis = getRedis();
    if (redis) {
      const nonceExists = await redis.exists(nonceKey);
      
      if (nonceExists) {
        return next(new AppError(401, 'Duplicate request detected', true, 'DUPLICATE_NONCE'));
      }
    }
    
    // Get secret for verification
    const secret = await getSecret(req);
    if (!secret) {
      return next(new AppError(401, 'Unable to verify signature', true, 'NO_SECRET'));
    }
    
    // Verify device binding if enforced
    if (enforceDeviceBinding && deviceId) {
      const isValidDevice = await verifyDeviceBinding(req, deviceId);
      if (!isValidDevice) {
        return next(new AppError(403, 'Device mismatch detected', true, 'DEVICE_MISMATCH'));
      }
    }
    
    // Generate expected signature
    const expectedSignature = generateRequestSignature(
      secret,
      req.method,
      req.path,
      requestTime,
      nonce,
      deviceId,
      req.body
    );
    
    // Verify signature using timing-safe comparison
    if (!crypto.timingSafeEqual(Buffer.from(signature), Buffer.from(expectedSignature))) {
      return next(new AppError(401, 'Invalid signature', true, 'INVALID_SIGNATURE'));
    }
    
    // Store nonce to prevent replay attacks
    if (redis) {
      await redis.setex(nonceKey, NONCE_EXPIRY, '1');
    }
    
    // Attach signature info to request for logging/auditing
    req.signature = {
      timestamp: requestTime,
      nonce,
      signature,
      deviceId
    };
    
    next();
  };
}

// Get signing secret based on authentication method
async function getDefaultSecret(req: Request): Promise<string | null> {
  // Check if using API key authentication
  if ((req as any).apiKey) {
    // Retrieve the secret associated with the API key
    const apiKeyId = (req as any).apiKey.id;
    const secretKey = `security:api_secret:${apiKeyId}`;
    
    // In production, these secrets should be stored encrypted
    const redis = getRedis();
    if (redis) {
      const secret = await redis.get(secretKey);
      if (secret) {
        return secret;
      }
    }
    
    // Fallback to environment variable
    return config.security.apiSigningSecret || null;
  }
  
  // Check if using user authentication
  if ((req as any).user) {
    // For user requests, use a user-specific secret or shared secret
    const userId = (req as any).user.userId;
    const userSecretKey = `security:user_secret:${userId}`;
    
    // Check for user-specific secret
    const redis = getRedis();
    if (redis) {
      const userSecret = await redis.get(userSecretKey);
      if (userSecret) {
        return userSecret;
      }
    }
    
    // Fallback to shared user signing secret
    return config.security.userSigningSecret || null;
  }
  
  return null;
}

// Verify device binding for additional security
async function verifyDeviceBinding(req: Request, deviceId: string): Promise<boolean> {
  if (!(req as any).user) {
    // Device binding only applies to authenticated users
    return true;
  }
  
  const userId = (req as any).user.userId;
  const deviceKey = `security:device_binding:${userId}`;
  
  // Get registered devices for this user
  const redis = getRedis();
  if (!redis) {
    // If Redis not available, skip device binding verification
    return true;
  }
  const registeredDevices = await redis.smembers(deviceKey);
  
  if (registeredDevices.length === 0) {
    // First device - register it
    await redis.sadd(deviceKey, deviceId);
    await redis.expire(deviceKey, 86400 * 30); // 30 days
    return true;
  }
  
  // Check if device is registered
  return registeredDevices.includes(deviceId);
}

// Helper to sort object keys recursively for consistent hashing
function sortKeyDeep(obj: any): any {
  if (Array.isArray(obj)) {
    return obj.map(sortKeyDeep);
  }
  
  if (obj !== null && typeof obj === 'object') {
    return Object.keys(obj)
      .sort()
      .reduce((result, key) => {
        result[key] = sortKeyDeep(obj[key]);
        return result;
      }, {} as any);
  }
  
  return obj;
}

// Express middleware to add signature to responses (optional)
export function signResponse(options?: {
  getSecret?: (req: Request) => Promise<string | null>;
  includePaths?: string[];
}) {
  const getSecret = options?.getSecret || getDefaultSecret;
  const includePaths = options?.includePaths || [];
  
  return async (req: Request, res: Response, next: NextFunction) => {
    // Check if response signing is required for this path
    if (includePaths.length > 0 && !includePaths.some(path => req.path.startsWith(path))) {
      return next();
    }
    
    const originalSend = res.send;
    
    res.send = async function(data: any) {
      try {
        const secret = await getSecret(req);
        if (!secret) {
          return originalSend.call(this, data);
        }
        
        const timestamp = Date.now();
        const nonce = crypto.randomBytes(16).toString('hex');
        
        // Create signature for response
        const signature = crypto
          .createHmac('sha256', secret)
          .update(`${res.statusCode}:${timestamp}:${nonce}:${JSON.stringify(data)}`)
          .digest('hex');
        
        // Add signature headers
        res.setHeader('X-Response-Signature', signature);
        res.setHeader('X-Response-Timestamp', timestamp.toString());
        res.setHeader('X-Response-Nonce', nonce);
      } catch (error) {
        // Silent fail - response signing is optional
      }
      
      return originalSend.call(this, data);
    };
    
    next();
  };
}

// Webhook signature verification
export function verifyWebhookSignature(
  secret: string,
  signature: string,
  timestamp: string,
  body: string
): boolean {
  const expectedSignature = crypto
    .createHmac('sha256', secret)
    .update(`${timestamp}.${body}`)
    .digest('hex');
  
  return crypto.timingSafeEqual(
    Buffer.from(signature),
    Buffer.from(expectedSignature)
  );
}

// Middleware to verify webhook signatures
export function verifyWebhook(getSecret: (req: Request) => string) {
  return async (req: Request, res: Response, next: NextFunction) => {
    const signature = req.headers['x-webhook-signature'] as string;
    const timestamp = req.headers['x-webhook-timestamp'] as string;
    
    if (!signature || !timestamp) {
      return next(new AppError(401, 'Missing webhook signature', true, 'MISSING_WEBHOOK_SIGNATURE'));
    }
    
    // Validate timestamp
    const requestTime = parseInt(timestamp, 10);
    const now = Date.now();
    
    if (isNaN(requestTime) || Math.abs(now - requestTime) > SIGNATURE_WINDOW) {
      return next(new AppError(401, 'Invalid or expired webhook timestamp', true, 'INVALID_WEBHOOK_TIMESTAMP'));
    }
    
    const secret = getSecret(req);
    const body = JSON.stringify(req.body);
    
    if (!verifyWebhookSignature(secret, signature, timestamp, body)) {
      return next(new AppError(401, 'Invalid webhook signature', true, 'INVALID_WEBHOOK_SIGNATURE'));
    }
    
    next();
  };
}

// Utility to generate API signing secrets
export function generateApiSigningSecret(): string {
  return crypto.randomBytes(32).toString('hex');
}

// Utility to register a new device for a user
export async function registerUserDevice(userId: string, deviceId: string): Promise<void> {
  const redis = getRedis();
  if (!redis) return;

  const deviceKey = `security:device_binding:${userId}`;
  await redis.sadd(deviceKey, deviceId);
  await redis.expire(deviceKey, 86400 * 30); // 30 days
}

// Utility to revoke a device for a user
export async function revokeUserDevice(userId: string, deviceId: string): Promise<void> {
  const redis = getRedis();
  if (!redis) return;

  const deviceKey = `security:device_binding:${userId}`;
  await redis.srem(deviceKey, deviceId);
}

// Utility to list all registered devices for a user
export async function listUserDevices(userId: string): Promise<string[]> {
  const redis = getRedis();
  if (!redis) return [];

  const deviceKey = `security:device_binding:${userId}`;
  return redis.smembers(deviceKey);
}