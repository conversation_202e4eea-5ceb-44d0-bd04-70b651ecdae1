# npm configuration for React Native mobile app
# This project uses npm, not pnpm

# Use legacy peer deps to avoid React Native dependency conflicts
legacy-peer-deps=true

# Ensure we get exact versions
save-exact=true

# Set registry (use default npm registry)
registry=https://registry.npmjs.org/

# Increase network timeout for large dependencies
fetch-timeout=60000
fetch-retries=2

# Handle native dependencies properly
# React Native requires certain packages to be at the root