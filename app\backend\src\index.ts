import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { setupEncryptionMiddleware } from './security/data-encryption';
import { sanitizeOutput } from './security/validators';
import { sessionMiddleware } from './security/session';
import compression from 'compression';
import { createServer } from 'http';
import { Server } from 'socket.io';
import { rateLimit } from 'express-rate-limit';
import Redis from 'ioredis';
import { Queue } from 'bullmq';
import { config } from './config';

import { errorHandler } from './middleware/errorHandler';
import { requestLogger } from './middleware/requestLogger';
import { authRouter } from './routes/auth';
import { customersRouter } from './routes/customers';
import { projectsRouter } from './routes/projects';
import { estimatesRouter } from './routes/estimates';
import { materialsRouter } from './routes/materials';
import { calculationsRouter } from './routes/calculations';
// import agentsRouter from './routes/agents';
import panelRouter from './routes/panel.routes';
import arcFlashRouter from './routes/arc-flash.routes';
import shortCircuitRouter from './routes/short-circuit.routes';
import permitDocumentsRouter from './routes/permit-documents.routes';
import inspectionsRouter from './routes/inspections';
import { exportsRouter } from './routes/exports';
import { barcodeRouter } from './routes/barcode';
import analyticsRouter from './routes/analytics';
import aiRouter from './routes/ai';
import quotesRouter from './routes/quotes';
import { setupSocketHandlers } from './socket';
import { initializeDatabase } from './database/initialize';
import { startWorkers } from './workers';
// import { AgentService } from './services/agent-service';
// Import logger from utils to avoid circular dependencies
import { logger } from './utils/logger';
export { logger }; // Re-export for backward compatibility
// Import Prisma from database module to avoid circular dependencies
import { prisma } from './database/prisma';
export { prisma }; // Re-export for backward compatibility

// Initialize Redis using the new Redis manager
import { redisManager } from './services/redis-manager';
export let redis: Redis | null = null;

async function initializeRedis(): Promise<void> {
  const connected = await redisManager.connect();
  
  if (connected) {
    redis = redisManager.getClient();
    logger.info('Redis initialized successfully via RedisManager');
    
    // Listen for Redis events
    redisManager.on('disconnected', () => {
      logger.warn('Redis disconnected - features like caching and rate limiting may be affected');
      redis = null;
    });
    
    redisManager.on('connected', () => {
      logger.info('Redis reconnected');
      redis = redisManager.getClient();
      // Re-initialize queues if needed
      initializeQueues();
    });
  } else {
    redis = null;
    logger.warn('Redis not available - application will continue without caching and advanced features');
  }
}

// Initialize job queues (only if Redis is available)
export let materialPriceQueue: Queue | null = null;
export let calculationQueue: Queue | null = null;

function initializeQueues(): void {
  if (redis) {
    materialPriceQueue = new Queue('material-prices', {
      connection: redis as Redis
    });
    
    calculationQueue = new Queue('calculations', {
      connection: redis as Redis
    });
    
    logger.info('Job queues initialized');
  } else {
    logger.warn('Job queues not initialized (Redis not available)');
  }
}

// Create Express app
const app = express();
const httpServer = createServer(app);

// Initialize Socket.io
export const io = new Server(httpServer, {
  cors: {
    origin: config.corsOrigins,
    credentials: true
  }
});

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", 'data:', 'https:'],
      connectSrc: ["'self'", 'http://localhost:3001', 'http://localhost:3000'],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: true,
  crossOriginOpenerPolicy: true,
  crossOriginResourcePolicy: { policy: 'cross-origin' },
  dnsPrefetchControl: true,
  frameguard: { action: 'deny' },
  hidePoweredBy: true,
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  },
  ieNoOpen: true,
  noSniff: true,
  originAgentCluster: true,
  permittedCrossDomainPolicies: false,
  referrerPolicy: { policy: 'strict-origin-when-cross-origin' },
  xssFilter: true,
}));

// Basic middleware
app.use(compression());
app.use(cors({
  origin: config.corsOrigins,
  credentials: true,
  optionsSuccessStatus: 200
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Output sanitization
app.use(sanitizeOutput());

// Session security for sensitive operations
app.use('/api/admin', sessionMiddleware({ requireFreshSession: true }));
app.use('/api/exports', sessionMiddleware());
app.use('/api/permit-documents', sessionMiddleware());

// Request logging
app.use(requestLogger);

// Rate limiting
if (config.isDevelopment) {
  // In development, use a more lenient rate limiter or skip it entirely
  if (redis) {
    // If Redis is available, use normal rate limiting
    const limiter = rateLimit({
      windowMs: config.rateLimitWindowMs,
      max: config.rateLimitMaxRequests * 10, // 10x more lenient in dev
      message: 'Too many requests from this IP, please try again later.',
      standardHeaders: true,
      legacyHeaders: false,
      skip: (req) => {
        // Skip rate limiting for certain endpoints in development
        const skipPaths = ['/health', '/api-docs'];
        return skipPaths.some(path => req.path.startsWith(path));
      }
    });
    app.use('/api/', limiter);
  } else {
    // If Redis is not available in development, skip rate limiting entirely
    logger.warn('Rate limiting disabled in development (Redis not available)');
  }
} else {
  // In production, use normal rate limiting
  const limiter = rateLimit({
    windowMs: config.rateLimitWindowMs,
    max: config.rateLimitMaxRequests,
    message: 'Too many requests from this IP, please try again later.',
    standardHeaders: true,
    legacyHeaders: false,
  });
  app.use('/api/', limiter);
}

// Basic health check
app.get('/health', (_req, res) => {
  const redisHealth = redisManager.getHealth();
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    redis: {
      connected: redisHealth.isConnected,
      lastError: redisHealth.lastError
    }
  });
});

// API routes
app.use('/api/auth', authRouter);
app.use('/api/customers', customersRouter);
app.use('/api/projects', projectsRouter);
app.use('/api/estimates', estimatesRouter);
app.use('/api/materials', materialsRouter);
app.use('/api/calculations', calculationsRouter);
// app.use('/api/agents', agentsRouter);
app.use('/api/panels', panelRouter);
app.use('/api/arc-flash', arcFlashRouter);
app.use('/api/short-circuit', shortCircuitRouter);
app.use('/api/permit-documents', permitDocumentsRouter);
app.use('/api/inspections', inspectionsRouter);
app.use('/api/exports', exportsRouter);
app.use('/api/barcode', barcodeRouter);
app.use('/api/analytics', analyticsRouter);
app.use('/api/ai', aiRouter);
app.use('/api/quotes', quotesRouter);

// Socket.io handlers
setupSocketHandlers(io);

// Error handling
app.use(errorHandler);

// 404 handler
app.use((_req, res) => {
  res.status(404).json({ error: 'Not found' });
});

// Graceful shutdown
process.on('SIGTERM', async () => {
  logger.info('SIGTERM received, shutting down gracefully');
  httpServer.close(() => {
    logger.info('HTTP server closed');
  });
  
  await prisma.$disconnect();
  await redisManager.disconnect();
  process.exit(0);
});

process.on('SIGINT', async () => {
  logger.info('SIGINT received, shutting down gracefully');
  httpServer.close(() => {
    logger.info('HTTP server closed');
  });
  
  await prisma.$disconnect();
  await redisManager.disconnect();
  process.exit(0);
});

// Security headers for WebSocket
io.engine.on('headers', (headers: any) => {
  headers['X-Frame-Options'] = 'DENY';
  headers['X-Content-Type-Options'] = 'nosniff';
  headers['X-XSS-Protection'] = '1; mode=block';
});

// Enforce HTTPS in production
if (config.isProduction) {
  app.use((req, res, next) => {
    if (req.header('x-forwarded-proto') !== 'https') {
      res.redirect(`https://${req.header('host')}${req.url}`);
    } else {
      next();
    }
  });
}

// Start server
async function startServer(): Promise<void> {
  try {
    // Initialize database
    await initializeDatabase();
    
    // Setup Prisma encryption middleware
    setupEncryptionMiddleware();
    
    // Initialize Redis (optional)
    await initializeRedis();
    
    // Initialize job queues if Redis is available
    initializeQueues();
    
    // Initialize AI agent service (temporarily disabled)
    // const agentService = AgentService.getInstance();
    // await agentService.initialize();
    // logger.info('AI Agent service initialized');
    
    // Start background workers only if Redis is available
    if (redis) {
      startWorkers();
    } else {
      logger.warn('Background workers not started (Redis not available)');
    }
    
    // Start HTTP server
    httpServer.listen(config.port, () => {
      logger.info(`Server running on port ${config.port}`);
      logger.info(`Environment: ${config.nodeEnv}`);
      if (!redis) {
        logger.warn('Running without Redis - some features may be limited');
      }
    });
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

startServer();