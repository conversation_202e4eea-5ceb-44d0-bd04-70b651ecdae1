import { BaseAgent, BaseAgentConfig, AgentCapability } from '../base/base-agent';
import { MemoryStore, MemoryType, MemoryItem, MemoryQuery } from '../infrastructure/memory-store';
import { z } from 'zod';
import * as crypto from 'crypto';

// Knowledge categories specific to electrical domain
export enum KnowledgeCategory {
  NEC_VIOLATIONS = 'NEC_VIOLATIONS',
  CALCULATION_PATTERNS = 'CALCULATION_PATTERNS',
  MATERIAL_PRICING = 'MATERIAL_PRICING',
  PROJECT_ESTIMATION = 'PROJECT_ESTIMATION',
  COMMON_MISTAKES = 'COMMON_MISTAKES',
  COMPONENT_SPECS = 'COMPONENT_SPECS',
  REGIONAL_CODES = 'REGIONAL_CODES',
  BEST_PRACTICES = 'BEST_PRACTICES',
}

// Learning pattern schema
const learningPatternSchema = z.object({
  pattern: z.string(),
  category: z.nativeEnum(KnowledgeCategory),
  occurrences: z.number(),
  confidence: z.number().min(0).max(1),
  context: z.record(z.any()),
  relatedConcepts: z.array(z.string()),
});

// Knowledge graph node schema
const knowledgeNodeSchema = z.object({
  id: z.string(),
  concept: z.string(),
  category: z.nativeEnum(KnowledgeCategory),
  properties: z.record(z.any()),
  connections: z.array(z.object({
    targetId: z.string(),
    relationship: z.string(),
    strength: z.number().min(0).max(1),
  })),
});

// Memory consolidation request schema
const consolidationRequestSchema = z.object({
  threshold: z.number().min(0).max(1).default(0.7),
  minAccessCount: z.number().default(3),
  timeWindow: z.number().default(7 * 24 * 60 * 60 * 1000), // 7 days in ms
});

// Pattern search request schema
const patternSearchSchema = z.object({
  category: z.nativeEnum(KnowledgeCategory).optional(),
  minConfidence: z.number().min(0).max(1).default(0.5),
  includeRelated: z.boolean().default(true),
});

// Knowledge storage request schema
const knowledgeStorageSchema = z.object({
  content: z.any(),
  category: z.nativeEnum(KnowledgeCategory),
  tags: z.array(z.string()),
  importance: z.number().min(0).max(1).default(0.5),
  relatedConcepts: z.array(z.string()).optional(),
});

// Error learning request schema
const errorLearningSchema = z.object({
  errorType: z.string(),
  context: z.record(z.any()),
  correctSolution: z.any().optional(),
  category: z.nativeEnum(KnowledgeCategory),
});

// Memory optimization request schema
const memoryOptimizationSchema = z.object({
  maxAge: z.number().optional(), // Max age in milliseconds
  minImportance: z.number().min(0).max(1).default(0.3),
  preserveCategories: z.array(z.nativeEnum(KnowledgeCategory)).optional(),
});

export class MemoryAgent extends BaseAgent {
  private knowledgeGraph: Map<string, z.infer<typeof knowledgeNodeSchema>>;
  private learningPatterns: Map<string, z.infer<typeof learningPatternSchema>>;
  private consolidationTimer: NodeJS.Timeout | null = null;
  private patternRecognitionThreshold = 3; // Min occurrences for pattern recognition

  constructor(config: Omit<BaseAgentConfig, 'capabilities'>) {
    const capabilities: AgentCapability[] = [
      {
        name: 'store-knowledge',
        description: 'Store new learnings with proper categorization',
        inputSchema: knowledgeStorageSchema,
        outputSchema: z.object({ memoryId: z.string(), success: z.boolean() }),
      },
      {
        name: 'retrieve-knowledge',
        description: 'Semantic search across memories',
        inputSchema: z.object({
          query: z.string(),
          category: z.nativeEnum(KnowledgeCategory).optional(),
          limit: z.number().default(10),
        }),
        outputSchema: z.array(memoryItemSchema),
      },
      {
        name: 'consolidate-memory',
        description: 'Move important patterns to long-term memory',
        inputSchema: consolidationRequestSchema,
        outputSchema: z.object({ consolidated: z.number(), pruned: z.number() }),
      },
      {
        name: 'find-patterns',
        description: 'Identify recurring calculation patterns',
        inputSchema: patternSearchSchema,
        outputSchema: z.array(learningPatternSchema),
      },
      {
        name: 'learn-from-errors',
        description: 'Store and learn from past mistakes',
        inputSchema: errorLearningSchema,
        outputSchema: z.object({ learned: z.boolean(), relatedPatterns: z.array(z.string()) }),
      },
      {
        name: 'optimize-memory',
        description: 'Prune outdated or redundant memories',
        inputSchema: memoryOptimizationSchema,
        outputSchema: z.object({ removed: z.number(), optimized: z.number() }),
      },
      {
        name: 'build-knowledge-graph',
        description: 'Build connections between related concepts',
        inputSchema: z.object({ category: z.nativeEnum(KnowledgeCategory).optional() }),
        outputSchema: z.object({ nodes: z.number(), connections: z.number() }),
      },
      {
        name: 'get-insights',
        description: 'Get insights about a specific electrical domain topic',
        inputSchema: z.object({
          topic: z.string(),
          category: z.nativeEnum(KnowledgeCategory).optional(),
        }),
        outputSchema: z.object({
          insights: z.array(z.string()),
          relatedConcepts: z.array(z.string()),
          confidence: z.number(),
        }),
      },
    ];

    super({
      ...config,
      type: 'memory',
      description: 'Advanced memory and learning agent for electrical calculations',
      capabilities,
    });

    // Initialize Maps in constructor to avoid transpilation issues
    this.knowledgeGraph = new Map();
    this.learningPatterns = new Map();
  }

  protected async onInitialize(): Promise<void> {
    // Load existing knowledge graph and patterns
    await this.loadKnowledgeGraph();
    await this.loadLearningPatterns();

    // Schedule periodic memory consolidation
    this.consolidationTimer = setInterval(async () => {
      await this.autoConsolidateMemory();
    }, 60 * 60 * 1000); // Every hour

    await this.log('Memory agent initialized with knowledge graph and learning patterns', { level: 'info' });
  }

  protected async processTask(taskType: string, data: any): Promise<any> {
    switch (taskType) {
      case 'store-knowledge':
        return this.storeElectricalKnowledge(data);
      case 'retrieve-knowledge':
        return this.retrieveElectricalKnowledge(data);
      case 'consolidate-memory':
        return this.consolidateMemory(data);
      case 'find-patterns':
        return this.findPatterns(data);
      case 'learn-from-errors':
        return this.learnFromErrors(data);
      case 'optimize-memory':
        return this.optimizeMemory(data);
      case 'build-knowledge-graph':
        return this.buildKnowledgeGraph(data);
      case 'get-insights':
        return this.getInsights(data);
      default:
        throw new Error(`Unknown task type: ${taskType}`);
    }
  }

  private async storeElectricalKnowledge(data: z.infer<typeof knowledgeStorageSchema>): Promise<{ memoryId: string; success: boolean }> {
    try {
      // Generate embedding for semantic search (simplified version)
      const embedding = this.generateEmbedding(JSON.stringify(data.content));

      // Store in memory with electrical-specific metadata
      const memory = await this.memoryStore.store({
        type: MemoryType.SEMANTIC,
        agentId: this.id,
        content: {
          ...data.content,
          category: data.category,
        },
        metadata: {
          tags: [...data.tags, data.category],
          importance: data.importance,
        },
        embedding,
      });

      // Update knowledge graph
      if (data.relatedConcepts && data.relatedConcepts.length > 0) {
        await this.updateKnowledgeGraph(memory.id, data.content, data.category, data.relatedConcepts);
      }

      // Check for patterns
      await this.detectPatterns(data.category, data.content);

      return { memoryId: memory.id, success: true };
    } catch (error) {
      await this.log('Failed to store knowledge', { level: 'error', error });
      return { memoryId: '', success: false };
    }
  }

  private async retrieveElectricalKnowledge(data: { query: string; category?: KnowledgeCategory; limit: number }): Promise<MemoryItem[]> {
    // Generate query embedding
    const queryEmbedding = this.generateEmbedding(data.query);

    // Retrieve memories with optional category filter
    const memories = await this.memoryStore.retrieve({
      type: MemoryType.SEMANTIC,
      agentId: this.id,
      tags: data.category ? [data.category] : undefined,
      limit: data.limit * 2, // Get more for semantic filtering
    });

    // Perform semantic similarity ranking
    const rankedMemories = memories
      .map(memory => ({
        memory,
        similarity: this.cosineSimilarity(queryEmbedding, memory.embedding || []),
      }))
      .filter(item => item.similarity > 0.5) // Similarity threshold
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, data.limit)
      .map(item => item.memory);

    // Boost importance for frequently accessed items
    for (const memory of rankedMemories) {
      if (memory.metadata.accessCount > 5) {
        await this.memoryStore.updateImportance(
          memory.id,
          Math.min(1, memory.metadata.importance + 0.1)
        );
      }
    }

    return rankedMemories;
  }

  private async consolidateMemory(data: z.infer<typeof consolidationRequestSchema>): Promise<{ consolidated: number; pruned: number }> {
    let consolidated = 0;
    let pruned = 0;

    // Get short-term memories
    const shortTermMemories = await this.memoryStore.retrieve({
      type: MemoryType.SHORT_TERM,
      agentId: this.id,
    });

    const now = Date.now();

    for (const memory of shortTermMemories) {
      const age = now - memory.metadata.created.getTime();
      
      // Check consolidation criteria
      if (
        memory.metadata.importance >= data.threshold ||
        memory.metadata.accessCount >= data.minAccessCount ||
        (age <= data.timeWindow && memory.metadata.accessCount > 1)
      ) {
        // Move to long-term memory
        await this.memoryStore.store({
          ...memory,
          type: MemoryType.LONG_TERM,
          metadata: {
            ...memory.metadata,
            tags: [...memory.metadata.tags, 'consolidated'],
          },
        });
        consolidated++;
      } else if (age > data.timeWindow) {
        // Prune old, unimportant memories
        await this.memoryStore.delete(memory.id);
        pruned++;
      }
    }

    await this.log(`Memory consolidation complete: ${consolidated} consolidated, ${pruned} pruned`, { level: 'info' });
    return { consolidated, pruned };
  }

  private async findPatterns(data: z.infer<typeof patternSearchSchema>): Promise<Array<z.infer<typeof learningPatternSchema>>> {
    const patterns: Array<z.infer<typeof learningPatternSchema>> = [];

    // Filter patterns by category and confidence
    for (const [id, pattern] of this.learningPatterns) {
      if (
        (!data.category || pattern.category === data.category) &&
        pattern.confidence >= data.minConfidence
      ) {
        patterns.push(pattern);
      }
    }

    // Include related patterns if requested
    if (data.includeRelated) {
      const relatedPatterns = new Set<string>();
      
      for (const pattern of patterns) {
        for (const relatedConcept of pattern.relatedConcepts) {
          const related = Array.from(this.learningPatterns.values()).filter(p =>
            p.relatedConcepts.includes(relatedConcept) && 
            !patterns.includes(p)
          );
          related.forEach(p => relatedPatterns.add(JSON.stringify(p)));
        }
      }

      // Add unique related patterns
      for (const patternStr of relatedPatterns) {
        patterns.push(JSON.parse(patternStr));
      }
    }

    return patterns.sort((a, b) => b.confidence - a.confidence);
  }

  private async learnFromErrors(data: z.infer<typeof errorLearningSchema>): Promise<{ learned: boolean; relatedPatterns: string[] }> {
    try {
      // Store the error as a learning experience
      const errorMemory = await this.memoryStore.store({
        type: MemoryType.EPISODIC,
        agentId: this.id,
        content: {
          errorType: data.errorType,
          context: data.context,
          correctSolution: data.correctSolution,
          category: data.category,
        },
        metadata: {
          tags: ['error', 'learning', data.category, data.errorType],
          importance: 0.9, // Errors are important to remember
        },
      });

      // Find similar errors
      const similarErrors = await this.memoryStore.retrieve({
        tags: ['error', data.errorType],
        limit: 10,
      });

      // Extract patterns from repeated errors
      const relatedPatterns: string[] = [];
      if (similarErrors.length >= this.patternRecognitionThreshold) {
        const patternId = `error-pattern-${data.errorType}-${Date.now()}`;
        const pattern: z.infer<typeof learningPatternSchema> = {
          pattern: `Common ${data.errorType} in ${data.category}`,
          category: data.category,
          occurrences: similarErrors.length,
          confidence: Math.min(1, similarErrors.length / 10),
          context: {
            errorType: data.errorType,
            commonContexts: this.extractCommonContext(similarErrors),
          },
          relatedConcepts: this.extractRelatedConcepts(similarErrors),
        };

        this.learningPatterns.set(patternId, pattern);
        relatedPatterns.push(patternId);

        // Store pattern as semantic knowledge
        await this.storeKnowledge(
          {
            pattern,
            examples: similarErrors.slice(0, 3).map(e => e.content),
          },
          ['error-pattern', data.errorType],
          0.8
        );
      }

      return { learned: true, relatedPatterns };
    } catch (error) {
      await this.log('Failed to learn from error', { level: 'error', error });
      return { learned: false, relatedPatterns: [] };
    }
  }

  private async optimizeMemory(data: z.infer<typeof memoryOptimizationSchema>): Promise<{ removed: number; optimized: number }> {
    let removed = 0;
    let optimized = 0;

    // Get all memories for optimization
    const allMemories = await this.memoryStore.retrieve({
      agentId: this.id,
    });

    const now = Date.now();
    const memoriesToRemove: string[] = [];
    const memoriesToOptimize: Map<string, Partial<MemoryItem>> = new Map();

    for (const memory of allMemories) {
      const age = now - memory.metadata.created.getTime();
      const daysSinceAccess = (now - memory.metadata.accessed.getTime()) / (24 * 60 * 60 * 1000);

      // Skip preserved categories
      if (data.preserveCategories && memory.metadata.tags.some(tag => 
        data.preserveCategories!.includes(tag as KnowledgeCategory)
      )) {
        continue;
      }

      // Remove old, unimportant, rarely accessed memories
      if (
        (data.maxAge && age > data.maxAge) ||
        (memory.metadata.importance < data.minImportance && daysSinceAccess > 30) ||
        (memory.metadata.accessCount === 0 && daysSinceAccess > 7)
      ) {
        memoriesToRemove.push(memory.id);
      } else if (memory.metadata.accessCount > 10 && memory.metadata.importance < 0.7) {
        // Boost importance of frequently accessed memories
        memoriesToOptimize.set(memory.id, {
          metadata: {
            ...memory.metadata,
            importance: Math.min(1, memory.metadata.importance + 0.2),
          },
        });
      }
    }

    // Remove redundant memories (similar content)
    const semanticGroups = this.groupBySimilarity(allMemories);
    for (const group of semanticGroups) {
      if (group.length > 1) {
        // Keep the most important/accessed one
        const sorted = group.sort((a, b) => 
          (b.metadata.importance * b.metadata.accessCount) - 
          (a.metadata.importance * a.metadata.accessCount)
        );
        
        // Remove duplicates
        for (let i = 1; i < sorted.length; i++) {
          if (!memoriesToRemove.includes(sorted[i].id)) {
            memoriesToRemove.push(sorted[i].id);
          }
        }
      }
    }

    // Execute removal
    for (const id of memoriesToRemove) {
      await this.memoryStore.delete(id);
      removed++;
    }

    // Execute optimization
    for (const [id, updates] of memoriesToOptimize) {
      if (updates.metadata?.importance) {
        await this.memoryStore.updateImportance(id, updates.metadata.importance);
        optimized++;
      }
    }

    await this.log(`Memory optimization complete: ${removed} removed, ${optimized} optimized`, { level: 'info' });
    return { removed, optimized };
  }

  private async buildKnowledgeGraph(data: { category?: KnowledgeCategory }): Promise<{ nodes: number; connections: number }> {
    // Get relevant memories
    const memories = await this.memoryStore.retrieve({
      type: MemoryType.SEMANTIC,
      agentId: this.id,
      tags: data.category ? [data.category] : undefined,
    });

    let nodesCreated = 0;
    let connectionsCreated = 0;

    // Build nodes from memories
    for (const memory of memories) {
      const nodeId = this.generateNodeId(memory.content);
      
      if (!this.knowledgeGraph.has(nodeId)) {
        const node: z.infer<typeof knowledgeNodeSchema> = {
          id: nodeId,
          concept: this.extractConcept(memory.content),
          category: this.extractCategory(memory.metadata.tags),
          properties: memory.content,
          connections: [],
        };

        this.knowledgeGraph.set(nodeId, node);
        nodesCreated++;
      }
    }

    // Build connections based on similarity and relationships
    const nodeArray = Array.from(this.knowledgeGraph.values());
    for (let i = 0; i < nodeArray.length; i++) {
      for (let j = i + 1; j < nodeArray.length; j++) {
        const similarity = this.calculateConceptSimilarity(nodeArray[i], nodeArray[j]);
        
        if (similarity > 0.6) {
          // Create bidirectional connection
          const relationship = this.inferRelationship(nodeArray[i], nodeArray[j]);
          
          nodeArray[i].connections.push({
            targetId: nodeArray[j].id,
            relationship,
            strength: similarity,
          });

          nodeArray[j].connections.push({
            targetId: nodeArray[i].id,
            relationship: this.reverseRelationship(relationship),
            strength: similarity,
          });

          connectionsCreated += 2;
        }
      }
    }

    // Save knowledge graph
    await this.saveKnowledgeGraph();

    return { nodes: this.knowledgeGraph.size, connections: connectionsCreated };
  }

  private async getInsights(data: { topic: string; category?: KnowledgeCategory }): Promise<{
    insights: string[];
    relatedConcepts: string[];
    confidence: number;
  }> {
    // Search for relevant memories
    const memories = await this.retrieveElectricalKnowledge({
      query: data.topic,
      category: data.category,
      limit: 20,
    });

    if (memories.length === 0) {
      return {
        insights: ['No relevant information found on this topic'],
        relatedConcepts: [],
        confidence: 0,
      };
    }

    // Extract insights from memories
    const insights: string[] = [];
    const relatedConcepts = new Set<string>();
    let totalImportance = 0;

    // Analyze patterns related to the topic
    const relatedPatterns = await this.findPatterns({
      category: data.category,
      minConfidence: 0.5,
      includeRelated: true,
    });

    // Generate insights from memories
    for (const memory of memories) {
      const insight = this.generateInsight(memory, data.topic);
      if (insight) {
        insights.push(insight);
        totalImportance += memory.metadata.importance;
      }

      // Extract related concepts
      if (memory.content.relatedConcepts) {
        memory.content.relatedConcepts.forEach((c: string) => relatedConcepts.add(c));
      }
    }

    // Add pattern-based insights
    for (const pattern of relatedPatterns) {
      if (this.isRelevantToTopic(pattern, data.topic)) {
        insights.push(`Pattern detected: ${pattern.pattern} (${pattern.occurrences} occurrences, ${Math.round(pattern.confidence * 100)}% confidence)`);
        pattern.relatedConcepts.forEach(c => relatedConcepts.add(c));
      }
    }

    // Find related nodes in knowledge graph
    const topicNode = this.findNodeByTopic(data.topic);
    if (topicNode) {
      for (const connection of topicNode.connections) {
        const connectedNode = this.knowledgeGraph.get(connection.targetId);
        if (connectedNode) {
          relatedConcepts.add(connectedNode.concept);
        }
      }
    }

    const confidence = Math.min(1, totalImportance / memories.length);

    return {
      insights: insights.slice(0, 10), // Top 10 insights
      relatedConcepts: Array.from(relatedConcepts).slice(0, 15),
      confidence,
    };
  }

  // Helper methods

  private generateEmbedding(text: string): number[] {
    // Simplified embedding generation (in production, use proper embeddings)
    const hash = crypto.createHash('sha256').update(text).digest();
    const embedding: number[] = [];
    
    for (let i = 0; i < 128; i++) {
      embedding.push(hash[i % hash.length] / 255);
    }
    
    return embedding;
  }

  private cosineSimilarity(a: number[], b: number[]): number {
    if (a.length !== b.length) return 0;
    
    let dotProduct = 0;
    let normA = 0;
    let normB = 0;
    
    for (let i = 0; i < a.length; i++) {
      dotProduct += a[i] * b[i];
      normA += a[i] * a[i];
      normB += b[i] * b[i];
    }
    
    return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
  }

  private async detectPatterns(category: KnowledgeCategory, content: any): Promise<void> {
    // Look for similar content in recent memories
    const recentMemories = await this.memoryStore.retrieve({
      type: MemoryType.SEMANTIC,
      agentId: this.id,
      tags: [category],
      limit: 50,
      sortBy: 'created',
    });

    // Group by similarity
    const contentStr = JSON.stringify(content);
    const similarMemories = recentMemories.filter(m => {
      const similarity = this.cosineSimilarity(
        this.generateEmbedding(contentStr),
        m.embedding || []
      );
      return similarity > 0.8;
    });

    if (similarMemories.length >= this.patternRecognitionThreshold) {
      const patternId = `pattern-${category}-${Date.now()}`;
      const pattern: z.infer<typeof learningPatternSchema> = {
        pattern: this.extractPatternDescription(similarMemories),
        category,
        occurrences: similarMemories.length,
        confidence: Math.min(1, similarMemories.length / 10),
        context: this.extractCommonContext(similarMemories),
        relatedConcepts: this.extractRelatedConcepts(similarMemories),
      };

      this.learningPatterns.set(patternId, pattern);
    }
  }

  private extractCommonContext(memories: MemoryItem[]): Record<string, any> {
    const commonContext: Record<string, any> = {};
    
    // Find common properties across memories
    if (memories.length > 0) {
      const firstContent = memories[0].content;
      
      for (const key in firstContent) {
        const values = memories.map(m => m.content[key]).filter(v => v !== undefined);
        
        if (values.length === memories.length) {
          // All memories have this property
          const uniqueValues = [...new Set(values.map(v => JSON.stringify(v)))];
          
          if (uniqueValues.length === 1) {
            commonContext[key] = JSON.parse(uniqueValues[0]);
          } else if (uniqueValues.length < memories.length / 2) {
            commonContext[key] = uniqueValues.map(v => JSON.parse(v));
          }
        }
      }
    }
    
    return commonContext;
  }

  private extractRelatedConcepts(memories: MemoryItem[]): string[] {
    const concepts = new Set<string>();
    
    for (const memory of memories) {
      if (memory.metadata.tags) {
        memory.metadata.tags.forEach(tag => concepts.add(tag));
      }
      
      if (memory.content.relatedConcepts) {
        memory.content.relatedConcepts.forEach((c: string) => concepts.add(c));
      }
    }
    
    return Array.from(concepts);
  }

  private extractPatternDescription(memories: MemoryItem[]): string {
    // Extract common pattern from similar memories
    const commonTags = this.extractRelatedConcepts(memories);
    const context = this.extractCommonContext(memories);
    
    return `Recurring pattern in ${commonTags.join(', ')} with context: ${JSON.stringify(context).substring(0, 100)}...`;
  }

  private groupBySimilarity(memories: MemoryItem[]): MemoryItem[][] {
    const groups: MemoryItem[][] = [];
    const processed = new Set<string>();
    
    for (const memory of memories) {
      if (processed.has(memory.id)) continue;
      
      const group = [memory];
      processed.add(memory.id);
      
      for (const other of memories) {
        if (processed.has(other.id)) continue;
        
        const similarity = this.cosineSimilarity(
          memory.embedding || [],
          other.embedding || []
        );
        
        if (similarity > 0.9) {
          group.push(other);
          processed.add(other.id);
        }
      }
      
      if (group.length > 1) {
        groups.push(group);
      }
    }
    
    return groups;
  }

  private async updateKnowledgeGraph(
    memoryId: string,
    content: any,
    category: KnowledgeCategory,
    relatedConcepts: string[]
  ): Promise<void> {
    const nodeId = this.generateNodeId(content);
    const node: z.infer<typeof knowledgeNodeSchema> = {
      id: nodeId,
      concept: this.extractConcept(content),
      category,
      properties: { ...content, memoryId },
      connections: [],
    };

    // Add connections to related concepts
    for (const concept of relatedConcepts) {
      const relatedNodeId = this.generateNodeId({ concept });
      
      node.connections.push({
        targetId: relatedNodeId,
        relationship: 'related_to',
        strength: 0.7,
      });
    }

    this.knowledgeGraph.set(nodeId, node);
  }

  private generateNodeId(content: any): string {
    const str = JSON.stringify(content);
    return crypto.createHash('md5').update(str).digest('hex').substring(0, 16);
  }

  private extractConcept(content: any): string {
    if (typeof content === 'string') return content.substring(0, 50);
    if (content.concept) return content.concept;
    if (content.name) return content.name;
    if (content.title) return content.title;
    
    return JSON.stringify(content).substring(0, 50);
  }

  private extractCategory(tags: string[]): KnowledgeCategory {
    for (const tag of tags) {
      if (Object.values(KnowledgeCategory).includes(tag as KnowledgeCategory)) {
        return tag as KnowledgeCategory;
      }
    }
    return KnowledgeCategory.BEST_PRACTICES;
  }

  private calculateConceptSimilarity(
    node1: z.infer<typeof knowledgeNodeSchema>,
    node2: z.infer<typeof knowledgeNodeSchema>
  ): number {
    // Category similarity
    if (node1.category === node2.category) {
      // Check property similarity
      const props1 = JSON.stringify(node1.properties);
      const props2 = JSON.stringify(node2.properties);
      
      return this.cosineSimilarity(
        this.generateEmbedding(props1),
        this.generateEmbedding(props2)
      );
    }
    
    return 0.3; // Base similarity for different categories
  }

  private inferRelationship(
    node1: z.infer<typeof knowledgeNodeSchema>,
    node2: z.infer<typeof knowledgeNodeSchema>
  ): string {
    if (node1.category === node2.category) {
      return 'similar_to';
    }
    
    // Infer based on categories
    const relationships: Record<string, Record<string, string>> = {
      [KnowledgeCategory.NEC_VIOLATIONS]: {
        [KnowledgeCategory.BEST_PRACTICES]: 'violates',
        [KnowledgeCategory.COMMON_MISTAKES]: 'leads_to',
      },
      [KnowledgeCategory.CALCULATION_PATTERNS]: {
        [KnowledgeCategory.COMMON_MISTAKES]: 'prevents',
        [KnowledgeCategory.BEST_PRACTICES]: 'implements',
      },
      [KnowledgeCategory.MATERIAL_PRICING]: {
        [KnowledgeCategory.PROJECT_ESTIMATION]: 'affects',
        [KnowledgeCategory.COMPONENT_SPECS]: 'prices',
      },
    };
    
    return relationships[node1.category]?.[node2.category] || 'related_to';
  }

  private reverseRelationship(relationship: string): string {
    const reverseMap: Record<string, string> = {
      'violates': 'violated_by',
      'leads_to': 'caused_by',
      'prevents': 'prevented_by',
      'implements': 'implemented_by',
      'affects': 'affected_by',
      'prices': 'priced_by',
      'similar_to': 'similar_to',
      'related_to': 'related_to',
    };
    
    return reverseMap[relationship] || relationship;
  }

  private generateInsight(memory: MemoryItem, topic: string): string | null {
    const content = memory.content;
    
    // Generate insight based on memory type and content
    if (content.category === KnowledgeCategory.NEC_VIOLATIONS) {
      return `NEC Violation Pattern: ${content.violation || content.pattern || 'Check compliance'}`;
    }
    
    if (content.category === KnowledgeCategory.CALCULATION_PATTERNS) {
      return `Calculation Pattern: ${content.pattern || content.description || 'Optimization available'}`;
    }
    
    if (content.category === KnowledgeCategory.COMMON_MISTAKES) {
      return `Common Mistake: ${content.errorType || content.mistake || 'Error pattern detected'}`;
    }
    
    if (content.category === KnowledgeCategory.BEST_PRACTICES) {
      return `Best Practice: ${content.practice || content.recommendation || 'Recommended approach'}`;
    }
    
    // Generic insight
    if (content.insight) return content.insight;
    if (content.summary) return content.summary;
    
    return null;
  }

  private isRelevantToTopic(pattern: z.infer<typeof learningPatternSchema>, topic: string): boolean {
    const topicLower = topic.toLowerCase();
    const patternStr = JSON.stringify(pattern).toLowerCase();
    
    return patternStr.includes(topicLower) || 
           pattern.relatedConcepts.some(c => c.toLowerCase().includes(topicLower));
  }

  private findNodeByTopic(topic: string): z.infer<typeof knowledgeNodeSchema> | null {
    const topicLower = topic.toLowerCase();
    
    for (const node of this.knowledgeGraph.values()) {
      if (
        node.concept.toLowerCase().includes(topicLower) ||
        JSON.stringify(node.properties).toLowerCase().includes(topicLower)
      ) {
        return node;
      }
    }
    
    return null;
  }

  private async autoConsolidateMemory(): Promise<void> {
    try {
      await this.consolidateMemory({
        threshold: 0.7,
        minAccessCount: 3,
        timeWindow: 7 * 24 * 60 * 60 * 1000,
      });
    } catch (error) {
      await this.log('Auto-consolidation failed', { level: 'error', error });
    }
  }

  private async loadKnowledgeGraph(): Promise<void> {
    try {
      const graphMemory = await this.memoryStore.retrieve({
        type: MemoryType.SEMANTIC,
        agentId: this.id,
        tags: ['knowledge-graph'],
        limit: 1,
      });

      if (graphMemory.length > 0) {
        const nodes = graphMemory[0].content.nodes || [];
        for (const node of nodes) {
          this.knowledgeGraph.set(node.id, node);
        }
      }
    } catch (error) {
      await this.log('Failed to load knowledge graph', { level: 'warn', error });
    }
  }

  private async saveKnowledgeGraph(): Promise<void> {
    try {
      await this.memoryStore.store({
        type: MemoryType.SEMANTIC,
        agentId: this.id,
        content: {
          nodes: Array.from(this.knowledgeGraph.values()),
        },
        metadata: {
          tags: ['knowledge-graph'],
          importance: 1,
        },
      });
    } catch (error) {
      await this.log('Failed to save knowledge graph', { level: 'error', error });
    }
  }

  private async loadLearningPatterns(): Promise<void> {
    try {
      const patternsMemory = await this.memoryStore.retrieve({
        type: MemoryType.SEMANTIC,
        agentId: this.id,
        tags: ['learning-patterns'],
        limit: 1,
      });

      if (patternsMemory.length > 0) {
        const patterns = patternsMemory[0].content.patterns || [];
        for (const pattern of patterns) {
          this.learningPatterns.set(pattern.id || `pattern-${Date.now()}`, pattern);
        }
      }
    } catch (error) {
      await this.log('Failed to load learning patterns', { level: 'warn', error });
    }
  }

  async shutdown(): Promise<void> {
    // Save current state
    await this.saveKnowledgeGraph();
    
    // Save learning patterns
    try {
      await this.memoryStore.store({
        type: MemoryType.SEMANTIC,
        agentId: this.id,
        content: {
          patterns: Array.from(this.learningPatterns.entries()).map(([id, pattern]) => ({
            id,
            ...pattern,
          })),
        },
        metadata: {
          tags: ['learning-patterns'],
          importance: 1,
        },
      });
    } catch (error) {
      await this.log('Failed to save learning patterns', { level: 'error', error });
    }

    // Clear consolidation timer
    if (this.consolidationTimer) {
      clearInterval(this.consolidationTimer);
    }

    await super.shutdown();
  }
}

// Export MemoryItem type from infrastructure
export { MemoryItem } from '../infrastructure/memory-store';

// Helper to fix the type issue
const memoryItemSchema = z.object({
  id: z.string(),
  type: z.nativeEnum(MemoryType),
  agentId: z.string(),
  content: z.any(),
  metadata: z.object({
    created: z.date(),
    accessed: z.date(),
    accessCount: z.number(),
    importance: z.number().min(0).max(1),
    tags: z.array(z.string()),
  }),
  embedding: z.array(z.number()).optional(),
});