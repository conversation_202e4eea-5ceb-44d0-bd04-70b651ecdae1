// Frontend security utilities and configurations

import DOMPurify from 'dompurify';
import { api } from '../services/api';

// XSS Prevention
export function sanitizeHtml(dirty: string): string {
  return DOMPurify.sanitize(dirty, {
    ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'a', 'p', 'br'],
    ALLOWED_ATTR: ['href', 'title'],
  });
}

// Secure storage with encryption
export class SecureStorage {
  private static encrypt(data: string): string {
    // In production, use Web Crypto API
    // This is a simplified version
    return btoa(encodeURIComponent(data));
  }
  
  private static decrypt(data: string): string {
    try {
      return decodeURIComponent(atob(data));
    } catch {
      return '';
    }
  }
  
  static setItem<T>(key: string, value: T): void {
    const encrypted = this.encrypt(JSON.stringify(value));
    localStorage.setItem(key, encrypted);
  }
  
  static getItem<T>(key: string): T | null {
    const encrypted = localStorage.getItem(key);
    if (!encrypted) return null;
    
    try {
      const decrypted = this.decrypt(encrypted);
      return JSON.parse(decrypted);
    } catch {
      return null;
    }
  }
  
  static removeItem(key: string): void {
    localStorage.removeItem(key);
  }
  
  static clear(): void {
    localStorage.clear();
  }
}

// Session management
export class SessionManager {
  private static timeoutId: NodeJS.Timeout | null = null;
  private static warningTimeoutId: NodeJS.Timeout | null = null;
  
  static startSessionTimer(
    duration: number,
    onWarning: () => void,
    onExpire: () => void
  ): void {
    this.clearTimers();
    
    const warningTime = duration - 5 * 60 * 1000; // 5 minutes before expiry
    
    this.warningTimeoutId = setTimeout(() => {
      onWarning();
    }, warningTime);
    
    this.timeoutId = setTimeout(() => {
      onExpire();
    }, duration);
  }
  
  static resetTimer(): void {
    // Called on user activity
    const session = SecureStorage.getItem('session');
    if (session && session.duration) {
      this.startSessionTimer(
        session.duration,
        () => console.warn('Session expiring soon'),
        () => window.location.href = '/login'
      );
    }
  }
  
  static clearTimers(): void {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
    if (this.warningTimeoutId) {
      clearTimeout(this.warningTimeoutId);
      this.warningTimeoutId = null;
    }
  }
}

// CSRF token management
export class CsrfManager {
  private static token: string | null = null;
  
  static async getToken(): Promise<string> {
    if (!this.token) {
      const response = await api.get('/auth/csrf-token');
      this.token = response.data.token;
    }
    return this.token;
  }
  
  static clearToken(): void {
    this.token = null;
  }
  
  static async addTokenToRequest(config: any): Promise<any> {
    const token = await this.getToken();
    config.headers['X-CSRF-Token'] = token;
    return config;
  }
}

// Content Security Policy
export function setupCSP(): void {
  const meta = document.createElement('meta');
  meta.httpEquiv = 'Content-Security-Policy';
  meta.content = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https:",
    "font-src 'self'",
    "connect-src 'self' http://localhost:3001",
    "object-src 'none'",
    "frame-src 'none'",
    "base-uri 'self'",
    "form-action 'self'",
  ].join('; ');
  document.head.appendChild(meta);
}

// Input validation
export const Validators = {
  email: (email: string): boolean => {
    const re = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return re.test(email);
  },
  
  password: (password: string): { valid: boolean; errors: string[] } => {
    const errors: string[] = [];
    
    if (password.length < 12) {
      errors.push('Password must be at least 12 characters long');
    }
    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }
    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }
    if (!/[0-9]/.test(password)) {
      errors.push('Password must contain at least one number');
    }
    if (!/[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }
    
    return { valid: errors.length === 0, errors };
  },
  
  phone: (phone: string): boolean => {
    const cleaned = phone.replace(/\D/g, '');
    return cleaned.length >= 10 && cleaned.length <= 14;
  },
  
  ssn: (ssn: string): boolean => {
    const cleaned = ssn.replace(/\D/g, '');
    return cleaned.length === 9;
  },
  
  sanitizeInput: (input: string): string => {
    return input
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+\s*=/gi, '') // Remove event handlers
      .trim();
  },
};

// File upload security
export class FileUploadValidator {
  static readonly ALLOWED_TYPES = {
    image: ['image/jpeg', 'image/png', 'image/gif'],
    document: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
    spreadsheet: ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'text/csv'],
  };
  
  static readonly MAX_SIZE = 10 * 1024 * 1024; // 10MB
  
  static validate(
    file: File,
    allowedTypes: string[] = [...this.ALLOWED_TYPES.image, ...this.ALLOWED_TYPES.document]
  ): { valid: boolean; error?: string } {
    // Check file size
    if (file.size > this.MAX_SIZE) {
      return { valid: false, error: 'File size exceeds 10MB limit' };
    }
    
    // Check file type
    if (!allowedTypes.includes(file.type)) {
      return { valid: false, error: 'File type not allowed' };
    }
    
    // Check file extension
    const extension = file.name.split('.').pop()?.toLowerCase();
    const validExtensions = this.getValidExtensions(allowedTypes);
    
    if (!extension || !validExtensions.includes(extension)) {
      return { valid: false, error: 'Invalid file extension' };
    }
    
    // Check for double extensions
    if (file.name.split('.').length > 2) {
      return { valid: false, error: 'Multiple file extensions not allowed' };
    }
    
    return { valid: true };
  }
  
  private static getValidExtensions(mimeTypes: string[]): string[] {
    const extensions: string[] = [];
    
    mimeTypes.forEach(type => {
      switch (type) {
        case 'image/jpeg': extensions.push('jpg', 'jpeg'); break;
        case 'image/png': extensions.push('png'); break;
        case 'image/gif': extensions.push('gif'); break;
        case 'application/pdf': extensions.push('pdf'); break;
        case 'application/msword': extensions.push('doc'); break;
        case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document': extensions.push('docx'); break;
        case 'application/vnd.ms-excel': extensions.push('xls'); break;
        case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': extensions.push('xlsx'); break;
        case 'text/csv': extensions.push('csv'); break;
      }
    });
    
    return extensions;
  }
}

// Activity tracker for session timeout
export function setupActivityTracker(): void {
  let lastActivity = Date.now();
  const IDLE_TIMEOUT = 30 * 60 * 1000; // 30 minutes
  
  const updateActivity = () => {
    lastActivity = Date.now();
    SessionManager.resetTimer();
  };
  
  // Track user activity
  ['mousedown', 'keydown', 'scroll', 'touchstart'].forEach(event => {
    document.addEventListener(event, updateActivity, { passive: true });
  });
  
  // Check for idle timeout
  setInterval(() => {
    if (Date.now() - lastActivity > IDLE_TIMEOUT) {
      // Logout due to inactivity
      // Only clear auth-related items, not all localStorage
      localStorage.removeItem('auth-storage');
      SecureStorage.removeItem('session');
      window.location.href = '/login?timeout=true';
    }
  }, 60000); // Check every minute
}

// Secure API request interceptor
export function setupSecureApiInterceptor(): void {
  // Add CSRF token to requests
  api.interceptors.request.use(async (config) => {
    if (['post', 'put', 'delete', 'patch'].includes(config.method?.toLowerCase() || '')) {
      config = await CsrfManager.addTokenToRequest(config);
    }
    return config;
  });
  
  // Handle security responses
  api.interceptors.response.use(
    (response) => {
      // Update CSRF token if provided
      const newToken = response.headers['x-csrf-token'];
      if (newToken) {
        CsrfManager.token = newToken;
      }
      return response;
    },
    (error) => {
      if (error.response?.status === 403 && error.response?.data?.code === 'CSRF_TOKEN_INVALID') {
        // Clear token and retry
        CsrfManager.clearToken();
        return api.request(error.config);
      }
      return Promise.reject(error);
    }
  );
}