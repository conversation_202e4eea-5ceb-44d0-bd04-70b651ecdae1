# Quick Start Guide - Electrical Project

## 🚀 Fastest Way to Get Started

### Option 1: Automated Installation (Recommended)
```powershell
# Run from the /app directory
.\install-dependencies.ps1
```

This will:
- Clean up any existing node_modules
- Install pnpm if needed
- Install all dependencies
- Generate Prisma client
- Provide startup instructions

### Option 2: Manual Installation

#### Using pnpm (Recommended)
```powershell
# Install pnpm globally if not installed
npm install -g pnpm@8

# Install all dependencies
pnpm install

# Generate Prisma client
cd backend
npx prisma generate
cd ..
```

#### Using npm (Alternative)
```powershell
# Install dependencies
npm install

# Generate Prisma client
cd backend
npx prisma generate
cd ..
```

## 🏃 Starting the Services

### All Services at Once

#### PowerShell:
```powershell
.\start-all.ps1
```

#### Command Prompt:
```cmd
start-all.bat
```

#### Using pnpm:
```powershell
pnpm run dev:main
```

### Individual Services

#### Backend only:
```powershell
.\start-backend.ps1
# OR
cd backend && npm run dev
```

#### Frontend only:
```powershell
.\start-frontend.ps1
# OR
cd frontend && npm run dev
```

#### AI Agents:
```powershell
.\start-agents.ps1
# OR
cd agents && npm run dev
```

## 📍 Service URLs

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:3001
- **API Documentation**: http://localhost:3001/api-docs
- **Prisma Studio** (optional): http://localhost:5555

## 🗄️ Database Setup

If this is your first time running the project:

```powershell
cd backend

# Run migrations
npx prisma migrate deploy

# Seed the database (optional)
npm run db:seed

# Open Prisma Studio to view data (optional)
npx prisma studio
```

## 🔧 Troubleshooting

### "Cannot find module 'express'" Error
This means dependencies aren't installed properly. Run:
```powershell
.\install-dependencies.ps1
```

### Port Already in Use
If ports 3000 or 3001 are in use:
```powershell
# Find what's using the port
netstat -ano | findstr :3000
netstat -ano | findstr :3001

# Kill the process (replace PID with actual number)
taskkill /PID <PID> /F
```

### Permission Errors with PowerShell Scripts
```powershell
# Run this once to allow script execution
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### Dependencies Out of Sync
```powershell
# Clean everything and reinstall
Remove-Item -Recurse -Force node_modules, */node_modules
pnpm install
```

## 📝 Notes

- The project uses a monorepo structure with pnpm workspaces
- Each service (backend, frontend, agents) can be developed independently
- The shared package contains common types used across services
- Make sure Node.js 20+ is installed before starting