// Temporarily using mock TypeORM - will be replaced with proper database solution
// Note: Decorators removed for React Native compatibility

export abstract class BaseEntity {
  id: string;

  createdAt: Date;

  updatedAt: Date;

  version: number;

  isDeleted: boolean;

  syncStatus: 'pending' | 'synced' | 'conflict' | null;

  lastSyncedAt: string;

  remoteId: string;

  conflictData: string;
}