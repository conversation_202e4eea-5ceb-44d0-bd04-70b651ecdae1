import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Calculator, AlertCircle, FileText } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { api } from '../../services/api';
import { GENERAL_LIGHTING_LOADS } from '@electrical/shared';

// Define the result type based on what the API returns
interface LoadCalculationResult {
  general_lighting_va: number;
  small_appliance_va: number;
  laundry_va: number;
  appliance_loads?: Array<{ name: string; va: number; demand_factor: number }>;
  heating_cooling_va: number;
  largest_motor_va: number;
  motor_additional_va: number;
  other_loads_va: number;
  total_computed_va: number;
  demand_factor_applied: boolean;
  demand_factor: number;
  total_demand_va: number;
  required_amperage_240v: number;
  required_amperage_208v: number;
  recommended_service_240v: number;
  recommended_service_208v: number;
  necReferences: string[];
  calculation_steps?: string[];
}

const loadCalculationSchema = z.object({
  project_id: z.string().optional(),
  calculation_type: z.enum(['STANDARD', 'OPTIONAL', 'EXISTING']).default('STANDARD'),
  building_type: z.enum(Object.keys(GENERAL_LIGHTING_LOADS) as [keyof typeof GENERAL_LIGHTING_LOADS]),
  square_footage: z.coerce.number().positive('Square footage must be positive').int('Square footage must be a whole number'),
  small_appliance_circuits: z.coerce.number().int('Must be a whole number').min(2, 'Minimum 2 circuits required').default(2),
  laundry_circuit: z.boolean().default(true),
  heating_va: z.coerce.number().min(0, 'Cannot be negative').default(0),
  cooling_va: z.coerce.number().min(0, 'Cannot be negative').default(0),
  largest_motor_va: z.coerce.number().min(0, 'Cannot be negative').default(0),
  other_loads_va: z.coerce.number().min(0, 'Cannot be negative').default(0),
});

type LoadCalculationFormData = z.infer<typeof loadCalculationSchema>;

export function LoadCalculator() {
  const [isCalculating, setIsCalculating] = useState(false);
  const [result, setResult] = useState<LoadCalculationResult | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<LoadCalculationFormData>({
    resolver: zodResolver(loadCalculationSchema),
    defaultValues: {
      calculation_type: 'STANDARD',
      building_type: 'DWELLING',
      square_footage: undefined, // Let user enter this
      small_appliance_circuits: 2,
      laundry_circuit: true,
      heating_va: 0,
      cooling_va: 0,
      largest_motor_va: 0,
      other_loads_va: 0,
    },
  });

  const buildingType = watch('building_type');

  const onSubmit = async (data: LoadCalculationFormData) => {
    setIsCalculating(true);
    try {
      const response = await api.post('/calculations/load', data);
      setResult(response.data);
      toast.success('Load calculation completed');
    } catch (error) {
      const errorMessage = error instanceof Error 
        ? error.message 
        : (error as any).response?.data?.error?.message || 'Calculation failed';
      toast.error(errorMessage);
    } finally {
      setIsCalculating(false);
    }
  };

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white flex items-center">
          <Calculator className="mr-2 h-5 w-5" />
          Load Calculation (NEC Article 220)
        </h3>
      </div>
      <div className="card-body">
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Calculation Type
              </label>
              <select
                {...register('calculation_type')}
                className="mt-1 input"
              >
                <option value="STANDARD">Standard Method</option>
                <option value="OPTIONAL">Optional Method</option>
                <option value="EXISTING">Existing Dwelling</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Building Type
              </label>
              <select
                {...register('building_type')}
                className="mt-1 input"
              >
                {Object.entries(GENERAL_LIGHTING_LOADS).map(([key, value]) => (
                  <option key={key} value={key}>
                    {key.charAt(0) + key.slice(1).toLowerCase()} ({value} VA/sq ft)
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Square Footage
              </label>
              <input
                type="number"
                {...register('square_footage')}
                className="mt-1 input"
                placeholder="2500"
              />
              {errors.square_footage && (
                <p className="mt-1 text-sm text-red-600">{errors.square_footage.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Small Appliance Circuits
              </label>
              <input
                type="number"
                {...register('small_appliance_circuits')}
                className="mt-1 input"
                min="2"
              />
              <p className="mt-1 text-xs text-gray-500">Minimum 2 required (1500 VA each)</p>
              {errors.small_appliance_circuits && (
                <p className="mt-1 text-sm text-red-600">{errors.small_appliance_circuits.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Heating Load (VA)
              </label>
              <input
                type="number"
                {...register('heating_va')}
                className="mt-1 input"
                placeholder="0"
              />
              {errors.heating_va && (
                <p className="mt-1 text-sm text-red-600">{errors.heating_va.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Cooling Load (VA)
              </label>
              <input
                type="number"
                {...register('cooling_va')}
                className="mt-1 input"
                placeholder="0"
              />
              {errors.cooling_va && (
                <p className="mt-1 text-sm text-red-600">{errors.cooling_va.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Largest Motor (VA)
              </label>
              <input
                type="number"
                {...register('largest_motor_va')}
                className="mt-1 input"
                placeholder="0"
              />
              <p className="mt-1 text-xs text-gray-500">25% will be added per NEC 220.50</p>
              {errors.largest_motor_va && (
                <p className="mt-1 text-sm text-red-600">{errors.largest_motor_va.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Other Loads (VA)
              </label>
              <input
                type="number"
                {...register('other_loads_va')}
                className="mt-1 input"
                placeholder="0"
              />
              {errors.other_loads_va && (
                <p className="mt-1 text-sm text-red-600">{errors.other_loads_va.message}</p>
              )}
            </div>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              {...register('laundry_circuit')}
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
            <label className="ml-2 block text-sm text-gray-900 dark:text-gray-300">
              Include laundry circuit (1500 VA)
            </label>
          </div>

          <button
            type="submit"
            disabled={isCalculating}
            className="btn-primary w-full sm:w-auto"
          >
            {isCalculating ? (
              <>
                <div className="spinner mr-2" />
                Calculating...
              </>
            ) : (
              <>
                <Calculator className="mr-2 h-4 w-4" />
                Calculate Load
              </>
            )}
          </button>
        </form>

        {result && (
          <div className="mt-6 space-y-4">
            <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <AlertCircle className="h-5 w-5 text-green-400" />
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-green-800 dark:text-green-200">
                    Calculation Results
                  </h3>
                  <div className="mt-2 text-sm text-green-700 dark:text-green-300">
                    <dl className="space-y-1">
                      <div className="flex justify-between">
                        <dt>Total Computed Load:</dt>
                        <dd className="font-medium">{result.total_computed_va.toLocaleString()} VA</dd>
                      </div>
                      <div className="flex justify-between">
                        <dt>Total Demand Load:</dt>
                        <dd className="font-medium">{result.total_demand_va.toLocaleString()} VA</dd>
                      </div>
                      <div className="flex justify-between">
                        <dt>Required Amperage (240V):</dt>
                        <dd className="font-medium">{result.required_amperage_240v.toFixed(1)} A</dd>
                      </div>
                      <div className="flex justify-between">
                        <dt>Recommended Service (240V):</dt>
                        <dd className="font-bold text-lg">{result.recommended_service_240v} A</dd>
                      </div>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="border border-gray-200 dark:border-gray-700 rounded-md overflow-hidden">
              <div className="bg-gray-50 dark:bg-gray-800 px-4 py-2">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                  Calculation Breakdown
                </h4>
              </div>
              <div className="p-4">
                <dl className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <dt>General Lighting:</dt>
                    <dd>{result.general_lighting_va.toLocaleString()} VA</dd>
                  </div>
                  <div className="flex justify-between">
                    <dt>Small Appliances:</dt>
                    <dd>{result.small_appliance_va.toLocaleString()} VA</dd>
                  </div>
                  <div className="flex justify-between">
                    <dt>Laundry Circuit:</dt>
                    <dd>{result.laundry_va.toLocaleString()} VA</dd>
                  </div>
                  {result.heating_cooling_va > 0 && (
                    <div className="flex justify-between">
                      <dt>Heating/Cooling (larger):</dt>
                      <dd>{result.heating_cooling_va.toLocaleString()} VA</dd>
                    </div>
                  )}
                  {result.largest_motor_va > 0 && (
                    <div className="flex justify-between">
                      <dt>Largest Motor (+25%):</dt>
                      <dd>{(result.largest_motor_va + result.motor_additional_va).toLocaleString()} VA</dd>
                    </div>
                  )}
                  {result.demand_factor_applied && (
                    <div className="flex justify-between text-primary-600 dark:text-primary-400">
                      <dt>Demand Factor Applied:</dt>
                      <dd>{(result.demand_factor * 100).toFixed(0)}%</dd>
                    </div>
                  )}
                </dl>
              </div>
            </div>

            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md p-4">
              <div className="flex">
                <FileText className="h-5 w-5 text-blue-400 flex-shrink-0" />
                <div className="ml-3">
                  <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200">
                    NEC References
                  </h4>
                  <p className="mt-1 text-sm text-blue-700 dark:text-blue-300">
                    {result.necReferences.join(', ')}
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}