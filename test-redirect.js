const puppeteer = require('puppeteer');

async function testRedirect() {
  console.log('Starting redirect test...');
  
  const browser = await puppeteer.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    // Enable console logging
    page.on('console', msg => {
      const text = msg.text();
      if (text.includes('QuoteFormPage') || 
          text.includes('REDIRECT') || 
          text.includes('401') || 
          text.includes('API Interceptor') ||
          text.includes('ProtectedRoute') ||
          text.includes('Navigation') ||
          text.includes('Error') ||
          text.includes('Failed')) {
        console.log('Browser console:', text);
      }
    });
    
    // Navigate to login page
    console.log('1. Navigating to login page...');
    await page.goto('http://localhost:3000/login', { waitUntil: 'networkidle0' });
    await new <PERSON>(resolve => setTimeout(resolve, 2000)); // Wait to avoid rate limiting
    
    // Fill login form
    console.log('2. Filling login form...');
    await page.type('input[type="email"]', '<EMAIL>');
    await page.type('input[type="password"]', 'Test123!');
    
    // Submit form
    console.log('3. Submitting login...');
    await Promise.all([
      page.waitForNavigation({ waitUntil: 'networkidle0' }),
      page.click('button[type="submit"]')
    ]);
    
    // Check if logged in
    const currentUrl = page.url();
    console.log('4. After login, current URL:', currentUrl);
    
    // Get auth state from localStorage
    const authState = await page.evaluate(() => {
      const stored = localStorage.getItem('auth-storage');
      if (stored) {
        const parsed = JSON.parse(stored);
        return {
          hasToken: !!parsed.state?.accessToken,
          hasUser: !!parsed.state?.user,
          userEmail: parsed.state?.user?.email,
          userCompanyId: parsed.state?.user?.companyId,
          fullUser: parsed.state?.user
        };
      }
      return null;
    });
    console.log('5. Auth state after login:', JSON.stringify(authState, null, 2));
    
    // Navigate to /quotes/new
    console.log('6. Navigating to /quotes/new...');
    
    // Set up response monitoring
    const responses = [];
    page.on('response', response => {
      if (response.url().includes('/api/')) {
        responses.push({
          url: response.url(),
          status: response.status(),
          statusText: response.statusText()
        });
      }
    });
    
    await page.goto('http://localhost:3000/quotes/new', { waitUntil: 'networkidle0' });
    
    // Check final URL
    const finalUrl = page.url();
    console.log('7. Final URL:', finalUrl);
    console.log('8. API responses during navigation:', responses);
    
    if (finalUrl.includes('/quotes/new')) {
      console.log('✅ SUCCESS: Stayed on /quotes/new page');
    } else {
      console.log('❌ FAILED: Redirected away from /quotes/new');
    }
    
    // Get any error messages
    const errorElement = await page.$('.text-red-600');
    if (errorElement) {
      const errorText = await page.evaluate(el => el.textContent, errorElement);
      console.log('Error on page:', errorText);
    }
    
    // Take screenshot
    await page.screenshot({ path: 'redirect-test.png' });
    console.log('Screenshot saved as redirect-test.png');
    
  } catch (error) {
    console.error('Test error:', error);
  } finally {
    await browser.close();
  }
}

testRedirect();