{"name": "@electrical-contractor/mobile", "version": "1.0.0", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start --port 8081", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "type-check": "tsc --noEmit", "postinstall": "patch-package", "clean": "rm -rf node_modules && rm -rf ios/Pods && cd ios && pod install", "pod-install": "cd ios && pod install"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.21.0", "@react-native-community/netinfo": "^11.2.1", "@react-native-picker/picker": "^2.6.1", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/drawer": "^6.6.6", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "@reduxjs/toolkit": "^2.0.1", "@tanstack/react-query": "^5.17.9", "axios": "^1.6.5", "jail-monkey": "^2.8.0", "lz-string": "^1.5.0", "native-base": "^3.4.28", "react": "18.2.0", "react-native": "0.73.2", "react-native-background-fetch": "4.2.8", "react-native-background-timer": "^2.4.1", "react-native-biometrics": "^3.0.1", "react-native-crypto-js": "^1.0.0", "react-native-device-info": "^10.12.0", "react-native-encrypted-storage": "^4.0.3", "react-native-flag-secure-android": "^1.0.3", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "2.14.0", "react-native-image-picker": "^5.7.0", "react-native-keychain": "^8.1.2", "react-native-reanimated": "3.6.2", "react-native-safe-area-context": "^4.8.2", "react-native-screens": "3.29.0", "react-native-sqlite-storage": "6.0.1", "react-native-ssl-pinning": "^1.5.0", "react-native-svg": "^14.1.0", "react-native-vector-icons": "^10.0.3", "react-native-vision-camera": "^3.7.1", "react-native-web": "0.20.0", "react-redux": "^9.0.4", "redux-persist": "^6.0.0", "reflect-metadata": "^0.2.1", "typeorm": "^0.3.19", "uuid": "^9.0.1"}, "devDependencies": {"@babel/core": "^7.23.7", "@babel/preset-env": "^7.23.7", "@babel/runtime": "^7.27.6", "@react-native-community/cli": "^12.3.2", "@react-native-community/cli-platform-android": "^12.3.2", "@react-native-community/cli-platform-ios": "^12.3.2", "@react-native/babel-preset": "0.73.19", "@react-native/eslint-config": "0.73.2", "@react-native/metro-config": "0.73.3", "@react-native/typescript-config": "0.73.1", "@types/react": "^18.2.48", "@types/react-native": "^0.72.8", "@types/react-native-vector-icons": "^6.4.18", "@typescript-eslint/eslint-plugin": "^6.19.0", "@typescript-eslint/parser": "^6.19.0", "babel-jest": "^29.7.0", "babel-plugin-module-resolver": "5.0.2", "eslint": "^8.56.0", "jest": "^29.7.0", "metro-react-native-babel-preset": "0.73.9", "patch-package": "^8.0.0", "prettier": "^3.2.4", "react-native-dotenv": "^3.4.9", "react-test-renderer": "18.2.0", "typescript": "^5.3.3"}, "jest": {"preset": "react-native", "moduleFileExtensions": ["ts", "tsx", "js", "jsx", "json", "node"]}, "engines": {"node": ">=18"}}