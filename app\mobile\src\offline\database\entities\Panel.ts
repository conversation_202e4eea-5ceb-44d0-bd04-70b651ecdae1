// Temporarily using mock TypeORM - will be replaced with proper database solution
// Note: Decorators removed for React Native compatibility
import { BaseEntity } from './BaseEntity';
import { Project } from './Project';
import { Circuit } from './Circuit';

export class Panel extends BaseEntity {
  name: string;

  location: string;

  voltage: number;

  amperage: number;

  phases: number;

  type: 'main' | 'sub' | 'distribution';

  manufacturer: string;

  model: string;

  serialNumber: string;

  installationDate: string;

  lastInspectionDate: string;

  notes: string;

  qrCode: string;

  metadata: string; // JSON string

  projectId: string;

  project: Project;

  circuits: Circuit[];
}