import { BaseAgent, BaseAgentConfig, AgentCapability } from '../base/base-agent';
import { z } from 'zod';

// Coding task schemas
const codeGenerationSchema = z.object({
  type: z.enum(['function', 'component', 'api', 'calculation', 'validation']),
  specification: z.string(),
  language: z.enum(['typescript', 'javascript', 'python']).default('typescript'),
  framework: z.string().optional(),
  constraints: z.array(z.string()).optional(),
});

const codeAnalysisSchema = z.object({
  code: z.string(),
  analysisType: z.enum(['security', 'performance', 'quality', 'nec-compliance']),
  context: z.record(z.any()).optional(),
});

const extractParametersSchema = z.object({
  design: z.record(z.any()),
  targetFormat: z.enum(['calculation', 'validation', 'api']).default('calculation'),
});

export class CodingAgent extends BaseAgent {
  private codeTemplates: Map<string, string>;
  private necPatterns: Map<string, RegExp>;

  constructor(config: Omit<BaseAgentConfig, 'capabilities'>) {
    const capabilities: AgentCapability[] = [
      {
        name: 'generate-code',
        description: 'Generate code based on specifications',
        inputSchema: codeGenerationSchema,
      },
      {
        name: 'analyze-code',
        description: 'Analyze code for issues and improvements',
        inputSchema: codeAnalysisSchema,
      },
      {
        name: 'extract-calculation-parameters',
        description: 'Extract parameters from design for calculations',
        inputSchema: extractParametersSchema,
      },
      {
        name: 'optimize-calculation',
        description: 'Optimize electrical calculation algorithms',
      },
      {
        name: 'generate-tests',
        description: 'Generate test cases for electrical calculations',
      },
    ];

    super({
      ...config,
      capabilities,
    });

    // Initialize Maps in constructor to avoid transpilation issues
    this.codeTemplates = new Map();
    this.necPatterns = new Map();
  }

  protected async onInitialize(): Promise<void> {
    // Initialize code templates
    this.initializeTemplates();
    
    // Initialize NEC compliance patterns
    this.initializeNECPatterns();

    await this.log('Coding agent initialized', {
      level: 'info',
      templates: this.codeTemplates.size,
      patterns: this.necPatterns.size,
    });
  }

  protected async processTask(taskType: string, data: any): Promise<any> {
    switch (taskType) {
      case 'generate-code':
        return this.generateCode(data);
      case 'analyze-code':
        return this.analyzeCode(data);
      case 'extract-calculation-parameters':
        return this.extractParameters(data);
      case 'optimize-calculation':
        return this.optimizeCalculation(data);
      case 'generate-tests':
        return this.generateTests(data);
      default:
        throw new Error(`Unknown task type: ${taskType}`);
    }
  }

  // Generate code based on specifications
  private async generateCode(data: z.infer<typeof codeGenerationSchema>): Promise<any> {
    const { type, specification, language, framework, constraints } = data;

    let generatedCode = '';
    let explanation = '';
    let dependencies = [] as string[];

    switch (type) {
      case 'calculation':
        const calcResult = this.generateCalculationCode(specification, language);
        generatedCode = calcResult.code;
        explanation = calcResult.explanation;
        dependencies = calcResult.dependencies;
        break;

      case 'component':
        if (framework === 'react') {
          const compResult = this.generateReactComponent(specification);
          generatedCode = compResult.code;
          explanation = compResult.explanation;
          dependencies = compResult.dependencies;
        }
        break;

      case 'api':
        const apiResult = this.generateAPIEndpoint(specification, language);
        generatedCode = apiResult.code;
        explanation = apiResult.explanation;
        dependencies = apiResult.dependencies;
        break;

      case 'validation':
        const valResult = this.generateValidation(specification, language);
        generatedCode = valResult.code;
        explanation = valResult.explanation;
        break;
    }

    // Apply constraints if any
    if (constraints && constraints.length > 0) {
      generatedCode = this.applyConstraints(generatedCode, constraints);
    }

    // Store generated code pattern
    await this.storeKnowledge(
      {
        type,
        specification: specification.substring(0, 100),
        language,
        framework,
        linesOfCode: generatedCode.split('\n').length,
      },
      ['code', 'generated', type],
      0.5
    );

    return {
      code: generatedCode,
      explanation,
      dependencies,
      language,
      type,
    };
  }

  // Analyze code for various aspects
  private async analyzeCode(data: z.infer<typeof codeAnalysisSchema>): Promise<any> {
    const { code, analysisType, context } = data;

    const analysis = {
      type: analysisType,
      issues: [] as any[],
      suggestions: [] as string[],
      score: 100,
      metrics: {} as Record<string, any>,
    };

    switch (analysisType) {
      case 'security':
        this.analyzeSecurityIssues(code, analysis);
        break;
      case 'performance':
        this.analyzePerformance(code, analysis);
        break;
      case 'quality':
        this.analyzeCodeQuality(code, analysis);
        break;
      case 'nec-compliance':
        this.analyzeNECCompliance(code, analysis, context);
        break;
    }

    // Calculate final score
    analysis.score = Math.max(0, 100 - analysis.issues.length * 10);

    return analysis;
  }

  // Extract parameters from design
  private async extractParameters(data: z.infer<typeof extractParametersSchema>): Promise<any> {
    const { design, targetFormat } = data;

    const extracted = {
      parameters: {} as Record<string, any>,
      missingRequired: [] as string[],
      assumptions: [] as string[],
    };

    // Extract based on target format
    if (targetFormat === 'calculation') {
      // Extract electrical calculation parameters
      if (design.buildingType) {
        extracted.parameters.building_type = design.buildingType;
      }
      if (design.squareFootage) {
        extracted.parameters.square_footage = design.squareFootage;
      }
      if (design.voltage) {
        extracted.parameters.voltage = design.voltage;
      }
      if (design.phase) {
        extracted.parameters.phase = design.phase;
      }
      if (design.loads) {
        extracted.parameters.loads = this.parseLoads(design.loads);
      }

      // Check for missing required parameters
      const required = ['building_type', 'square_footage'];
      extracted.missingRequired = required.filter(param => !extracted.parameters[param]);

      // Add assumptions for missing optional parameters
      if (!extracted.parameters.voltage) {
        extracted.parameters.voltage = 120;
        extracted.assumptions.push('Assumed 120V single phase');
      }
    }

    return extracted;
  }

  // Initialize code templates
  private initializeTemplates(): void {
    // Load calculation template
    this.codeTemplates.set('calculation-ts', `
import { Decimal } from 'decimal.js';
import { z } from 'zod';

// Input validation schema
const inputSchema = z.object({
  {{INPUT_FIELDS}}
});

// {{CALCULATION_NAME}} calculation function
export function calculate{{CALCULATION_NAME}}(input: z.infer<typeof inputSchema>) {
  // Validate input
  const validated = inputSchema.parse(input);
  
  // Perform calculations using Decimal for precision
  {{CALCULATION_LOGIC}}
  
  // Return results
  return {
    {{OUTPUT_FIELDS}}
    necReferences: {{NEC_REFS}},
  };
}
`);

    // React component template
    this.codeTemplates.set('component-react', `
import React, { useState } from 'react';
import { {{IMPORTS}} } from '{{IMPORT_SOURCE}}';

interface {{COMPONENT_NAME}}Props {
  {{PROP_TYPES}}
}

export function {{COMPONENT_NAME}}({ {{PROPS}} }: {{COMPONENT_NAME}}Props) {
  {{STATE_HOOKS}}
  
  {{EVENT_HANDLERS}}
  
  return (
    {{JSX_CONTENT}}
  );
}
`);

    // API endpoint template
    this.codeTemplates.set('api-ts', `
import { Request, Response } from 'express';
import { z } from 'zod';
import { prisma } from '../database';

// Request validation
const requestSchema = z.object({
  {{REQUEST_SCHEMA}}
});

// {{ENDPOINT_NAME}} endpoint
export async function {{ENDPOINT_NAME}}(req: Request, res: Response) {
  try {
    // Validate request
    const data = requestSchema.parse(req.body);
    
    // Process request
    {{PROCESSING_LOGIC}}
    
    // Return response
    res.json({
      success: true,
      data: result,
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}
`);
  }

  // Initialize NEC compliance patterns
  private initializeNECPatterns(): void {
    // Pattern for checking ampacity calculations
    this.necPatterns.set('ampacity', /ampacity|current.*rating|conductor.*size/i);
    
    // Pattern for voltage drop
    this.necPatterns.set('voltage-drop', /voltage.*drop|vd.*percent/i);
    
    // Pattern for grounding
    this.necPatterns.set('grounding', /ground|gnd|egc|grounding.*conductor/i);
    
    // Pattern for GFCI/AFCI
    this.necPatterns.set('protection', /gfci|afci|ground.*fault|arc.*fault/i);
  }

  // Generate calculation code
  private generateCalculationCode(specification: string, language: string): any {
    const template = this.codeTemplates.get(`calculation-${language}`) || '';
    
    // Parse specification to identify calculation type
    const isLoadCalc = specification.toLowerCase().includes('load');
    const isVoltageCalc = specification.toLowerCase().includes('voltage');
    
    let code = template;
    
    if (isLoadCalc) {
      code = code.replace('{{CALCULATION_NAME}}', 'LoadRequirements');
      code = code.replace('{{INPUT_FIELDS}}', `
  building_type: z.enum(['DWELLING', 'OFFICE', 'STORE', 'WAREHOUSE']),
  square_footage: z.number().positive(),
  small_appliance_circuits: z.number().int().min(2).default(2),
  laundry_circuit: z.boolean().default(true),
      `);
      code = code.replace('{{CALCULATION_LOGIC}}', `
  const lightingLoad = new Decimal(validated.square_footage).times(LIGHTING_LOADS[validated.building_type]);
  const smallApplianceLoad = new Decimal(validated.small_appliance_circuits).times(1500);
  const laundryLoad = validated.laundry_circuit ? new Decimal(1500) : new Decimal(0);
  
  const totalLoad = lightingLoad.plus(smallApplianceLoad).plus(laundryLoad);
      `);
      code = code.replace('{{OUTPUT_FIELDS}}', `
    lightingLoad: lightingLoad.toNumber(),
    smallApplianceLoad: smallApplianceLoad.toNumber(),
    laundryLoad: laundryLoad.toNumber(),
    totalLoad: totalLoad.toNumber(),
      `);
      code = code.replace('{{NEC_REFS}}', "['220.12', '220.52', '220.53']");
    }
    
    return {
      code,
      explanation: 'Generated NEC-compliant calculation function with input validation and precise decimal arithmetic',
      dependencies: ['decimal.js', 'zod'],
    };
  }

  // Generate React component
  private generateReactComponent(specification: string): any {
    const template = this.codeTemplates.get('component-react') || '';
    
    // Simple parser for component specification
    const isCalculator = specification.toLowerCase().includes('calculator');
    const isForm = specification.toLowerCase().includes('form');
    
    let code = template;
    
    if (isCalculator) {
      code = code.replace('{{COMPONENT_NAME}}', 'ElectricalCalculator');
      code = code.replace('{{IMPORTS}}', 'Calculator, AlertCircle');
      code = code.replace('{{IMPORT_SOURCE}}', 'lucide-react');
      code = code.replace('{{PROP_TYPES}}', `
  onCalculate: (result: any) => void;
  initialValues?: any;
      `);
      code = code.replace('{{STATE_HOOKS}}', `
  const [values, setValues] = useState(initialValues || {});
  const [result, setResult] = useState(null);
  const [loading, setLoading] = useState(false);
      `);
      code = code.replace('{{EVENT_HANDLERS}}', `
  const handleCalculate = async () => {
    setLoading(true);
    try {
      const result = await calculateElectricalLoad(values);
      setResult(result);
      onCalculate(result);
    } catch (error) {
      console.error('Calculation failed:', error);
    } finally {
      setLoading(false);
    }
  };
      `);
      code = code.replace('{{JSX_CONTENT}}', `
    <div className="calculator-container">
      <h2 className="text-xl font-bold mb-4">Electrical Load Calculator</h2>
      {/* Form inputs */}
      <button onClick={handleCalculate} disabled={loading}>
        <Calculator className="mr-2" />
        Calculate
      </button>
      {result && (
        <div className="result-container">
          {/* Display results */}
        </div>
      )}
    </div>
      `);
    }
    
    return {
      code,
      explanation: 'Generated React component with TypeScript, hooks, and proper event handling',
      dependencies: ['react', 'lucide-react'],
    };
  }

  // Generate API endpoint
  private generateAPIEndpoint(specification: string, language: string): any {
    const template = this.codeTemplates.get(`api-${language}`) || '';
    
    // Parse specification
    const isCalculation = specification.toLowerCase().includes('calculation');
    const isCRUD = specification.toLowerCase().includes('create') || 
                   specification.toLowerCase().includes('update');
    
    let code = template;
    
    if (isCalculation) {
      code = code.replace('{{ENDPOINT_NAME}}', 'calculateElectricalLoad');
      code = code.replace('{{REQUEST_SCHEMA}}', `
  projectId: z.string().optional(),
  buildingType: z.string(),
  squareFootage: z.number().positive(),
  additionalLoads: z.array(z.object({
    description: z.string(),
    watts: z.number(),
  })).optional(),
      `);
      code = code.replace('{{PROCESSING_LOGIC}}', `
    // Perform calculation
    const result = await calculateLoadService.execute(data);
    
    // Store in database if project ID provided
    if (data.projectId) {
      await prisma.calculationLog.create({
        data: {
          project_id: data.projectId,
          type: 'LOAD_CALCULATION',
          input: data,
          output: result,
          nec_references: result.necReferences,
        },
      });
    }
      `);
    }
    
    return {
      code,
      explanation: 'Generated Express API endpoint with validation, error handling, and database integration',
      dependencies: ['express', 'zod', '@prisma/client'],
    };
  }

  // Generate validation code
  private generateValidation(specification: string, language: string): any {
    // Parse what needs validation
    const isElectrical = specification.toLowerCase().includes('electrical');
    const isNEC = specification.toLowerCase().includes('nec');
    
    let code = '';
    
    if (isElectrical) {
      code = `
import { z } from 'zod';

// Electrical value validations
export const electricalSchema = z.object({
  voltage: z.number().positive().max(600),
  current: z.number().positive(),
  power: z.number().positive(),
  powerFactor: z.number().min(0).max(1).default(0.9),
  phase: z.enum(['SINGLE', 'THREE']),
});

// Wire size validation
export const wireSizeSchema = z.enum([
  '14', '12', '10', '8', '6', '4', '3', '2', '1',
  '1/0', '2/0', '3/0', '4/0', '250', '300', '350',
  '400', '500', '600', '750', '1000'
]);

// Validate electrical parameters
export function validateElectricalParams(params: any) {
  try {
    return { valid: true, data: electricalSchema.parse(params) };
  } catch (error) {
    return { valid: false, errors: error.errors };
  }
}
`;
    }
    
    return {
      code,
      explanation: 'Generated validation schemas with proper constraints for electrical values',
    };
  }

  // Apply constraints to generated code
  private applyConstraints(code: string, constraints: string[]): string {
    let modifiedCode = code;
    
    constraints.forEach(constraint => {
      if (constraint === 'no-console') {
        modifiedCode = modifiedCode.replace(/console\.\w+\([^)]*\);?/g, '');
      }
      if (constraint === 'use-decimal') {
        modifiedCode = modifiedCode.replace(/(\d+\.?\d*)\s*[+\-*/]/g, 'new Decimal($1)');
      }
      if (constraint === 'add-comments') {
        // Add comments before functions
        modifiedCode = modifiedCode.replace(/^(export\s+function)/gm, '// TODO: Add function documentation\n$1');
      }
    });
    
    return modifiedCode;
  }

  // Analyze security issues
  private analyzeSecurityIssues(code: string, analysis: any): void {
    // Check for SQL injection vulnerabilities
    if (code.includes('query') && !code.includes('prepare')) {
      analysis.issues.push({
        type: 'sql-injection',
        severity: 'HIGH',
        line: this.findLineNumber(code, 'query'),
        message: 'Potential SQL injection - use prepared statements',
      });
    }
    
    // Check for hardcoded credentials
    const credentialPattern = /password\s*=\s*["'][^"']+["']/i;
    if (credentialPattern.test(code)) {
      analysis.issues.push({
        type: 'hardcoded-credentials',
        severity: 'CRITICAL',
        message: 'Hardcoded credentials detected',
      });
    }
    
    // Check for missing input validation
    if (code.includes('req.body') && !code.includes('validate') && !code.includes('parse')) {
      analysis.issues.push({
        type: 'missing-validation',
        severity: 'MEDIUM',
        message: 'Missing input validation',
      });
      analysis.suggestions.push('Add input validation using Zod or similar library');
    }
  }

  // Analyze performance
  private analyzePerformance(code: string, analysis: any): void {
    // Check for nested loops
    const nestedLoops = (code.match(/for.*{[\s\S]*?for/g) || []).length;
    if (nestedLoops > 0) {
      analysis.issues.push({
        type: 'nested-loops',
        severity: 'MEDIUM',
        count: nestedLoops,
        message: `Found ${nestedLoops} nested loops - consider optimization`,
      });
    }
    
    // Check for synchronous file operations
    if (code.includes('readFileSync') || code.includes('writeFileSync')) {
      analysis.issues.push({
        type: 'sync-operations',
        severity: 'HIGH',
        message: 'Synchronous file operations block event loop',
      });
      analysis.suggestions.push('Use async file operations');
    }
    
    // Check for missing caching
    if (code.includes('database') && !code.includes('cache')) {
      analysis.suggestions.push('Consider adding caching for database queries');
    }
    
    // Calculate complexity metrics
    analysis.metrics.linesOfCode = code.split('\n').length;
    analysis.metrics.cyclomaticComplexity = this.calculateComplexity(code);
  }

  // Analyze code quality
  private analyzeCodeQuality(code: string, analysis: any): void {
    // Check for proper error handling
    const tryBlocks = (code.match(/try\s*{/g) || []).length;
    const catchBlocks = (code.match(/catch/g) || []).length;
    if (tryBlocks !== catchBlocks) {
      analysis.issues.push({
        type: 'error-handling',
        severity: 'MEDIUM',
        message: 'Incomplete error handling',
      });
    }
    
    // Check for magic numbers
    const magicNumbers = code.match(/[^0-9][0-9]{2,}(?![0-9])/g) || [];
    if (magicNumbers.length > 3) {
      analysis.issues.push({
        type: 'magic-numbers',
        severity: 'LOW',
        count: magicNumbers.length,
        message: 'Multiple magic numbers - consider using constants',
      });
    }
    
    // Check for function length
    const functions = code.match(/function.*?\{[\s\S]*?\n\}/g) || [];
    functions.forEach((func, index) => {
      const lines = func.split('\n').length;
      if (lines > 50) {
        analysis.issues.push({
          type: 'long-function',
          severity: 'MEDIUM',
          functionIndex: index,
          lines,
          message: `Function is ${lines} lines - consider splitting`,
        });
      }
    });
    
    // Check naming conventions
    const camelCaseViolations = code.match(/\b[a-z]+_[a-z]+\b/g) || [];
    if (camelCaseViolations.length > 0) {
      analysis.suggestions.push('Use camelCase for variable names in TypeScript/JavaScript');
    }
  }

  // Analyze NEC compliance
  private analyzeNECCompliance(code: string, analysis: any, context?: any): void {
    // Check for proper ampacity calculations
    if (this.necPatterns.get('ampacity')?.test(code)) {
      if (!code.includes('temperature') || !code.includes('derating')) {
        analysis.issues.push({
          type: 'nec-compliance',
          severity: 'HIGH',
          reference: 'NEC 310.15',
          message: 'Ampacity calculations must include temperature and bundling derating',
        });
      }
    }
    
    // Check voltage drop calculations
    if (this.necPatterns.get('voltage-drop')?.test(code)) {
      if (!code.includes('3') && !code.includes('5')) {
        analysis.suggestions.push('Include NEC recommended voltage drop limits (3% branch, 5% total)');
      }
    }
    
    // Check grounding calculations
    if (this.necPatterns.get('grounding')?.test(code)) {
      if (!code.includes('250.122') && !code.includes('Table')) {
        analysis.issues.push({
          type: 'nec-compliance',
          severity: 'MEDIUM',
          reference: 'NEC 250.122',
          message: 'Ground conductor sizing should reference Table 250.122',
        });
      }
    }
    
    // Check for required safety calculations
    if (context?.type === 'panel' && !code.includes('arc flash')) {
      analysis.suggestions.push('Consider adding arc flash calculations for panel work');
    }
  }

  // Optimize calculation algorithms
  private async optimizeCalculation(data: any): Promise<any> {
    const { algorithm, constraints } = data;
    
    const optimizations = {
      original: algorithm,
      optimized: '',
      improvements: [] as string[],
      performanceGain: 0,
    };
    
    // Apply common optimizations
    let optimized = algorithm;
    
    // Cache repeated calculations
    if (algorithm.includes('Math.sqrt') || algorithm.includes('Math.pow')) {
      optimized = this.addCaching(optimized);
      optimizations.improvements.push('Added caching for expensive math operations');
      optimizations.performanceGain += 15;
    }
    
    // Use lookup tables for common values
    if (algorithm.includes('ampacity') || algorithm.includes('wire size')) {
      optimized = this.addLookupTables(optimized);
      optimizations.improvements.push('Replaced calculations with lookup tables');
      optimizations.performanceGain += 25;
    }
    
    // Optimize loops
    if (algorithm.includes('for') || algorithm.includes('while')) {
      optimized = this.optimizeLoops(optimized);
      optimizations.improvements.push('Optimized loop performance');
      optimizations.performanceGain += 10;
    }
    
    optimizations.optimized = optimized;
    
    return optimizations;
  }

  // Generate test cases
  private async generateTests(data: any): Promise<any> {
    const { functionName, functionCode, testFramework = 'jest' } = data;
    
    // Analyze function to understand inputs/outputs
    const analysis = this.analyzeFunctionSignature(functionCode);
    
    let testCode = '';
    
    if (testFramework === 'jest') {
      testCode = `
import { ${functionName} } from './calculations';

describe('${functionName}', () => {
  // Test valid inputs
  it('should calculate correctly with typical values', () => {
    const input = ${JSON.stringify(analysis.sampleInput, null, 2)};
    const result = ${functionName}(input);
    
    expect(result).toBeDefined();
    expect(result.total).toBeGreaterThan(0);
    expect(result.necReferences).toContain('220.12');
  });
  
  // Test edge cases
  it('should handle minimum values', () => {
    const input = ${JSON.stringify(analysis.minInput, null, 2)};
    const result = ${functionName}(input);
    
    expect(result.total).toBeGreaterThanOrEqual(0);
  });
  
  // Test validation
  it('should throw error for invalid input', () => {
    const invalidInput = { ...${JSON.stringify(analysis.sampleInput)}, voltage: -120 };
    
    expect(() => ${functionName}(invalidInput)).toThrow();
  });
  
  // Test NEC compliance
  it('should meet NEC requirements', () => {
    const input = ${JSON.stringify(analysis.necTestInput, null, 2)};
    const result = ${functionName}(input);
    
    // Verify minimum load requirements
    const minRequired = input.square_footage * 3; // 3 VA/sq ft minimum
    expect(result.generalLightingLoad).toBeGreaterThanOrEqual(minRequired);
  });
});
`;
    }
    
    return {
      testCode,
      testCases: 4,
      coverage: 'basic',
      framework: testFramework,
    };
  }

  // Helper methods
  
  private findLineNumber(code: string, search: string): number {
    const lines = code.split('\n');
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].includes(search)) {
        return i + 1;
      }
    }
    return 0;
  }
  
  private calculateComplexity(code: string): number {
    // Simplified cyclomatic complexity calculation
    let complexity = 1;
    
    const decisionPoints = [
      /if\s*\(/g,
      /else\s+if\s*\(/g,
      /while\s*\(/g,
      /for\s*\(/g,
      /case\s+/g,
      /catch\s*\(/g,
    ];
    
    decisionPoints.forEach(pattern => {
      const matches = code.match(pattern) || [];
      complexity += matches.length;
    });
    
    return complexity;
  }
  
  private addCaching(code: string): string {
    // Add simple caching
    const cacheCode = `
// Cache for expensive calculations
const calculationCache = new Map();

function getCached(key, calculator) {
  if (calculationCache.has(key)) {
    return calculationCache.get(key);
  }
  const result = calculator();
  calculationCache.set(key, result);
  return result;
}
`;
    
    return cacheCode + '\n' + code;
  }
  
  private addLookupTables(code: string): string {
    // Replace calculations with lookups
    return code.replace(
      /calculateAmpacity\([^)]+\)/g,
      'AMPACITY_TABLE[wireSize]'
    );
  }
  
  private optimizeLoops(code: string): string {
    // Move invariants out of loops
    return code.replace(
      /for\s*\([^)]+\)\s*{([^}]+)}/g,
      (match, body) => {
        // Simple optimization - would be more complex in reality
        return match.replace(/\.length/g, 'Length');
      }
    );
  }
  
  private analyzeFunctionSignature(code: string): any {
    // Extract function parameters and generate test data
    const params = code.match(/function.*?\(([^)]*)\)/)?.[1] || '';
    
    return {
      sampleInput: {
        building_type: 'DWELLING',
        square_footage: 2000,
        voltage: 120,
        phase: 'SINGLE',
      },
      minInput: {
        building_type: 'DWELLING',
        square_footage: 100,
        voltage: 120,
        phase: 'SINGLE',
      },
      necTestInput: {
        building_type: 'DWELLING',
        square_footage: 1500,
        small_appliance_circuits: 2,
        laundry_circuit: true,
      },
    };
  }
  
  private parseLoads(loads: any): any[] {
    if (Array.isArray(loads)) {
      return loads;
    }
    
    if (typeof loads === 'string') {
      // Parse text description of loads
      const parsed = [];
      const patterns = [
        /(\d+)\s*hp/gi,  // Motor loads
        /(\d+)\s*kw/gi,  // kW loads
        /(\d+)\s*amp/gi, // Amp loads
      ];
      
      patterns.forEach(pattern => {
        const matches = loads.matchAll(pattern);
        for (const match of matches) {
          parsed.push({
            value: parseInt(match[1]),
            unit: match[0].replace(/\d+\s*/, ''),
          });
        }
      });
      
      return parsed;
    }
    
    return [];
  }
}