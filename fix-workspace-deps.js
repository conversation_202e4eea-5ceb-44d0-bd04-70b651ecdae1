// Fix workspace dependencies by creating proper node_modules structure
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');

async function runCommand(command, cwd) {
  return new Promise((resolve) => {
    console.log(`Running: ${command} in ${cwd}`);
    exec(command, { cwd }, (error, stdout, stderr) => {
      if (error) {
        console.error(`Error: ${error.message}`);
        resolve({ success: false, error: error.message });
      } else {
        console.log(stdout);
        if (stderr) console.error(stderr);
        resolve({ success: true, stdout });
      }
    });
  });
}

async function createTempPackageJson() {
  const backendDir = path.join(__dirname, 'app', 'backend');
  const originalPackageJson = path.join(backendDir, 'package.json');
  const tempPackageJson = path.join(backendDir, 'package-temp.json');
  
  console.log('📝 Creating temporary package.json without workspace references...');
  
  try {
    // Read original package.json
    const packageData = JSON.parse(fs.readFileSync(originalPackageJson, 'utf8'));
    
    // Remove workspace references and problematic dependencies
    const cleanPackage = {
      ...packageData,
      name: "electrical-backend-temp",
      private: false
    };
    
    // Remove any workspace: dependencies if they exist
    if (cleanPackage.dependencies) {
      Object.keys(cleanPackage.dependencies).forEach(dep => {
        if (cleanPackage.dependencies[dep].startsWith('workspace:')) {
          delete cleanPackage.dependencies[dep];
        }
      });
    }
    
    // Write temporary package.json
    fs.writeFileSync(tempPackageJson, JSON.stringify(cleanPackage, null, 2));
    
    // Backup original and replace
    fs.renameSync(originalPackageJson, path.join(backendDir, 'package-original.json'));
    fs.renameSync(tempPackageJson, originalPackageJson);
    
    console.log('✅ Temporary package.json created');
    return true;
  } catch (error) {
    console.error('❌ Failed to create temporary package.json:', error.message);
    return false;
  }
}

async function restorePackageJson() {
  const backendDir = path.join(__dirname, 'app', 'backend');
  const originalPackageJson = path.join(backendDir, 'package.json');
  const backupPackageJson = path.join(backendDir, 'package-original.json');
  
  try {
    if (fs.existsSync(backupPackageJson)) {
      fs.renameSync(originalPackageJson, path.join(backendDir, 'package-temp.json'));
      fs.renameSync(backupPackageJson, originalPackageJson);
      console.log('✅ Original package.json restored');
    }
  } catch (error) {
    console.error('❌ Failed to restore package.json:', error.message);
  }
}

async function installDependencies() {
  const backendDir = path.join(__dirname, 'app', 'backend');
  
  console.log('📦 Installing dependencies with npm...');
  
  // Remove existing node_modules
  const nodeModulesPath = path.join(backendDir, 'node_modules');
  if (fs.existsSync(nodeModulesPath)) {
    console.log('Removing existing node_modules...');
    await runCommand('rmdir /s /q node_modules', backendDir);
  }
  
  // Install with npm
  const result = await runCommand('npm install --no-package-lock --legacy-peer-deps', backendDir);
  return result.success;
}

async function startBackend() {
  const backendDir = path.join(__dirname, 'app', 'backend');
  
  console.log('🚀 Starting backend server...');
  
  // Try npx tsx first
  console.log('Trying npx tsx watch src/index.ts...');
  const tsxResult = await runCommand('npx tsx watch src/index.ts', backendDir);
  
  if (tsxResult.success) {
    console.log('✅ Backend started with tsx!');
    return true;
  }
  
  // Try npm run dev
  console.log('Trying npm run dev...');
  const devResult = await runCommand('npm run dev', backendDir);
  
  if (devResult.success) {
    console.log('✅ Backend started with npm run dev!');
    return true;
  }
  
  return false;
}

async function main() {
  try {
    console.log('🔧 Fixing workspace dependencies for backend...');
    
    // Step 1: Create temporary package.json
    const packageCreated = await createTempPackageJson();
    if (!packageCreated) {
      console.log('❌ Failed to create temporary package.json');
      return;
    }
    
    // Step 2: Install dependencies
    const depsInstalled = await installDependencies();
    if (!depsInstalled) {
      console.log('❌ Failed to install dependencies');
      await restorePackageJson();
      return;
    }
    
    console.log('✅ Dependencies installed successfully!');
    console.log('');
    console.log('🚀 Now you can start the backend with:');
    console.log('   npx tsx watch src/index.ts');
    console.log('   or');
    console.log('   npm run dev');
    console.log('');
    console.log('📝 To restore original package.json later, run:');
    console.log('   node ../../restore-package.js');
    
    // Create restore script
    const restoreScript = `
// Restore original package.json
const fs = require('fs');
const path = require('path');

const backendDir = path.join(__dirname, 'app', 'backend');
const originalPackageJson = path.join(backendDir, 'package.json');
const backupPackageJson = path.join(backendDir, 'package-original.json');

try {
  if (fs.existsSync(backupPackageJson)) {
    fs.renameSync(originalPackageJson, path.join(backendDir, 'package-temp.json'));
    fs.renameSync(backupPackageJson, originalPackageJson);
    console.log('✅ Original package.json restored');
  } else {
    console.log('❌ No backup found');
  }
} catch (error) {
  console.error('❌ Failed to restore package.json:', error.message);
}
`;
    
    fs.writeFileSync(path.join(__dirname, 'restore-package.js'), restoreScript);
    
  } catch (error) {
    console.error('Main execution error:', error);
    await restorePackageJson();
  }
}

main();
