import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { api } from '../../services/api';
import { formatCurrency, formatDate } from '../../utils/format';
import { 
  ArrowLeftIcon, 
  PencilIcon, 
  DocumentDuplicateIcon,
  EnvelopeIcon,
  DocumentArrowDownIcon,
  CheckCircleIcon,
  XCircleIcon,
  BriefcaseIcon,
  DocumentTextIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';

interface QuoteDetail {
  id: string;
  quote_number: string;
  name: string;
  customer_id: string;
  project_id: string;
  status: string;
  project_overview?: string;
  scope_of_work?: string;
  materials_included?: string;
  exclusions?: string;
  terms_and_conditions?: string;
  items: any[];
  subtotal: number;
  tax_rate: number;
  tax_amount: number;
  discount: number;
  discount_type: string;
  total: number;
  expires_at?: string;
  created_at: string;
  sent_at?: string;
  viewed_at?: string;
  approved_at?: string;
  rejected_at?: string;
  customer_notes?: string;
  internal_notes?: string;
  customer?: {
    id: string;
    name: string;
    email: string;
    phone: string;
  };
  project?: {
    id: string;
    name: string;
    address: string;
    city: string;
    state: string;
    zip: string;
  };
  created_by?: {
    name: string;
    email: string;
  };
}

export const QuoteDetailPage: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [quote, setQuote] = useState<QuoteDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [sendLoading, setSendLoading] = useState(false);
  const [pdfLoading, setPdfLoading] = useState(false);
  const [showSendModal, setShowSendModal] = useState(false);
  const [emailData, setEmailData] = useState({
    to: '',
    cc: '',
    subject: '',
    message: '',
  });

  useEffect(() => {
    if (id) {
      loadQuote();
    }
  }, [id]);

  const loadQuote = async () => {
    try {
      setLoading(true);
      const response = await api.get(`/quotes/${id}`);
      setQuote(response.data);
      
      // Set default email data
      if (response.data.customer?.email) {
        setEmailData({
          to: response.data.customer.email,
          cc: '',
          subject: `Quote ${response.data.quote_number} - ${response.data.name}`,
          message: `Dear ${response.data.customer.name},\n\nPlease find attached our quote for ${response.data.name}.\n\nThis quote is valid until ${response.data.expires_at ? formatDate(response.data.expires_at) : '30 days from today'}.\n\nPlease don't hesitate to contact us if you have any questions.\n\nBest regards,\n${response.data.created_by?.name || 'Your Electrical Team'}`,
        });
      }
    } catch (error) {
      toast.error('Failed to load quote');
      navigate('/quotes');
    } finally {
      setLoading(false);
    }
  };

  const handleDuplicate = async () => {
    if (!quote) return;
    
    try {
      const response = await api.post(`/quotes/${quote.id}/duplicate`, {
        name: `${quote.name} (Copy)`,
      });
      toast.success('Quote duplicated successfully');
      navigate(`/quotes/${response.data.id}/edit`);
    } catch (error) {
      toast.error('Failed to duplicate quote');
    }
  };

  const handleSendQuote = async () => {
    try {
      setSendLoading(true);
      await api.post(`/quotes/${id}/send`, emailData);
      toast.success('Quote sent successfully');
      setShowSendModal(false);
      loadQuote(); // Reload to get updated status
    } catch (error) {
      toast.error('Failed to send quote');
    } finally {
      setSendLoading(false);
    }
  };

  const handleDownloadPDF = async () => {
    try {
      setPdfLoading(true);
      const response = await api.get(`/quotes/${id}/pdf`, {
        responseType: 'blob',
      });
      
      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `quote-${quote?.quote_number}.pdf`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
      
      toast.success('PDF downloaded successfully');
    } catch (error) {
      toast.error('Failed to download PDF');
    } finally {
      setPdfLoading(false);
    }
  };

  const handleConvertToJob = async () => {
    if (!quote || quote.status !== 'APPROVED') {
      toast.error('Quote must be approved before converting to job');
      return;
    }
    
    // Navigate to job creation with quote data
    navigate('/jobs/new', { state: { fromQuote: quote } });
  };

  const handleConvertToEstimate = async () => {
    try {
      const response = await api.post(`/quotes/${id}/convert-to-estimate`);
      toast.success('Quote converted to estimate successfully');
      navigate(`/estimates/${response.data.id}`);
    } catch (error) {
      toast.error('Failed to convert to estimate');
    }
  };

  const handleRefreshPrices = async () => {
    try {
      await api.post(`/quotes/${id}/refresh-prices`, { force: true });
      toast.success('Price refresh initiated');
      loadQuote();
    } catch (error) {
      toast.error('Failed to refresh prices');
    }
  };

  const getStatusBadge = (status: string) => {
    const badges = {
      DRAFT: { bg: 'bg-gray-100', text: 'text-gray-800', icon: null },
      SENT: { bg: 'bg-blue-100', text: 'text-blue-800', icon: EnvelopeIcon },
      VIEWED: { bg: 'bg-purple-100', text: 'text-purple-800', icon: null },
      APPROVED: { bg: 'bg-green-100', text: 'text-green-800', icon: CheckCircleIcon },
      REJECTED: { bg: 'bg-red-100', text: 'text-red-800', icon: XCircleIcon },
      EXPIRED: { bg: 'bg-orange-100', text: 'text-orange-800', icon: null },
      CONVERTED: { bg: 'bg-indigo-100', text: 'text-indigo-800', icon: BriefcaseIcon }
    };
    
    return badges[status as keyof typeof badges] || { bg: 'bg-gray-100', text: 'text-gray-800', icon: null };
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!quote) {
    return null;
  }

  const statusBadge = getStatusBadge(quote.status);

  return (
    <div className="px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <button
          onClick={() => navigate('/quotes')}
          className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
        >
          <ArrowLeftIcon className="h-4 w-4 mr-1" />
          Back to Quotes
        </button>
        
        <div className="mt-2 md:flex md:items-center md:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Quote {quote.quote_number}
            </h1>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              {quote.name}
            </p>
          </div>
          
          <div className="mt-4 flex flex-wrap gap-2 md:mt-0">
            <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${statusBadge.bg} ${statusBadge.text}`}>
              {statusBadge.icon && <statusBadge.icon className="h-4 w-4 mr-1" />}
              {quote.status}
            </span>
            
            {quote.status === 'DRAFT' && (
              <>
                <button
                  onClick={() => navigate(`/quotes/${id}/edit`)}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
                >
                  <PencilIcon className="h-4 w-4 mr-1" />
                  Edit
                </button>
                <button
                  onClick={() => setShowSendModal(true)}
                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                >
                  <EnvelopeIcon className="h-4 w-4 mr-1" />
                  Send Quote
                </button>
              </>
            )}
            
            <button
              onClick={handleDownloadPDF}
              disabled={pdfLoading}
              className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
            >
              <DocumentArrowDownIcon className="h-4 w-4 mr-1" />
              {pdfLoading ? 'Generating...' : 'Download PDF'}
            </button>
            
            <button
              onClick={handleDuplicate}
              className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
            >
              <DocumentDuplicateIcon className="h-4 w-4 mr-1" />
              Duplicate
            </button>
            
            {quote.status === 'APPROVED' && (
              <button
                onClick={handleConvertToJob}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
              >
                <BriefcaseIcon className="h-4 w-4 mr-1" />
                Convert to Job
              </button>
            )}
            
            <button
              onClick={handleConvertToEstimate}
              className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
            >
              <DocumentTextIcon className="h-4 w-4 mr-1" />
              Convert to Estimate
            </button>
          </div>
        </div>
      </div>

      {/* Quote Information */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Customer & Project Info */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Customer & Project</h2>
            
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Customer</h3>
                {quote.customer ? (
                  <div className="mt-1">
                    <p className="text-sm text-gray-900 dark:text-white">{quote.customer.name}</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{quote.customer.email}</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{quote.customer.phone}</p>
                  </div>
                ) : (
                  <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">No customer selected</p>
                )}
              </div>
              
              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Project</h3>
                {quote.project ? (
                  <div className="mt-1">
                    <p className="text-sm text-gray-900 dark:text-white">{quote.project.name}</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {quote.project.address}<br />
                      {quote.project.city}, {quote.project.state} {quote.project.zip}
                    </p>
                  </div>
                ) : (
                  <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">No project selected</p>
                )}
              </div>
            </div>
          </div>

          {/* Project Details */}
          {(quote.project_overview || quote.scope_of_work || quote.materials_included || quote.exclusions) && (
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
              <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Project Details</h2>
              
              <div className="space-y-4">
                {quote.project_overview && (
                  <div>
                    <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">Project Overview</h3>
                    <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 whitespace-pre-wrap">{quote.project_overview}</p>
                  </div>
                )}
                
                {quote.scope_of_work && (
                  <div>
                    <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">Scope of Work</h3>
                    <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 whitespace-pre-wrap">{quote.scope_of_work}</p>
                  </div>
                )}
                
                {quote.materials_included && (
                  <div>
                    <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">Materials Included</h3>
                    <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 whitespace-pre-wrap">{quote.materials_included}</p>
                  </div>
                )}
                
                {quote.exclusions && (
                  <div>
                    <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">Exclusions</h3>
                    <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 whitespace-pre-wrap">{quote.exclusions}</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Line Items */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-medium text-gray-900 dark:text-white">Line Items</h2>
              <button
                onClick={handleRefreshPrices}
                className="inline-flex items-center px-3 py-1 text-sm text-blue-600 hover:text-blue-700"
              >
                <ArrowPathIcon className="h-4 w-4 mr-1" />
                Refresh Prices
              </button>
            </div>
            
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-900">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Item</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Category</th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Qty</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Unit</th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Unit Price</th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Total</th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {quote.items.map((item, index) => (
                    <tr key={index}>
                      <td className="px-4 py-3">
                        <div>
                          <p className="text-sm font-medium text-gray-900 dark:text-white">{item.name}</p>
                          {item.description && (
                            <p className="text-sm text-gray-500 dark:text-gray-400">{item.description}</p>
                          )}
                        </div>
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-600 dark:text-gray-400">
                        {item.category}
                      </td>
                      <td className="px-4 py-3 text-sm text-right text-gray-900 dark:text-white">
                        {item.quantity}
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-600 dark:text-gray-400">
                        {item.unit}
                      </td>
                      <td className="px-4 py-3 text-sm text-right text-gray-900 dark:text-white">
                        {formatCurrency(item.unitPrice || 0)}
                      </td>
                      <td className="px-4 py-3 text-sm text-right font-medium text-gray-900 dark:text-white">
                        {formatCurrency(item.totalPrice || item.quantity * (item.unitPrice || 0))}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Terms and Conditions */}
          {quote.terms_and_conditions && (
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
              <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Terms and Conditions</h2>
              <p className="text-sm text-gray-600 dark:text-gray-400 whitespace-pre-wrap">{quote.terms_and_conditions}</p>
            </div>
          )}

          {/* Notes */}
          {(quote.customer_notes || quote.internal_notes) && (
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
              <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Notes</h2>
              
              <div className="space-y-4">
                {quote.customer_notes && (
                  <div>
                    <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">Customer Notes</h3>
                    <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 whitespace-pre-wrap">{quote.customer_notes}</p>
                  </div>
                )}
                
                {quote.internal_notes && (
                  <div>
                    <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">Internal Notes</h3>
                    <p className="mt-1 text-sm text-gray-600 dark:text-gray-400 whitespace-pre-wrap">{quote.internal_notes}</p>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Pricing Summary */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Pricing Summary</h2>
            
            <dl className="space-y-3">
              <div className="flex justify-between text-sm">
                <dt className="text-gray-600 dark:text-gray-400">Subtotal</dt>
                <dd className="text-gray-900 dark:text-white font-medium">{formatCurrency(quote.subtotal)}</dd>
              </div>
              
              <div className="flex justify-between text-sm">
                <dt className="text-gray-600 dark:text-gray-400">Tax ({(quote.tax_rate * 100).toFixed(2)}%)</dt>
                <dd className="text-gray-900 dark:text-white">{formatCurrency(quote.tax_amount)}</dd>
              </div>
              
              {quote.discount > 0 && (
                <div className="flex justify-between text-sm">
                  <dt className="text-gray-600 dark:text-gray-400">
                    Discount {quote.discount_type === 'PERCENTAGE' ? `(${quote.discount}%)` : ''}
                  </dt>
                  <dd className="text-gray-900 dark:text-white">
                    -{formatCurrency(
                      quote.discount_type === 'PERCENTAGE' 
                        ? quote.subtotal * (quote.discount / 100)
                        : quote.discount
                    )}
                  </dd>
                </div>
              )}
              
              <div className="pt-3 border-t border-gray-200 dark:border-gray-700">
                <div className="flex justify-between">
                  <dt className="text-base font-medium text-gray-900 dark:text-white">Total</dt>
                  <dd className="text-base font-medium text-gray-900 dark:text-white">{formatCurrency(quote.total)}</dd>
                </div>
              </div>
            </dl>
          </div>

          {/* Timeline */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Timeline</h2>
            
            <dl className="space-y-3 text-sm">
              <div>
                <dt className="text-gray-600 dark:text-gray-400">Created</dt>
                <dd className="text-gray-900 dark:text-white">{formatDate(quote.created_at)}</dd>
                {quote.created_by && (
                  <dd className="text-xs text-gray-500 dark:text-gray-400">by {quote.created_by.name}</dd>
                )}
              </div>
              
              {quote.sent_at && (
                <div>
                  <dt className="text-gray-600 dark:text-gray-400">Sent</dt>
                  <dd className="text-gray-900 dark:text-white">{formatDate(quote.sent_at)}</dd>
                </div>
              )}
              
              {quote.viewed_at && (
                <div>
                  <dt className="text-gray-600 dark:text-gray-400">Viewed</dt>
                  <dd className="text-gray-900 dark:text-white">{formatDate(quote.viewed_at)}</dd>
                </div>
              )}
              
              {quote.approved_at && (
                <div>
                  <dt className="text-gray-600 dark:text-gray-400">Approved</dt>
                  <dd className="text-gray-900 dark:text-white">{formatDate(quote.approved_at)}</dd>
                </div>
              )}
              
              {quote.rejected_at && (
                <div>
                  <dt className="text-gray-600 dark:text-gray-400">Rejected</dt>
                  <dd className="text-gray-900 dark:text-white">{formatDate(quote.rejected_at)}</dd>
                </div>
              )}
              
              <div>
                <dt className="text-gray-600 dark:text-gray-400">Expires</dt>
                <dd className="text-gray-900 dark:text-white">
                  {quote.expires_at ? formatDate(quote.expires_at) : 'No expiration'}
                </dd>
              </div>
            </dl>
          </div>
        </div>
      </div>

      {/* Send Quote Modal */}
      {showSendModal && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-lg w-full p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Send Quote</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">To</label>
                <input
                  type="email"
                  value={emailData.to}
                  onChange={(e) => setEmailData({ ...emailData, to: e.target.value })}
                  className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">CC (optional)</label>
                <input
                  type="email"
                  value={emailData.cc}
                  onChange={(e) => setEmailData({ ...emailData, cc: e.target.value })}
                  className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Subject</label>
                <input
                  type="text"
                  value={emailData.subject}
                  onChange={(e) => setEmailData({ ...emailData, subject: e.target.value })}
                  className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Message</label>
                <textarea
                  rows={6}
                  value={emailData.message}
                  onChange={(e) => setEmailData({ ...emailData, message: e.target.value })}
                  className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
            
            <div className="mt-6 flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => setShowSendModal(false)}
                className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm hover:bg-gray-50 dark:hover:bg-gray-600"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleSendQuote}
                disabled={sendLoading || !emailData.to}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 disabled:opacity-50"
              >
                {sendLoading ? 'Sending...' : 'Send Quote'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};