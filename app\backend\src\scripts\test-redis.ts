#!/usr/bin/env tsx
import { config } from '../config';
import { redisManager } from '../services/redis-manager';
import { CacheService } from '../services/cache.service';

async function testRedis() {
  console.log('🔧 Testing Redis Connection and Functionality...\n');
  
  // Test 1: Connection
  console.log('1. Testing Redis connection...');
  const connected = await redisManager.connect();
  
  if (!connected) {
    console.log('❌ Redis connection failed');
    console.log('Health:', redisManager.getHealth());
    console.log('\n⚠️  Application can run without Redis, but caching and rate limiting will be disabled.');
    return;
  }
  
  console.log('✅ Redis connected successfully');
  console.log('Health:', redisManager.getHealth());
  
  // Test 2: Basic Operations
  console.log('\n2. Testing basic Redis operations...');
  
  try {
    // Set
    await redisManager.set('test:key', 'test value', 60);
    console.log('✅ SET operation successful');
    
    // Get
    const value = await redisManager.get('test:key');
    console.log(`✅ GET operation successful: ${value}`);
    
    // Exists
    const exists = await redisManager.exists('test:key');
    console.log(`✅ EXISTS operation successful: ${exists}`);
    
    // Delete
    await redisManager.del('test:key');
    console.log('✅ DELETE operation successful');
    
    // Incr
    await redisManager.incr('test:counter');
    const counter = await redisManager.get('test:counter');
    console.log(`✅ INCR operation successful: ${counter}`);
    await redisManager.del('test:counter');
    
  } catch (error) {
    console.error('❌ Basic operations failed:', error);
  }
  
  // Test 3: Cache Service
  console.log('\n3. Testing Cache Service...');
  
  try {
    // Cache calculation
    await CacheService.cacheCalculation('test', 
      { input: 'test' }, 
      { output: 'result' }, 
      60
    );
    console.log('✅ Cache calculation successful');
    
    // Get cached calculation
    const cached = await CacheService.getCachedCalculation('test', { input: 'test' });
    console.log('✅ Get cached calculation successful:', cached);
    
    // Cache material price
    await CacheService.cacheMaterialPrice('TEST123', { price: 10.99, unit: 'each' });
    console.log('✅ Cache material price successful');
    
    // Get cached price
    const price = await CacheService.getCachedMaterialPrice('TEST123');
    console.log('✅ Get cached price successful:', price);
    
    // Get cache stats
    const stats = await CacheService.getCacheStats();
    console.log('✅ Cache stats:', stats);
    
  } catch (error) {
    console.error('❌ Cache service operations failed:', error);
  }
  
  // Test 4: Connection Recovery
  console.log('\n4. Testing connection recovery...');
  
  // Simulate disconnect
  const client = redisManager.getClient();
  if (client) {
    client.disconnect();
    console.log('⚠️  Simulated disconnect');
    
    // Wait a bit
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Check health
    console.log('Health after disconnect:', redisManager.getHealth());
    
    // Try to reconnect
    const reconnected = await redisManager.connect();
    console.log(`Reconnection ${reconnected ? 'successful' : 'failed'}`);
  }
  
  // Cleanup
  console.log('\n5. Cleaning up...');
  await redisManager.disconnect();
  console.log('✅ Disconnected successfully');
  
  console.log('\n✅ All Redis tests completed!');
}

// Run tests
testRedis().catch(console.error).finally(() => process.exit(0));