import { Navigate, Outlet, useLocation } from 'react-router-dom';
import { useAuthStore } from '../../stores/auth';
import { useEffect, useState } from 'react';

interface ProtectedRouteProps {
  children?: React.ReactNode;
  redirectTo?: string;
}

export function ProtectedRoute({ children, redirectTo = '/login' }: ProtectedRouteProps) {
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  const user = useAuthStore((state) => state.user);
  const isLoading = useAuthStore((state) => state.isLoading);
  const hasHydrated = useAuthStore((state) => state.hasHydrated);
  const location = useLocation();

  // Show loading state while hydrating
  if (!hasHydrated || isLoading) {
    return (
      <div className="flex items-center justify-center h-screen bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading...</p>
        </div>
      </div>
    );
  }

  // Check both authentication and user existence with proper validation
  if (!isAuthenticated || !user || !user.id || !user.email) {
    console.log('[ProtectedRoute] Authentication check failed:', {
      isAuthenticated,
      hasUser: !!user,
      userId: user?.id,
      userEmail: user?.email,
      hasHydrated,
      location: location.pathname,
      timestamp: new Date().toISOString()
    });
    
    // Special handling for /quotes/new route
    if (location.pathname === '/quotes/new') {
      console.log('[ProtectedRoute] Special handling for /quotes/new - double checking auth state');
      const authStorage = localStorage.getItem('auth-storage');
      if (authStorage) {
        try {
          const parsed = JSON.parse(authStorage);
          console.log('[ProtectedRoute] Found auth in storage for /quotes/new:', {
            hasToken: !!parsed.state?.accessToken,
            hasUser: !!parsed.state?.user,
            isAuthenticated: parsed.state?.isAuthenticated
          });
        } catch (e) {
          console.error('[ProtectedRoute] Failed to parse auth storage:', e);
        }
      }
    }
    
    // Save the location they were trying to go to
    return <Navigate to={redirectTo} state={{ from: location }} replace />;
  }

  return children ? <>{children}</> : <Outlet />;
}