import { Request, Response, NextFunction } from 'express';
import crypto from 'crypto';
import { AppError } from '../middleware/errorHandler';
import { getRedis } from '../services/redis';

const CSRF_TOKEN_LENGTH = 32;
const CSRF_TOKEN_EXPIRY = 3600; // 1 hour
const CSRF_HEADER = 'x-csrf-token';
const CSRF_COOKIE = 'csrf-token';

export interface CsrfRequest extends Request {
  csrfToken?: string;
}

// Generate CSRF token
export async function generateCsrfToken(sessionId: string): Promise<string> {
  const token = crypto.randomBytes(CSRF_TOKEN_LENGTH).toString('hex');
  const key = `csrf:${sessionId}`;
  
  const redis = getRedis();
  if (redis) {
    await redis.setex(key, CSRF_TOKEN_EXPIRY, token);
  }
  
  return token;
}

// Verify CSRF token
export async function verifyCsrfToken(sessionId: string, token: string): Promise<boolean> {
  const redis = getRedis();
  if (!redis) {
    // In development without Redis, skip CSRF verification
    console.warn('CSRF verification skipped - Redis not available');
    return true;
  }
  
  const key = `csrf:${sessionId}`;
  const storedToken = await redis.get(key);
  
  if (!storedToken) return false;
  
  // Use timing-safe comparison
  return crypto.timingSafeEqual(
    Buffer.from(token),
    Buffer.from(storedToken)
  );
}

// CSRF middleware
export function csrfProtection(options?: {
  excludePaths?: string[];
  methods?: string[];
}) {
  const excludePaths = options?.excludePaths || [];
  const methods = options?.methods || ['POST', 'PUT', 'DELETE', 'PATCH'];
  
  return async (req: CsrfRequest, res: Response, next: NextFunction) => {
    // Skip for excluded paths
    if (excludePaths.some(path => req.path.startsWith(path))) {
      return next();
    }
    
    // Skip for safe methods
    if (!methods.includes(req.method)) {
      return next();
    }
    
    // Get session ID (from JWT or session)
    const sessionId = (req as any).user?.userId || req.sessionID;
    if (!sessionId) {
      return next(new AppError(401, 'No session found', true, 'NO_SESSION'));
    }
    
    // Get CSRF token from header or body
    const token = req.headers[CSRF_HEADER] as string || req.body?._csrf;
    
    if (!token) {
      return next(new AppError(403, 'CSRF token missing', true, 'CSRF_TOKEN_MISSING'));
    }
    
    // Verify token
    const isValid = await verifyCsrfToken(sessionId, token);
    
    if (!isValid) {
      return next(new AppError(403, 'Invalid CSRF token', true, 'CSRF_TOKEN_INVALID'));
    }
    
    // Regenerate token for next request
    req.csrfToken = await generateCsrfToken(sessionId);
    res.setHeader(CSRF_HEADER, req.csrfToken);
    
    next();
  };
}

// Endpoint to get CSRF token
export async function getCsrfToken(req: Request, res: Response) {
  const sessionId = (req as any).user?.userId || req.sessionID;
  
  if (!sessionId) {
    throw new AppError(401, 'No session found', true, 'NO_SESSION');
  }
  
  const token = await generateCsrfToken(sessionId);
  
  res.json({ token });
}