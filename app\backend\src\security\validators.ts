import { Request, Response, NextFunction } from 'express';
import { z } from 'zod';
import validator from 'validator';
import path from 'path';
import { AppError } from '../middleware/errorHandler';
import { createAuditLog, AUDIT_ACTIONS } from './audit';
import { getRedis } from '../services/redis';

// SQL injection patterns - more precise to avoid false positives
const SQL_INJECTION_PATTERNS = [
  // Match SQL keywords only when they appear to be part of SQL syntax
  /(\b(union\s+select|insert\s+into|update\s+set|delete\s+from|drop\s+table|create\s+table|alter\s+table)\b)/i,
  // Match SQL comments and special characters
  /(--|\/\*|\*\/|;.*(?:union|select|insert|update|delete|drop|create))/i,
  // Match SQL injection attempts with quotes
  /('\s*;\s*(?:union|select|insert|update|delete|drop|create))/i,
  // Match boolean-based SQL injection
  /(\b(?:or|and)\b\s*['"]?\d+['"]?\s*=\s*['"]?\d+['"]?(?:\s*--|;))/i,
  /(\b(?:or|and)\b\s*'[^']*'\s*=\s*'[^']*'(?:\s*--|;))/i,
  // Match common SQL injection payloads
  /(';.*(?:union|select|insert|update|delete|drop|create))/i,
  /(\) or \d+=\d+|' or '1'='1)/i
];

// XSS patterns
const XSS_PATTERNS = [
  /<script[^>]*>[\s\S]*?<\/script>/gi,
  /<iframe[^>]*>[\s\S]*?<\/iframe>/gi,
  /javascript:/gi,
  /on\w+\s*=/gi,
  /<img[^>]+src[\\s]*=[\\s]*["\']javascript:/gi
];

// Path traversal patterns
const PATH_TRAVERSAL_PATTERNS = [
  /\.\./g,
  /\.\.%2F/gi,
  /\.\.%5C/gi,
  /%2e%2e/gi,
  /\.\.\\/g
];

// Sanitize input to prevent XSS
export function sanitizeHtml(input: string): string {
  return validator.escape(input);
}

// Validate and sanitize SQL input
export function validateSqlInput(input: string): boolean {
  for (const pattern of SQL_INJECTION_PATTERNS) {
    if (pattern.test(input)) {
      return false;
    }
  }
  return true;
}

// Validate file path
export function validateFilePath(path: string): boolean {
  for (const pattern of PATH_TRAVERSAL_PATTERNS) {
    if (pattern.test(path)) {
      return false;
    }
  }
  return true;
}

// Input validation middleware
export function validateInput(schema: z.ZodSchema) {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      // Validate request body
      const validated = await schema.parseAsync(req.body);
      
      // Deep check for SQL injection
      const checkForSqlInjection = (obj: unknown, path: string = ''): void => {
        if (typeof obj === 'string') {
          if (!validateSqlInput(obj)) {
            throw new AppError(400, `Potential SQL injection detected in ${path}`, true, 'SQL_INJECTION_DETECTED');
          }
        } else if (Array.isArray(obj)) {
          obj.forEach((item, index) => checkForSqlInjection(item, `${path}[${index}]`));
        } else if (obj !== null && typeof obj === 'object') {
          Object.entries(obj).forEach(([key, value]) => {
            checkForSqlInjection(value, path ? `${path}.${key}` : key);
          });
        }
      };
      
      checkForSqlInjection(validated);
      
      // Replace body with validated data
      req.body = validated;
      
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        next(new AppError(400, 'Validation failed', true, 'VALIDATION_ERROR', error.errors));
      } else {
        next(error);
      }
    }
  };
}

// Sanitize output middleware
export function sanitizeOutput() {
  return (req: Request, res: Response, next: NextFunction) => {
    const originalJson = res.json;
    
    res.json = function(data: unknown) {
      const sanitizeData = (obj: unknown): unknown => {
        if (typeof obj === 'string') {
          // Check for XSS patterns
          for (const pattern of XSS_PATTERNS) {
            if (pattern.test(obj)) {
              return sanitizeHtml(obj);
            }
          }
          return obj;
        } else if (Array.isArray(obj)) {
          return obj.map(sanitizeData);
        } else if (obj !== null && typeof obj === 'object') {
          const sanitized: Record<string, unknown> = {};
          for (const [key, value] of Object.entries(obj)) {
            sanitized[key] = sanitizeData(value);
          }
          return sanitized;
        }
        return obj;
      };
      
      const sanitizedData = sanitizeData(data);
      return originalJson.call(this, sanitizedData);
    };
    
    next();
  };
}

// File upload validation
export function validateFileUpload(options: {
  allowedTypes?: string[];
  maxSize?: number;
  allowedExtensions?: string[];
}) {
  const allowedTypes = options.allowedTypes || ['image/jpeg', 'image/png', 'application/pdf'];
  const maxSize = options.maxSize || 10 * 1024 * 1024; // 10MB
  const allowedExtensions = options.allowedExtensions || ['.jpg', '.jpeg', '.png', '.pdf'];
  
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.file && !req.files) {
      return next();
    }
    
    const files = req.file ? [req.file] : Object.values(req.files || {}).flat();
    
    for (const file of files) {
      // Check file type
      if (!allowedTypes.includes(file.mimetype)) {
        return next(new AppError(400, 'Invalid file type', true, 'INVALID_FILE_TYPE'));
      }
      
      // Check file size
      if (file.size > maxSize) {
        return next(new AppError(400, 'File too large', true, 'FILE_TOO_LARGE'));
      }
      
      // Check file extension
      const ext = path.extname(file.originalname).toLowerCase();
      if (!allowedExtensions.includes(ext)) {
        return next(new AppError(400, 'Invalid file extension', true, 'INVALID_FILE_EXTENSION'));
      }
      
      // Validate file path
      if (!validateFilePath(file.originalname)) {
        return next(new AppError(400, 'Invalid file name', true, 'INVALID_FILE_NAME'));
      }
      
      // Additional security checks
      if (file.originalname.includes('\0')) {
        return next(new AppError(400, 'Invalid file name', true, 'NULL_BYTE_IN_FILENAME'));
      }
    }
    
    next();
  };
}

// Rate limit by user and action
interface AuthenticatedRequest extends Request {
  user?: {
    userId: string;
    [key: string]: unknown;
  };
}

export function actionRateLimit(action: string, limit: number, window: number) {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      return next();
    }
    
    const redis = getRedis();
    if (!redis) {
      // Skip rate limiting if Redis not available
      return next();
    }
    
    const key = `rate_limit:${action}:${req.user.userId}`;
    const current = await redis.incr(key);
    
    if (current === 1) {
      await redis.expire(key, window);
    }
    
    if (current > limit) {
      await createAuditLog({
        action: AUDIT_ACTIONS.RATE_LIMIT_EXCEEDED,
        userId: req.user.userId,
        resourceType: 'rate_limit',
        details: { action, limit, window }
      });
      
      return next(new AppError(429, 'Rate limit exceeded', true, 'RATE_LIMIT_EXCEEDED'));
    }
    
    next();
  };
}

// Parameterized query helper
export function buildSecureQuery(
  baseQuery: string,
  conditions: Record<string, unknown>
): { query: string; params: unknown[] } {
  const params: unknown[] = [];
  let query = baseQuery;
  const whereClauses: string[] = [];
  
  for (const [key, value] of Object.entries(conditions)) {
    if (value !== undefined && value !== null) {
      // Validate column name (alphanumeric and underscore only)
      if (!/^[a-zA-Z0-9_]+$/.test(key)) {
        throw new Error(`Invalid column name: ${key}`);
      }
      
      whereClauses.push(`${key} = ?`);
      params.push(value);
    }
  }
  
  if (whereClauses.length > 0) {
    query += ' WHERE ' + whereClauses.join(' AND ');
  }
  
  return { query, params };
}