// Temporarily using mock TypeORM - will be replaced with proper database solution
// Note: Decorators removed for React Native compatibility
import { BaseEntity } from './BaseEntity';
import { Panel } from './Panel';

export class Circuit extends BaseEntity {
  number: number;

  name: string;

  amperage: number;

  type: 'single' | 'double' | 'triple';

  status: 'active' | 'inactive' | 'maintenance';

  wireSize: string;

  wireType: string;

  destination: string;

  loadPercentage: number;

  notes: string;

  metadata: string; // JSON string

  panelId: string;

  panel: Panel;
}