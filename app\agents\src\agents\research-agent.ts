import { BaseAgent, BaseAgentConfig, AgentCapability } from '../base/base-agent';
import { z } from 'zod';
import axios from 'axios';
import { 
  NEC_REFERENCES,
  GENERAL_LIGHTING_LOADS,
  COPPER_AMPACITY_75C,
  ALUMINUM_AMPACITY_75C,
  CONDUIT_AREA,
  CONDUCTOR_AREA 
} from '@electrical/shared';

// Research task schemas
const necLookupSchema = z.object({
  article: z.string().optional(),
  section: z.string().optional(),
  keyword: z.string().optional(),
  edition: z.string().default('2023'),
});

const materialLookupSchema = z.object({
  materials: z.array(z.object({
    name: z.string(),
    quantity: z.number(),
    specifications: z.string().optional(),
  })),
  suppliers: z.array(z.string()).optional(),
  location: z.string().optional(),
});

const projectAnalysisSchema = z.object({
  description: z.string(),
  buildingType: z.string().optional(),
  squareFootage: z.number().optional(),
  voltage: z.number().optional(),
});

const complianceCheckSchema = z.object({
  parameters: z.record(z.any()),
  checkType: z.enum(['load', 'voltage-drop', 'conduit-fill', 'grounding', 'general']),
});

// NEC knowledge base (simplified - in production, use full database)
const NEC_KNOWLEDGE = {
  '210.19': {
    title: 'Conductors - Minimum Ampacity and Size',
    summary: 'Branch-circuit conductors shall have an ampacity not less than the maximum load to be served.',
    requirements: [
      'Conductors shall be sized to carry not less than the larger of (a) or (b)',
      '(a) Where a branch circuit supplies continuous loads, minimum branch-circuit conductor size shall have an allowable ampacity not less than 125% of the continuous load',
      '(b) Minimum branch-circuit conductor size shall have an allowable ampacity not less than the maximum load to be served after application of adjustment or correction factors',
    ],
  },
  '220.12': {
    title: 'Lighting Load for Specified Occupancies',
    summary: 'A unit load of not less than that specified in Table 220.12 for occupancies specified therein shall constitute the minimum lighting load.',
    table: GENERAL_LIGHTING_LOADS,
  },
  '250.122': {
    title: 'Size of Equipment Grounding Conductors',
    summary: 'Equipment grounding conductors shall be sized based on the rating of the overcurrent device protecting the circuit.',
    requirements: [
      '15A circuit: 14 AWG copper',
      '20A circuit: 12 AWG copper',
      '30-60A circuit: 10 AWG copper',
      '70-100A circuit: 8 AWG copper',
      '110-200A circuit: 6 AWG copper',
    ],
  },
  '310.16': {
    title: 'Ampacities of Insulated Conductors',
    summary: 'Allowable ampacities of insulated conductors rated up to and including 2000 volts.',
    data: {
      copper_75c: COPPER_AMPACITY_75C,
      aluminum_75c: ALUMINUM_AMPACITY_75C,
    },
  },
};

// Material supplier APIs (mock endpoints - replace with real APIs)
const SUPPLIER_APIS = {
  'grainger': 'https://api.grainger.com/v1/products',
  'graybar': 'https://api.graybar.com/materials',
  'rexel': 'https://api.rexel.com/catalog',
  'wholesale': 'https://api.wholesaleelectrical.com/items',
};

export class ResearchAgent extends BaseAgent {
  private necCache: Map<string, any>;
  private priceCache: Map<string, { price: number; timestamp: number }>;
  private priceCacheTimeout = 60 * 60 * 1000; // 1 hour

  constructor(config: Omit<BaseAgentConfig, 'capabilities'>) {
    const capabilities: AgentCapability[] = [
      {
        name: 'lookup-nec-reference',
        description: 'Look up NEC code references and requirements',
        inputSchema: necLookupSchema,
      },
      {
        name: 'lookup-material-prices',
        description: 'Get current material prices from suppliers',
        inputSchema: materialLookupSchema,
      },
      {
        name: 'analyze-project-requirements',
        description: 'Analyze project description and extract requirements',
        inputSchema: projectAnalysisSchema,
      },
      {
        name: 'check-nec-compliance',
        description: 'Check if parameters meet NEC requirements',
        inputSchema: complianceCheckSchema,
      },
      {
        name: 'research-best-practices',
        description: 'Research industry best practices for specific scenarios',
      },
      {
        name: 'find-alternatives',
        description: 'Find alternative materials or methods',
      },
    ];

    super({
      ...config,
      capabilities,
    });

    // Initialize Maps in constructor to avoid transpilation issues
    this.necCache = new Map();
    this.priceCache = new Map();
  }

  protected async onInitialize(): Promise<void> {
    // Load NEC references into cache
    Object.entries(NEC_KNOWLEDGE).forEach(([article, data]) => {
      this.necCache.set(article, data);
    });

    // Load pricing history from memory
    const priceHistory = await this.retrieveKnowledge(['material', 'price'], 100);
    priceHistory.forEach(item => {
      if (item.content.sku && item.content.price) {
        this.priceCache.set(item.content.sku, {
          price: item.content.price,
          timestamp: item.metadata.created.getTime(),
        });
      }
    });

    await this.log('Research agent initialized', { 
      level: 'info',
      necArticles: this.necCache.size,
      cachedPrices: this.priceCache.size,
    });
  }

  protected async processTask(taskType: string, data: any): Promise<any> {
    switch (taskType) {
      case 'lookup-nec-reference':
        return this.lookupNECReference(data);
      case 'lookup-material-prices':
        return this.lookupMaterialPrices(data);
      case 'analyze-project-requirements':
        return this.analyzeProjectRequirements(data);
      case 'check-nec-compliance':
        return this.checkNECCompliance(data);
      case 'research-best-practices':
        return this.researchBestPractices(data);
      case 'find-alternatives':
        return this.findAlternatives(data);
      default:
        throw new Error(`Unknown task type: ${taskType}`);
    }
  }

  // Look up NEC reference
  private async lookupNECReference(data: z.infer<typeof necLookupSchema>): Promise<any> {
    const { article, section, keyword, edition } = data;

    let results = [];

    // Search by article/section
    if (article) {
      const articleData = this.necCache.get(article);
      if (articleData) {
        results.push({
          article,
          ...articleData,
          edition,
        });
      }
    }

    // Search by keyword
    if (keyword) {
      const keywordLower = keyword.toLowerCase();
      for (const [art, data] of this.necCache.entries()) {
        const content = JSON.stringify(data).toLowerCase();
        if (content.includes(keywordLower)) {
          results.push({
            article: art,
            ...data,
            edition,
            relevance: this.calculateRelevance(content, keywordLower),
          });
        }
      }
      
      // Sort by relevance
      results.sort((a, b) => (b.relevance || 0) - (a.relevance || 0));
    }

    // Store search in memory for learning
    await this.storeKnowledge(
      { query: data, results: results.length },
      ['nec', 'search', keyword || article || 'general'],
      0.3
    );

    return {
      query: data,
      results,
      totalFound: results.length,
      additionalInfo: this.getAdditionalNECInfo(results),
    };
  }

  // Look up material prices from suppliers
  private async lookupMaterialPrices(data: z.infer<typeof materialLookupSchema>): Promise<any> {
    const { materials, suppliers = Object.keys(SUPPLIER_APIS), location } = data;

    const priceResults = [];

    for (const material of materials) {
      const materialKey = `${material.name}-${material.specifications || 'standard'}`;
      
      // Check cache first
      const cached = this.priceCache.get(materialKey);
      if (cached && Date.now() - cached.timestamp < this.priceCacheTimeout) {
        priceResults.push({
          material: material.name,
          quantity: material.quantity,
          cachedPrice: cached.price,
          totalPrice: cached.price * material.quantity,
          source: 'cache',
        });
        continue;
      }

      // Simulate API calls to suppliers (in production, use real APIs)
      const supplierPrices = await this.fetchSupplierPrices(material, suppliers);
      
      // Find best price
      const bestPrice = Math.min(...supplierPrices.map(s => s.unitPrice));
      const bestSupplier = supplierPrices.find(s => s.unitPrice === bestPrice);

      // Cache the price
      this.priceCache.set(materialKey, {
        price: bestPrice,
        timestamp: Date.now(),
      });

      // Store in long-term memory
      await this.storeKnowledge(
        {
          sku: materialKey,
          material: material.name,
          price: bestPrice,
          supplier: bestSupplier?.supplier,
          location,
          date: new Date(),
        },
        ['material', 'price', material.name],
        0.6
      );

      priceResults.push({
        material: material.name,
        quantity: material.quantity,
        specifications: material.specifications,
        unitPrice: bestPrice,
        totalPrice: bestPrice * material.quantity,
        supplier: bestSupplier?.supplier,
        alternatives: supplierPrices,
        priceHistory: await this.getPriceHistory(materialKey),
      });
    }

    // Calculate totals
    const subtotal = priceResults.reduce((sum, item) => sum + item.totalPrice, 0);
    const averageSavings = this.calculatePotentialSavings(priceResults);

    return {
      materials: priceResults,
      subtotal,
      averageSavings,
      lastUpdated: new Date(),
      recommendations: this.generatePricingRecommendations(priceResults),
    };
  }

  // Analyze project requirements from description
  private async analyzeProjectRequirements(data: z.infer<typeof projectAnalysisSchema>): Promise<any> {
    const { description, buildingType, squareFootage, voltage } = data;

    // Extract key information using pattern matching
    const analysis = {
      projectType: this.identifyProjectType(description),
      estimatedLoad: 0,
      requiredMaterials: [] as any[],
      necArticles: [] as string[],
      specialRequirements: [] as string[],
      riskFactors: [] as string[],
    };

    // Identify building type if not provided
    if (!buildingType) {
      analysis.buildingType = this.identifyBuildingType(description);
    } else {
      analysis.buildingType = buildingType;
    }

    // Estimate load requirements
    if (squareFootage && analysis.buildingType) {
      const loadPerSqFt = GENERAL_LIGHTING_LOADS[analysis.buildingType as keyof typeof GENERAL_LIGHTING_LOADS] || 3;
      analysis.estimatedLoad = squareFootage * loadPerSqFt;
    }

    // Identify required materials
    analysis.requiredMaterials = this.extractMaterials(description);

    // Identify applicable NEC articles
    analysis.necArticles = this.identifyNECArticles(description, analysis.projectType);

    // Identify special requirements
    if (description.toLowerCase().includes('outdoor')) {
      analysis.specialRequirements.push('Weather-resistant materials required');
      analysis.necArticles.push('300.5'); // Underground installations
    }
    
    if (description.toLowerCase().includes('bathroom') || description.toLowerCase().includes('kitchen')) {
      analysis.specialRequirements.push('GFCI protection required');
      analysis.necArticles.push('210.8'); // GFCI requirements
    }

    if (description.toLowerCase().includes('pool') || description.toLowerCase().includes('spa')) {
      analysis.specialRequirements.push('Special bonding and grounding requirements');
      analysis.necArticles.push('680'); // Swimming pools
      analysis.riskFactors.push('High risk - water and electricity');
    }

    // Identify risk factors
    if (description.toLowerCase().includes('aluminum')) {
      analysis.riskFactors.push('Aluminum wiring - special considerations needed');
    }

    if (description.toLowerCase().includes('old') || description.toLowerCase().includes('existing')) {
      analysis.riskFactors.push('Existing installation - potential code compliance issues');
    }

    // Store analysis in memory for future reference
    await this.storeKnowledge(
      analysis,
      ['project', 'analysis', analysis.projectType],
      0.7
    );

    return {
      ...analysis,
      confidence: this.calculateConfidence(analysis),
      suggestions: this.generateProjectSuggestions(analysis),
    };
  }

  // Check NEC compliance
  private async checkNECCompliance(data: z.infer<typeof complianceCheckSchema>): Promise<any> {
    const { parameters, checkType } = data;

    const complianceReport = {
      checkType,
      compliant: true,
      violations: [] as any[],
      warnings: [] as any[],
      references: [] as string[],
    };

    switch (checkType) {
      case 'load':
        this.checkLoadCompliance(parameters, complianceReport);
        break;
      case 'voltage-drop':
        this.checkVoltageDrop

Compliance(parameters, complianceReport);
        break;
      case 'conduit-fill':
        this.checkConduitFillCompliance(parameters, complianceReport);
        break;
      case 'grounding':
        this.checkGroundingCompliance(parameters, complianceReport);
        break;
      case 'general':
        // Check all applicable requirements
        this.checkLoadCompliance(parameters, complianceReport);
        this.checkVoltageDropCompliance(parameters, complianceReport);
        this.checkConduitFillCompliance(parameters, complianceReport);
        this.checkGroundingCompliance(parameters, complianceReport);
        break;
    }

    // Generate recommendations
    const recommendations = this.generateComplianceRecommendations(complianceReport);

    return {
      ...complianceReport,
      recommendations,
      checkedAt: new Date(),
      necEdition: '2023',
    };
  }

  // Research best practices
  private async researchBestPractices(data: any): Promise<any> {
    const { scenario, context } = data;

    // Retrieve similar scenarios from memory
    const similarCases = await this.retrieveKnowledge(['best-practice', scenario], 10);
    
    // Generate best practices based on scenario
    const practices = [];

    if (scenario.includes('panel')) {
      practices.push({
        practice: 'Leave 20% spare capacity in panels',
        reason: 'Allows for future expansion without panel replacement',
        necReference: '408.54',
      });
      practices.push({
        practice: 'Use color-coded phase tape',
        reason: 'Improves safety and maintenance efficiency',
        necReference: '210.5(C)',
      });
    }

    if (scenario.includes('commercial')) {
      practices.push({
        practice: 'Install surge protection at service entrance',
        reason: 'Protects sensitive electronic equipment',
        necReference: '242',
      });
      practices.push({
        practice: 'Use LED lighting with controls',
        reason: 'Energy efficiency and code compliance',
        necReference: 'Energy codes',
      });
    }

    // Add practices from similar cases
    similarCases.forEach(caseItem => {
      if (caseItem.content.practice) {
        practices.push(caseItem.content.practice);
      }
    });

    return {
      scenario,
      practices,
      totalFound: practices.length,
      sources: ['NEC 2023', 'Industry standards', 'Historical data'],
    };
  }

  // Find alternative materials or methods
  private async findAlternatives(data: any): Promise<any> {
    const { originalMaterial, reason, constraints } = data;

    const alternatives = [];

    // Find alternatives based on reason
    if (reason === 'cost') {
      // Look for cheaper alternatives
      if (originalMaterial.includes('copper')) {
        alternatives.push({
          material: 'Aluminum conductors',
          savings: '40-60%',
          considerations: ['Requires larger size', 'Special terminations needed'],
          necCompliant: true,
        });
      }
      
      if (originalMaterial.includes('EMT')) {
        alternatives.push({
          material: 'PVC conduit',
          savings: '50-70%',
          considerations: ['Not suitable for all locations', 'Temperature limitations'],
          necCompliant: true,
        });
      }
    }

    if (reason === 'availability') {
      // Find readily available alternatives
      alternatives.push({
        material: 'Generic equivalent',
        availability: 'In stock',
        considerations: ['Verify specifications match'],
        necCompliant: true,
      });
    }

    if (reason === 'performance') {
      // Find higher performance alternatives
      if (originalMaterial.includes('THHN')) {
        alternatives.push({
          material: 'XHHW-2',
          benefits: ['Wet location rated', 'Higher temperature rating'],
          considerations: ['Higher cost'],
          necCompliant: true,
        });
      }
    }

    // Store alternatives in memory
    await this.storeKnowledge(
      { originalMaterial, alternatives, reason },
      ['alternatives', originalMaterial, reason],
      0.5
    );

    return {
      originalMaterial,
      reason,
      alternatives,
      recommendation: alternatives.length > 0 ? alternatives[0] : null,
    };
  }

  // Helper methods

  private calculateRelevance(content: string, keyword: string): number {
    const keywordCount = (content.match(new RegExp(keyword, 'gi')) || []).length;
    const contentLength = content.length;
    return (keywordCount * 100) / contentLength;
  }

  private getAdditionalNECInfo(results: any[]): any {
    const info = {
      relatedArticles: [] as string[],
      commonViolations: [] as string[],
    };

    results.forEach(result => {
      // Add related articles based on topic
      if (result.article?.startsWith('210')) {
        info.relatedArticles.push('215', '220', '225'); // Branch circuits
      }
      if (result.article?.startsWith('250')) {
        info.relatedArticles.push('680', '690'); // Grounding related
      }
    });

    return info;
  }

  private async fetchSupplierPrices(material: any, suppliers: string[]): Promise<any[]> {
    // Simulate API calls with realistic pricing
    const basePrices: Record<string, number> = {
      'THHN 12 AWG': 0.15,
      'THHN 10 AWG': 0.25,
      'EMT 3/4"': 3.50,
      'EMT 1"': 4.75,
      'PVC 3/4"': 1.25,
      'Outlet': 2.50,
      'Breaker 20A': 8.50,
      'Panel 200A': 185.00,
    };

    const results = suppliers.map(supplier => {
      const basePrice = this.findBasePrice(material.name, basePrices);
      const variation = 0.9 + Math.random() * 0.2; // ±10% price variation
      const supplierPrice = basePrice * variation;

      return {
        supplier,
        unitPrice: Number(supplierPrice.toFixed(2)),
        availability: Math.random() > 0.2 ? 'In Stock' : 'Special Order',
        deliveryDays: Math.random() > 0.5 ? 1 : 3,
      };
    });

    return results;
  }

  private findBasePrice(materialName: string, basePrices: Record<string, number>): number {
    const nameLower = materialName.toLowerCase();
    
    for (const [key, price] of Object.entries(basePrices)) {
      if (nameLower.includes(key.toLowerCase())) {
        return price;
      }
    }
    
    // Default prices by category
    if (nameLower.includes('wire') || nameLower.includes('thhn')) return 0.20;
    if (nameLower.includes('conduit')) return 3.00;
    if (nameLower.includes('breaker')) return 10.00;
    if (nameLower.includes('panel')) return 150.00;
    
    return 5.00; // Default
  }

  private async getPriceHistory(materialKey: string): Promise<any[]> {
    const history = await this.retrieveKnowledge(['material', 'price', materialKey], 30);
    
    return history.map(item => ({
      date: item.metadata.created,
      price: item.content.price,
      supplier: item.content.supplier,
    })).sort((a, b) => b.date.getTime() - a.date.getTime());
  }

  private calculatePotentialSavings(priceResults: any[]): number {
    let totalSavings = 0;
    
    priceResults.forEach(result => {
      if (result.alternatives && result.alternatives.length > 1) {
        const prices = result.alternatives.map((a: any) => a.unitPrice);
        const highest = Math.max(...prices);
        const lowest = Math.min(...prices);
        totalSavings += (highest - lowest) * result.quantity;
      }
    });
    
    return totalSavings;
  }

  private generatePricingRecommendations(priceResults: any[]): string[] {
    const recommendations = [];
    
    // Check for bulk discounts
    const totalQuantity = priceResults.reduce((sum, item) => sum + item.quantity, 0);
    if (totalQuantity > 100) {
      recommendations.push('Consider negotiating bulk discount for large quantity order');
    }
    
    // Check price volatility
    priceResults.forEach(result => {
      if (result.priceHistory && result.priceHistory.length > 5) {
        const prices = result.priceHistory.map((h: any) => h.price);
        const avgPrice = prices.reduce((a: number, b: number) => a + b, 0) / prices.length;
        const currentPrice = result.unitPrice;
        
        if (currentPrice > avgPrice * 1.1) {
          recommendations.push(`${result.material} price is 10% above average - consider waiting or alternatives`);
        }
        if (currentPrice < avgPrice * 0.9) {
          recommendations.push(`${result.material} price is below average - good time to buy`);
        }
      }
    });
    
    return recommendations;
  }

  private identifyProjectType(description: string): string {
    const desc = description.toLowerCase();
    
    if (desc.includes('new construction')) return 'new-construction';
    if (desc.includes('remodel') || desc.includes('renovation')) return 'remodel';
    if (desc.includes('service upgrade')) return 'service-upgrade';
    if (desc.includes('troubleshoot') || desc.includes('repair')) return 'repair';
    if (desc.includes('addition')) return 'addition';
    
    return 'general';
  }

  private identifyBuildingType(description: string): string {
    const desc = description.toLowerCase();
    
    if (desc.includes('home') || desc.includes('house') || desc.includes('residential')) return 'DWELLING';
    if (desc.includes('office')) return 'OFFICE';
    if (desc.includes('store') || desc.includes('retail')) return 'STORE';
    if (desc.includes('warehouse')) return 'WAREHOUSE';
    if (desc.includes('hospital')) return 'HOSPITAL';
    if (desc.includes('school')) return 'SCHOOL';
    if (desc.includes('hotel') || desc.includes('motel')) return 'HOTEL';
    
    return 'DWELLING'; // Default
  }

  private extractMaterials(description: string): any[] {
    const materials = [];
    const desc = description.toLowerCase();
    
    // Common patterns
    const patterns = [
      { pattern: /(\d+)\s*amp\s*panel/i, material: 'Electrical Panel', unit: 'each' },
      { pattern: /(\d+)\s*amp\s*breaker/i, material: 'Circuit Breaker', unit: 'each' },
      { pattern: /#?(\d+)\s*awg/i, material: 'THHN Wire', unit: 'feet' },
      { pattern: /(\d+)\s*outlet/i, material: 'Duplex Outlet', unit: 'each' },
      { pattern: /(\d+)\s*switch/i, material: 'Light Switch', unit: 'each' },
    ];
    
    patterns.forEach(({ pattern, material, unit }) => {
      const matches = desc.match(pattern);
      if (matches) {
        materials.push({
          material,
          specification: matches[0],
          estimatedQuantity: parseInt(matches[1]) || 1,
          unit,
        });
      }
    });
    
    return materials;
  }

  private identifyNECArticles(description: string, projectType: string): string[] {
    const articles = [];
    const desc = description.toLowerCase();
    
    // Common articles by project type
    const projectArticles: Record<string, string[]> = {
      'new-construction': ['210', '220', '230', '250', '300', '310'],
      'remodel': ['210', '220', '250', '300', '406.4'],
      'service-upgrade': ['230', '250', '310', '408'],
      'repair': ['110.12', '110.14', '250'],
      'general': ['110', '210', '250'],
    };
    
    articles.push(...(projectArticles[projectType] || projectArticles.general));
    
    // Specific requirements
    if (desc.includes('gfci')) articles.push('210.8');
    if (desc.includes('afci')) articles.push('210.12');
    if (desc.includes('bathroom')) articles.push('210.11(C)(3)', '210.52(D)');
    if (desc.includes('kitchen')) articles.push('210.11(C)(1)', '210.52(B)');
    if (desc.includes('garage')) articles.push('210.11(C)(4)', '210.52(G)');
    
    return [...new Set(articles)]; // Remove duplicates
  }

  private calculateConfidence(analysis: any): number {
    let confidence = 0.5; // Base confidence
    
    if (analysis.buildingType) confidence += 0.1;
    if (analysis.estimatedLoad > 0) confidence += 0.1;
    if (analysis.requiredMaterials.length > 0) confidence += 0.1;
    if (analysis.necArticles.length > 0) confidence += 0.1;
    if (analysis.specialRequirements.length > 0) confidence += 0.1;
    
    return Math.min(confidence, 1.0);
  }

  private generateProjectSuggestions(analysis: any): string[] {
    const suggestions = [];
    
    if (!analysis.estimatedLoad) {
      suggestions.push('Provide square footage to calculate estimated electrical load');
    }
    
    if (analysis.riskFactors.length > 0) {
      suggestions.push('Consider hiring specialist for high-risk aspects');
    }
    
    if (analysis.specialRequirements.includes('GFCI protection required')) {
      suggestions.push('Budget for GFCI outlets/breakers - approximately $20-40 each');
    }
    
    if (analysis.projectType === 'remodel') {
      suggestions.push('Verify existing wiring meets current code requirements');
    }
    
    return suggestions;
  }

  private checkLoadCompliance(parameters: any, report: any): void {
    report.references.push('220.12', '220.14');
    
    if (parameters.loadPerSqFt) {
      const buildingType = parameters.buildingType || 'DWELLING';
      const requiredLoad = GENERAL_LIGHTING_LOADS[buildingType as keyof typeof GENERAL_LIGHTING_LOADS];
      
      if (parameters.loadPerSqFt < requiredLoad) {
        report.compliant = false;
        report.violations.push({
          code: '220.12',
          description: `Lighting load ${parameters.loadPerSqFt} VA/sq ft is less than required ${requiredLoad} VA/sq ft`,
          severity: 'HIGH',
        });
      }
    }
    
    if (parameters.continuousLoad && !parameters.loadFactor125) {
      report.warnings.push({
        code: '210.19(A)(1)',
        description: 'Continuous loads require 125% factor',
        suggestion: 'Apply 1.25 multiplier to continuous loads',
      });
    }
  }

  private checkVoltageDropCompliance(parameters: any, report: any): void {
    report.references.push('210.19(A)(1)', '215.2(A)(1)');
    
    if (parameters.voltageDropPercent) {
      if (parameters.voltageDropPercent > 3 && parameters.circuitType === 'branch') {
        report.warnings.push({
          code: '210.19(A)(1) Informational Note',
          description: `Voltage drop ${parameters.voltageDropPercent}% exceeds 3% recommendation for branch circuits`,
          suggestion: 'Consider larger conductor size',
        });
      }
      
      if (parameters.voltageDropPercent > 5) {
        report.warnings.push({
          code: '215.2(A)(1) Informational Note',
          description: `Total voltage drop ${parameters.voltageDropPercent}% exceeds 5% recommendation`,
          suggestion: 'Increase conductor size or reduce circuit length',
        });
      }
    }
  }

  private checkConduitFillCompliance(parameters: any, report: any): void {
    report.references.push('Chapter 9 Table 1');
    
    if (parameters.fillPercent && parameters.conductorCount) {
      let maxFill = 40; // More than 2 conductors
      if (parameters.conductorCount === 1) maxFill = 53;
      else if (parameters.conductorCount === 2) maxFill = 31;
      
      if (parameters.fillPercent > maxFill) {
        report.compliant = false;
        report.violations.push({
          code: 'Chapter 9 Table 1',
          description: `Conduit fill ${parameters.fillPercent}% exceeds ${maxFill}% limit for ${parameters.conductorCount} conductors`,
          severity: 'HIGH',
        });
      }
    }
  }

  private checkGroundingCompliance(parameters: any, report: any): void {
    report.references.push('250.122', '250.4');
    
    if (parameters.groundWireSize && parameters.overcurrentDevice) {
      const requiredSize = this.getRequiredGroundSize(parameters.overcurrentDevice);
      
      if (parameters.groundWireSize > requiredSize) {
        report.violations.push({
          code: '250.122',
          description: `Ground wire ${parameters.groundWireSize} AWG is smaller than required ${requiredSize} AWG`,
          severity: 'HIGH',
        });
      }
    }
  }

  private getRequiredGroundSize(ocpd: number): number {
    if (ocpd <= 15) return 14;
    if (ocpd <= 20) return 12;
    if (ocpd <= 60) return 10;
    if (ocpd <= 100) return 8;
    if (ocpd <= 200) return 6;
    if (ocpd <= 300) return 4;
    if (ocpd <= 400) return 3;
    return 2;
  }

  private generateComplianceRecommendations(report: any): string[] {
    const recommendations = [];
    
    if (!report.compliant) {
      recommendations.push('Address all code violations before proceeding');
    }
    
    report.violations.forEach((violation: any) => {
      if (violation.severity === 'HIGH') {
        recommendations.push(`Critical: Fix ${violation.code} violation immediately`);
      }
    });
    
    report.warnings.forEach((warning: any) => {
      if (warning.suggestion) {
        recommendations.push(warning.suggestion);
      }
    });
    
    return recommendations;
  }
}