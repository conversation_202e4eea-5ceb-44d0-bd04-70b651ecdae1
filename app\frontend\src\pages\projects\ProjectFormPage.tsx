import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'react-hot-toast';
import { api } from '../../services/api';
import { projectService } from '../../services/projectService';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';

// Form validation schema based on Project type
const projectFormSchema = z.object({
  customer_id: z.string().min(1, 'Customer is required'),
  name: z.string().min(1, 'Project name is required').max(255),
  address: z.string().min(1, 'Address is required'),
  city: z.string().min(1, 'City is required'),
  state: z.string().length(2, 'State must be 2 characters'),
  zip: z.string().regex(/^\d{5}(-\d{4})?$/, 'Invalid ZIP code format'),
  type: z.enum(['RESIDENTIAL', 'COMMERCIAL', 'INDUSTRIAL']),
  status: z.enum(['PLANNING', 'APPROVED', 'IN_PROGRESS', 'COMPLETED', 'ON_HOLD']),
  voltage_system: z.enum(['120/240V_1PH', '208V_3PH', '240V_3PH', '480V_3PH', '277/480V_3PH']),
  service_size: z.number().int().positive('Service size must be a positive number'),
  square_footage: z.number().int().positive().optional(),
  permit_number: z.string().optional(),
  permit_expiry: z.string().optional(),
  inspection_status: z.enum(['PENDING', 'ROUGH_IN_PASSED', 'FINAL_PASSED', 'FAILED']).optional(),
});

type ProjectFormData = z.infer<typeof projectFormSchema>;

export const ProjectFormPage: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const isEdit = !!id && id !== 'new';
  const [loading, setLoading] = useState(false);
  const [customers, setCustomers] = useState<any[]>([]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    reset,
  } = useForm<ProjectFormData>({
    resolver: zodResolver(projectFormSchema),
    defaultValues: {
      status: 'PLANNING',
      type: 'RESIDENTIAL',
      voltage_system: '120/240V_1PH',
      service_size: 200,
    },
  });

  useEffect(() => {
    loadCustomers();
    if (isEdit) {
      loadProject();
    }
  }, [id]);

  const loadCustomers = async () => {
    try {
      const response = await api.get('/customers');
      const customerData = response.data?.data || response.data || [];
      setCustomers(customerData);
    } catch (error) {
      toast.error('Failed to load customers');
      console.error('Error loading customers:', error);
    }
  };

  const loadProject = async () => {
    if (!id) return;
    try {
      setLoading(true);
      const project = await projectService.getProject(id);
      reset({
        ...project,
        zip: project.zip || project.zip_code,  // Handle both field names for compatibility
        service_size: project.service_size,
        square_footage: project.square_footage || undefined,
        permit_expiry: project.permit_expiry ? new Date(project.permit_expiry).toISOString().split('T')[0] : undefined,
      });
    } catch (error) {
      toast.error('Failed to load project');
      console.error('Error loading project:', error);
      navigate('/projects');
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (data: ProjectFormData) => {
    try {
      setLoading(true);
      
      const projectData = {
        ...data,
        service_size: Number(data.service_size),
        square_footage: data.square_footage ? Number(data.square_footage) : undefined,
        permit_expiry: data.permit_expiry ? new Date(data.permit_expiry) : undefined,
      };

      if (isEdit && id) {
        await projectService.updateProject(id, projectData);
        toast.success('Project updated successfully');
      } else {
        await projectService.createProject(projectData);
        toast.success('Project created successfully');
      }
      
      navigate('/projects');
    } catch (error: any) {
      console.error('Error saving project:', error);
      toast.error(error.response?.data?.message || 'Failed to save project');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-6">
        <button
          onClick={() => navigate('/projects')}
          className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
        >
          <ArrowLeftIcon className="h-4 w-4 mr-1" />
          Back to Projects
        </button>
      </div>

      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
          {isEdit ? 'Edit Project' : 'New Project'}
        </h1>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Customer Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Customer *
            </label>
            <select
              {...register('customer_id')}
              className="input w-full"
              disabled={loading}
            >
              <option value="">Select a customer</option>
              {customers.map((customer) => (
                <option key={customer.id} value={customer.id}>
                  {customer.name}
                </option>
              ))}
            </select>
            {errors.customer_id && (
              <p className="mt-1 text-sm text-red-600">{errors.customer_id.message}</p>
            )}
          </div>

          {/* Project Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Project Name *
            </label>
            <input
              type="text"
              {...register('name')}
              className="input w-full"
              disabled={loading}
            />
            {errors.name && (
              <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
            )}
          </div>

          {/* Address Fields */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Address *
              </label>
              <input
                type="text"
                {...register('address')}
                className="input w-full"
                disabled={loading}
              />
              {errors.address && (
                <p className="mt-1 text-sm text-red-600">{errors.address.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                City *
              </label>
              <input
                type="text"
                {...register('city')}
                className="input w-full"
                disabled={loading}
              />
              {errors.city && (
                <p className="mt-1 text-sm text-red-600">{errors.city.message}</p>
              )}
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  State *
                </label>
                <input
                  type="text"
                  {...register('state')}
                  className="input w-full"
                  maxLength={2}
                  disabled={loading}
                />
                {errors.state && (
                  <p className="mt-1 text-sm text-red-600">{errors.state.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  ZIP *
                </label>
                <input
                  type="text"
                  {...register('zip')}
                  className="input w-full"
                  disabled={loading}
                />
                {errors.zip && (
                  <p className="mt-1 text-sm text-red-600">{errors.zip.message}</p>
                )}
              </div>
            </div>
          </div>

          {/* Project Type and Status */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Project Type *
              </label>
              <select
                {...register('type')}
                className="input w-full"
                disabled={loading}
              >
                <option value="RESIDENTIAL">Residential</option>
                <option value="COMMERCIAL">Commercial</option>
                <option value="INDUSTRIAL">Industrial</option>
              </select>
              {errors.type && (
                <p className="mt-1 text-sm text-red-600">{errors.type.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Status *
              </label>
              <select
                {...register('status')}
                className="input w-full"
                disabled={loading}
              >
                <option value="PLANNING">Planning</option>
                <option value="APPROVED">Approved</option>
                <option value="IN_PROGRESS">In Progress</option>
                <option value="COMPLETED">Completed</option>
                <option value="ON_HOLD">On Hold</option>
              </select>
              {errors.status && (
                <p className="mt-1 text-sm text-red-600">{errors.status.message}</p>
              )}
            </div>
          </div>

          {/* Electrical Specifications */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Voltage System *
              </label>
              <select
                {...register('voltage_system')}
                className="input w-full"
                disabled={loading}
              >
                <option value="120/240V_1PH">120/240V Single Phase</option>
                <option value="208V_3PH">208V 3 Phase</option>
                <option value="240V_3PH">240V 3 Phase</option>
                <option value="480V_3PH">480V 3 Phase</option>
                <option value="277/480V_3PH">277/480V 3 Phase</option>
              </select>
              {errors.voltage_system && (
                <p className="mt-1 text-sm text-red-600">{errors.voltage_system.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Service Size (Amps) *
              </label>
              <input
                type="number"
                {...register('service_size', { valueAsNumber: true })}
                className="input w-full"
                disabled={loading}
              />
              {errors.service_size && (
                <p className="mt-1 text-sm text-red-600">{errors.service_size.message}</p>
              )}
            </div>
          </div>

          {/* Optional Fields */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Square Footage
              </label>
              <input
                type="number"
                {...register('square_footage', { valueAsNumber: true })}
                className="input w-full"
                disabled={loading}
              />
              {errors.square_footage && (
                <p className="mt-1 text-sm text-red-600">{errors.square_footage.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Permit Number
              </label>
              <input
                type="text"
                {...register('permit_number')}
                className="input w-full"
                disabled={loading}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Permit Expiry
              </label>
              <input
                type="date"
                {...register('permit_expiry')}
                className="input w-full"
                disabled={loading}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Inspection Status
              </label>
              <select
                {...register('inspection_status')}
                className="input w-full"
                disabled={loading}
              >
                <option value="">Not Set</option>
                <option value="PENDING">Pending</option>
                <option value="ROUGH_IN_PASSED">Rough-In Passed</option>
                <option value="FINAL_PASSED">Final Passed</option>
                <option value="FAILED">Failed</option>
              </select>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-4 pt-6 border-t">
            <button
              type="button"
              onClick={() => navigate('/projects')}
              className="btn-secondary"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="btn-primary"
              disabled={loading}
            >
              {loading ? 'Saving...' : isEdit ? 'Update Project' : 'Create Project'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};