#!/usr/bin/env ts-node

import { redisManager } from '../src/services/redis-manager';

async function clearRateLimits() {
  console.log('Connecting to Redis...');
  const connected = await redisManager.connect();
  
  if (!connected) {
    console.error('Failed to connect to Redis');
    process.exit(1);
  }

  const redis = redisManager.getClient();
  if (!redis) {
    console.error('Redis client not available');
    process.exit(1);
  }

  try {
    console.log('Clearing rate limit keys...');
    
    // Get all rate limit keys
    const patterns = [
      'rl:api:*',
      'rl:auth:*',
      'rl:password:*',
      'rl:export:*',
      'rl:calc:*',
      'rl:upload:*',
      'rl:apikey:*',
      'rl:tier:*'
    ];

    let totalDeleted = 0;

    for (const pattern of patterns) {
      const keys = await redis.keys(pattern);
      if (keys.length > 0) {
        const deleted = await redis.del(...keys);
        console.log(`Deleted ${deleted} keys matching pattern: ${pattern}`);
        totalDeleted += deleted;
      }
    }

    console.log(`\nTotal keys deleted: ${totalDeleted}`);
    console.log('Rate limits cleared successfully!');
  } catch (error) {
    console.error('Error clearing rate limits:', error);
    process.exit(1);
  } finally {
    await redisManager.disconnect();
    process.exit(0);
  }
}

// Run if called directly
if (require.main === module) {
  clearRateLimits();
}

export { clearRateLimits };