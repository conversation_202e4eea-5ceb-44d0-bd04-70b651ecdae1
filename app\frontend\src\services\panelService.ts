import { Panel, Circuit, PanelLoadCalculation } from '@electrical/shared';
import { api } from './api';
import { cacheService } from './cacheService';

interface PanelSchedule {
  panel: Panel;
  circuits: Circuit[];
  loadCalculation: PanelLoadCalculation | null;
  summary: {
    totalConnectedLoad: number;
    totalDemandLoad: number;
    loadPercentage: number;
    phaseImbalance: number;
    spacesUsed: number;
    spacesAvailable: number;
  };
}

export const panelService = {
  // Panel operations
  async createPanel(data: Partial<Panel>): Promise<Panel> {
    const response = await api.post('/panels', data);
    const panel = response.data.data || response.data;
    
    // Invalidate cache for this project
    if (panel.project_id) {
      cacheService.invalidate(`panels:project:${panel.project_id}`);
    }
    
    return panel;
  },

  async getPanelsByProject(projectId: string): Promise<Panel[]> {
    // Use cache for panel data
    const cacheKey = `panels:project:${projectId}`;
    
    return cacheService.cached(
      cacheKey,
      async () => {
        const response = await api.get(`/panels/project/${projectId}`);
        return response.data.data || response.data;
      },
      { ttl: 3 * 60 * 1000 } // Cache for 3 minutes
    );
  },

  async getPanel(panelId: string): Promise<Panel> {
    const response = await api.get(`/panels/${panelId}`);
    return response.data.data || response.data;
  },

  async updatePanel(panelId: string, data: Partial<Panel>): Promise<Panel> {
    const response = await api.put(`/panels/${panelId}`, data);
    const panel = response.data.data || response.data;
    
    // Invalidate cache for this project
    if (panel.project_id) {
      cacheService.invalidate(`panels:project:${panel.project_id}`);
    }
    cacheService.invalidate(`panels:${panelId}`);
    
    return panel;
  },

  async deletePanel(panelId: string): Promise<void> {
    // Get panel info first to invalidate project cache
    try {
      const panel = await this.getPanel(panelId);
      if (panel.project_id) {
        cacheService.invalidate(`panels:project:${panel.project_id}`);
      }
    } catch {
      // Continue with deletion even if we can't get panel info
    }
    
    await api.delete(`/panels/${panelId}`);
    cacheService.invalidate(`panels:${panelId}`);
  },

  async calculatePanelLoad(panelId: string): Promise<PanelLoadCalculation> {
    const response = await api.post(`/panels/${panelId}/calculate-load`);
    return response.data.data || response.data;
  },

  async getPanelSchedule(panelId: string): Promise<PanelSchedule> {
    const response = await api.get(`/panels/${panelId}/schedule`);
    return response.data.data || response.data;
  },

  async balancePanel(panelId: string): Promise<Panel> {
    const response = await api.post(`/panels/${panelId}/balance`);
    return response.data.data || response.data;
  },

  // Circuit operations
  async createCircuit(panelId: string, data: Partial<Circuit>): Promise<Circuit> {
    const response = await api.post(`/panels/${panelId}/circuits`, data);
    return response.data.data || response.data;
  },

  async getCircuitsByPanel(panelId: string): Promise<Circuit[]> {
    const response = await api.get(`/panels/${panelId}/circuits`);
    return response.data.data || response.data;
  },

  async updateCircuit(circuitId: string, data: Partial<Circuit>): Promise<Circuit> {
    const response = await api.put(`/panels/circuits/${circuitId}`, data);
    return response.data.data || response.data;
  },

  async deleteCircuit(circuitId: string): Promise<void> {
    await api.delete(`/panels/circuits/${circuitId}`);
  },

  async moveCircuit(
    circuitId: string, 
    targetPanelId: string, 
    targetCircuitNumber: number
  ): Promise<Circuit> {
    const response = await api.put(`/panels/circuits/${circuitId}/move`, {
      target_panel_id: targetPanelId,
      target_circuit_number: targetCircuitNumber,
    });
    return response.data.data || response.data;
  },

  async bulkCreateCircuits(
    panelId: string, 
    circuits: Partial<Circuit>[]
  ): Promise<Circuit[]> {
    const response = await api.post(`/panels/${panelId}/circuits/bulk`, { circuits });
    return response.data.data || response.data;
  },
};