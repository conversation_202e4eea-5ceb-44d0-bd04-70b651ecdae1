import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuthStore } from './stores/auth';
import { Layout } from './components/layout/Layout';
import { ProtectedRoute } from './components/auth/ProtectedRoute';
import { LoginPage } from './pages/auth/LoginPage';
import { RegisterPage } from './pages/auth/RegisterPage';
import { useAuthInit } from './hooks/useAuthInit';
import { DashboardPage } from './pages/DashboardPage';
import { CustomersPage } from './pages/customers/CustomersPage';
import { ProjectsPage } from './pages/projects/ProjectsPage';
import { ProjectDetailPage } from './pages/projects/ProjectDetailPage';
import { ProjectFormPage } from './pages/projects/ProjectFormPage';
import { QuotesPage } from './pages/quotes/QuotesPage';
import { QuoteFormPage } from './pages/quotes/QuoteFormPage';
import { QuoteDetailPage } from './pages/quotes/QuoteDetailPage';
import { QuoteAIAssistantPage } from './pages/quotes/QuoteAIAssistantPage';
import { EstimatesPage } from './pages/estimates/EstimatesPage';
import { CalculationsPage } from './pages/calculations/CalculationsPage';
import { MaterialsPage } from './pages/materials/MaterialsPage';
import { PanelsPage } from './pages/PanelsPage';
import { PanelFormPage } from './pages/PanelFormPage';
import { PanelSchedulePage } from './pages/PanelSchedulePage';
import { ArcFlashPage } from './pages/arc-flash/ArcFlashPage';
import { ShortCircuitPage } from './pages/ShortCircuitPage';
import PermitDocumentsPage from './pages/permits/PermitDocumentsPage';
import { InspectionsPage } from './pages/inspections/InspectionsPage';
import { InspectionFormPage } from './pages/inspections/InspectionFormPage';
import { InspectionChecklistPage } from './pages/inspections/InspectionChecklistPage';
import { InspectionMobilePage } from './pages/inspections/InspectionMobilePage';
import AnalyticsDashboard from './pages/analytics/AnalyticsDashboard';
import { ReportsPage } from './pages/reports/ReportsPage';
import { SettingsPage } from './pages/settings/SettingsPage';
import { AIAssistant } from './components/ai/AIAssistant';
import { AICalculationAssistant } from './components/ai/AICalculationAssistant';
import { useEffect } from 'react';
// import { useOfflineSync } from './hooks/useOfflineSync';
import { EnhancedErrorBoundary } from './components/EnhancedErrorBoundary';
import { useAuthRedirect } from './hooks/useAuthRedirect';
// import { ErrorHandler } from './utils/error-handler';
// import { performanceMonitor } from './services/performance-monitor';
// import { apiClient } from './utils/api-interceptors';
// import { logger } from './utils/logger';

function App(): JSX.Element {
  console.log('[App] App component rendering', new Date().toISOString());
  
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  const user = useAuthStore((state) => state.user);
  const { isAuthenticated: isAuthRedirect } = useAuthRedirect();
  const { isInitialized } = useAuthInit();
  // const { initializeOfflineSync } = useOfflineSync();
  
  // Auth state is now properly managed
  
  // Ensure we have both authentication flag AND a valid user object
  const isFullyAuthenticated = isAuthenticated && user && user.id && user.email;

  useEffect(() => {
    // initializeOfflineSync();
    
    // Set up error notification handler
    // ErrorHandler.setNotificationCallback((message, type) => {
    //   // TODO: Integrate with your notification system
    //   logger.info(`Notification: ${message}`, { type });
    // });
    
    // Clean up performance monitoring on unmount
    // return () => {
    //   performanceMonitor.cleanup();
    // };
  }, []);

  // TEMPORARY FIX: Force initialization after 3 seconds if stuck
  const [forceRender, setForceRender] = React.useState(false);

  React.useEffect(() => {
    const timer = setTimeout(() => {
      console.log('[App] Force rendering after 3 seconds, isInitialized:', isInitialized);
      setForceRender(true);
    }, 3000);

    return () => clearTimeout(timer);
  }, [isInitialized]);

  // Show loading screen while auth is initializing
  if (!isInitialized && !forceRender) {
    return (
      <div className="flex items-center justify-center h-screen bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">Initializing...</p>
        </div>
      </div>
    );
  }

  
  return (
    <EnhancedErrorBoundary
      showDetails={import.meta.env.DEV}
      resetOnPropsChange
      resetKeys={[isFullyAuthenticated]}
    >
      <Routes>
        {/* Public routes */}
        <Route path="/login" element={!isFullyAuthenticated ? <LoginPage /> : <Navigate to="/" />} />
        <Route path="/register" element={!isFullyAuthenticated ? <RegisterPage /> : <Navigate to="/" />} />
        
        {/* Mobile inspection route (no auth required for QR code access) */}
        <Route path="/inspection/mobile/:qrCodeId" element={<InspectionMobilePage />} />
        
        {/* Protected routes */}
        <Route element={<ProtectedRoute />}>
          <Route element={<Layout />}>
            <Route path="/" element={<DashboardPage />} />
            <Route path="/customers" element={<CustomersPage />} />
            <Route path="/customers/:id" element={<CustomersPage />} />
            <Route path="/projects" element={<ProjectsPage />} />
            <Route path="/projects/new" element={<ProjectFormPage />} />
            <Route path="/projects/:id" element={<ProjectDetailPage />} />
            <Route path="/projects/:id/edit" element={<ProjectFormPage />} />
            <Route path="/projects/:projectId/panels" element={<PanelsPage />} />
            <Route path="/projects/:projectId/panels/new" element={<PanelFormPage />} />
            <Route path="/projects/:projectId/panels/:panelId" element={<PanelSchedulePage />} />
            <Route path="/projects/:projectId/panels/:panelId/edit" element={<PanelFormPage />} />
            <Route path="/projects/:projectId/panels/:panelId/schedule" element={<PanelSchedulePage />} />
            <Route path="/projects/:projectId/arc-flash" element={<ArcFlashPage />} />
            <Route path="/projects/:projectId/short-circuit" element={<ShortCircuitPage />} />
            <Route path="/projects/:projectId/permits" element={<PermitDocumentsPage />} />
            <Route path="/projects/:projectId/inspections" element={<InspectionsPage />} />
            <Route path="/projects/:projectId/inspections/new" element={<InspectionFormPage />} />
            <Route path="/projects/:projectId/inspections/:inspectionId" element={<InspectionChecklistPage />} />
            {/* Quote routes - more specific routes first */}
            <Route path="/quotes/new" element={<QuoteFormPage />} />
            <Route path="/quotes/ai-assistant" element={<QuoteAIAssistantPage />} />
            <Route path="/quotes/:id/edit" element={<QuoteFormPage />} />
            <Route path="/quotes/:id" element={<QuoteDetailPage />} />
            <Route path="/quotes" element={<QuotesPage />} />
            <Route path="/estimates" element={<EstimatesPage />} />
            <Route path="/estimates/:id" element={<EstimatesPage />} />
            <Route path="/calculations" element={<CalculationsPage />} />
            <Route path="/materials" element={<MaterialsPage />} />
            <Route path="/analytics" element={<AnalyticsDashboard />} />
            <Route path="/reports" element={<ReportsPage />} />
            <Route path="/settings" element={<SettingsPage />} />
          </Route>
        </Route>
        
        {/* Catch all */}
        <Route path="*" element={<Navigate to="/" />} />
      </Routes>
      
      {/* AI Assistant - shown when authenticated */}
      {/* {isFullyAuthenticated && <AIAssistant />} */}
      {isFullyAuthenticated && <AICalculationAssistant />}
    </EnhancedErrorBoundary>
  );
}

export default App;