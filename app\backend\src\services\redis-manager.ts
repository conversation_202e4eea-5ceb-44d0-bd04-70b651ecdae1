import Redis from 'ioredis';
import { EventEmitter } from 'events';
import { logger } from '../utils/logger';
import { config } from '../config';

export interface RedisManagerOptions {
  host?: string;
  port?: number;
  password?: string;
  maxReconnectAttempts?: number;
  reconnectInterval?: number;
  enableInDevelopment?: boolean;
}

export interface RedisHealth {
  isConnected: boolean;
  lastError?: string;
  lastConnectionAttempt?: Date;
  reconnectAttempts: number;
  uptime?: number;
}

/**
 * RedisManager handles Redis connections with proper fallback mechanisms
 * for development environments where Redis might not be available.
 */
export class RedisManager extends EventEmitter {
  private static instance: RedisManager;
  private redis: Redis | null = null;
  private options: Required<RedisManagerOptions>;
  private reconnectAttempts = 0;
  private reconnectTimer?: NodeJS.Timeout;
  private lastError?: string;
  private lastConnectionAttempt?: Date;
  private connectionStartTime?: Date;
  private isShuttingDown = false;

  private constructor(options: RedisManagerOptions = {}) {
    super();
    
    // Don't default to localhost if Redis host is not configured
    const configuredHost = options.host || config.redis.host;
    
    this.options = {
      host: configuredHost || '',  // Empty string means no Redis
      port: options.port || config.redis.port || 6379,
      password: options.password || config.redis.password || '',
      maxReconnectAttempts: options.maxReconnectAttempts || 3,
      reconnectInterval: options.reconnectInterval || 5000,
      enableInDevelopment: options.enableInDevelopment ?? true,
    };
  }

  static getInstance(options?: RedisManagerOptions): RedisManager {
    if (!RedisManager.instance) {
      RedisManager.instance = new RedisManager(options);
    }
    return RedisManager.instance;
  }

  async connect(): Promise<boolean> {
    // Skip Redis if no host is configured
    if (!this.options.host || this.options.host === '' || this.options.host === 'undefined') {
      logger.info('Redis host not configured - skipping connection');
      logger.info('Application will run without Redis caching and advanced features');
      return false;
    }

    // Skip Redis in development if not enabled
    if (config.isDevelopment && !this.options.enableInDevelopment) {
      logger.info('Redis disabled in development environment');
      return false;
    }

    if (this.redis && this.redis.status === 'ready') {
      return true;
    }

    this.lastConnectionAttempt = new Date();

    try {
      const redisOptions: any = {
        host: this.options.host,
        port: this.options.port,
        maxRetriesPerRequest: null,
        enableOfflineQueue: false,
        retryStrategy: () => null,
        lazyConnect: true,
      };

      if (this.options.password) {
        redisOptions.password = this.options.password;
      }

      this.redis = new Redis(redisOptions);

      // Set up event handlers
      this.setupEventHandlers();

      // Try to connect
      await this.redis.connect();
      await this.redis.ping();

      this.connectionStartTime = new Date();
      this.reconnectAttempts = 0;
      this.lastError = undefined;
      
      logger.info('Redis connected successfully', {
        host: this.options.host,
        port: this.options.port,
      });

      this.emit('connected');
      return true;
    } catch (error) {
      this.lastError = (error as Error).message;
      this.handleConnectionError(error as Error);
      return false;
    }
  }

  private setupEventHandlers(): void {
    if (!this.redis) return;

    this.redis.on('error', (error) => {
      if (!this.isShuttingDown) {
        logger.error('Redis error:', error);
        this.lastError = error.message;
        this.emit('error', error);
      }
    });

    this.redis.on('close', () => {
      if (!this.isShuttingDown) {
        logger.warn('Redis connection closed');
        this.connectionStartTime = undefined;
        this.emit('disconnected');
        this.scheduleReconnect();
      }
    });

    this.redis.on('reconnecting', () => {
      logger.info('Redis reconnecting...');
      this.emit('reconnecting');
    });

    this.redis.on('ready', () => {
      logger.info('Redis ready');
      this.connectionStartTime = new Date();
      this.emit('ready');
    });
  }

  private handleConnectionError(error: Error): void {
    const errorMessage = error.message || 'Unknown error';
    
    if (config.isDevelopment) {
      logger.warn(`Redis connection failed in development: ${errorMessage}`);
      logger.warn('Application will continue without Redis caching and rate limiting');
    } else {
      logger.error(`Redis connection failed: ${errorMessage}`);
    }

    this.cleanupConnection();
    
    if (!config.isDevelopment) {
      this.scheduleReconnect();
    }
  }

  private scheduleReconnect(): void {
    if (this.isShuttingDown || this.reconnectTimer) return;

    if (this.reconnectAttempts >= this.options.maxReconnectAttempts) {
      logger.error('Max Redis reconnection attempts reached. Giving up.');
      this.emit('maxReconnectAttemptsReached');
      return;
    }

    this.reconnectAttempts++;
    
    logger.info(`Scheduling Redis reconnection attempt ${this.reconnectAttempts}/${this.options.maxReconnectAttempts} in ${this.options.reconnectInterval}ms`);
    
    this.reconnectTimer = setTimeout(() => {
      this.reconnectTimer = undefined;
      this.connect().catch((error) => {
        logger.error('Redis reconnection failed:', error);
      });
    }, this.options.reconnectInterval);
  }

  private cleanupConnection(): void {
    if (this.redis) {
      try {
        this.redis.removeAllListeners();
        this.redis.disconnect();
      } catch (error) {
        // Ignore errors during cleanup
      }
      this.redis = null;
    }
  }

  getClient(): Redis | null {
    if (this.redis && this.redis.status === 'ready') {
      return this.redis;
    }
    return null;
  }

  isConnected(): boolean {
    return this.redis !== null && this.redis.status === 'ready';
  }

  getHealth(): RedisHealth {
    return {
      isConnected: this.isConnected(),
      lastError: this.lastError,
      lastConnectionAttempt: this.lastConnectionAttempt,
      reconnectAttempts: this.reconnectAttempts,
      uptime: this.connectionStartTime 
        ? Math.floor((Date.now() - this.connectionStartTime.getTime()) / 1000)
        : undefined,
    };
  }

  async disconnect(): Promise<void> {
    this.isShuttingDown = true;

    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = undefined;
    }

    if (this.redis) {
      try {
        await this.redis.quit();
      } catch (error) {
        logger.error('Error during Redis disconnect:', error);
      }
      this.cleanupConnection();
    }

    this.emit('shutdown');
  }

  /**
   * Execute a Redis command with automatic fallback
   */
  async execute<T>(
    operation: (client: Redis) => Promise<T>,
    fallback?: T
  ): Promise<T | undefined> {
    const client = this.getClient();
    
    if (!client) {
      if (fallback !== undefined) {
        return fallback;
      }
      return undefined;
    }

    try {
      return await operation(client);
    } catch (error) {
      logger.error('Redis operation failed:', error);
      if (fallback !== undefined) {
        return fallback;
      }
      throw error;
    }
  }

  /**
   * Safe set operation with TTL
   */
  async set(key: string, value: string, ttl?: number): Promise<boolean> {
    return this.execute(
      async (client) => {
        if (ttl) {
          await client.setex(key, ttl, value);
        } else {
          await client.set(key, value);
        }
        return true;
      },
      false
    ) as Promise<boolean>;
  }

  /**
   * Safe get operation
   */
  async get(key: string): Promise<string | null> {
    return this.execute(
      async (client) => client.get(key),
      null
    ) as Promise<string | null>;
  }

  /**
   * Safe delete operation
   */
  async del(key: string | string[]): Promise<number> {
    return this.execute(
      async (client) => {
        if (Array.isArray(key)) {
          return key.length > 0 ? client.del(...key) : 0;
        }
        return client.del(key);
      },
      0
    ) as Promise<number>;
  }

  /**
   * Safe exists operation
   */
  async exists(key: string): Promise<boolean> {
    return this.execute(
      async (client) => {
        const result = await client.exists(key);
        return result === 1;
      },
      false
    ) as Promise<boolean>;
  }

  /**
   * Safe expire operation
   */
  async expire(key: string, ttl: number): Promise<boolean> {
    return this.execute(
      async (client) => {
        const result = await client.expire(key, ttl);
        return result === 1;
      },
      false
    ) as Promise<boolean>;
  }

  /**
   * Safe incr operation
   */
  async incr(key: string): Promise<number | null> {
    return this.execute(
      async (client) => client.incr(key),
      null
    ) as Promise<number | null>;
  }

  /**
   * Safe transaction operation
   */
  async multi(operations: Array<[string, ...any[]]>): Promise<any[] | null> {
    return this.execute(
      async (client) => {
        const pipeline = client.multi();
        for (const [command, ...args] of operations) {
          (pipeline as any)[command](...args);
        }
        const results = await pipeline.exec();
        return results ? results.map(([err, result]) => err ? null : result) : null;
      },
      null
    ) as Promise<any[] | null>;
  }
}

// Export singleton instance
export const redisManager = RedisManager.getInstance();