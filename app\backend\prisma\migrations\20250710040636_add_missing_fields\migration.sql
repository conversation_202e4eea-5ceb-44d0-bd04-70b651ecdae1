-- AlterTable
ALTER TABLE "ArcFlashCalculation" ADD COLUMN "arc_current" REAL;

-- AlterTable
ALTER TABLE "Customer" ADD COLUMN "type" TEXT;

-- AlterTable
ALTER TABLE "Panel" ADD COLUMN "arc_flash_incident_energy" REAL;
ALTER TABLE "Panel" ADD COLUMN "short_circuit_rating" REAL;

-- AlterTable
ALTER TABLE "User" ADD COLUMN "last_login_at" DATETIME;
ALTER TABLE "User" ADD COLUMN "locked_until" DATETIME;

-- CreateTable
CREATE TABLE "AuditLog" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "user_id" TEXT,
    "action" TEXT NOT NULL,
    "resource" TEXT,
    "resource_id" TEXT,
    "details" TEXT,
    "ip_address" TEXT,
    "user_agent" TEXT,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- CreateTable
CREATE TABLE "EquipmentRecommendation" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "panel_id" TEXT,
    "equipment_type" TEXT NOT NULL,
    "manufacturer" TEXT,
    "model_number" TEXT,
    "rating" TEXT,
    "recommendation" TEXT NOT NULL,
    "priority" TEXT NOT NULL DEFAULT 'MEDIUM',
    "estimated_cost" REAL,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL
);

-- CreateIndex
CREATE INDEX "AuditLog_user_id_idx" ON "AuditLog"("user_id");

-- CreateIndex
CREATE INDEX "AuditLog_action_idx" ON "AuditLog"("action");

-- CreateIndex
CREATE INDEX "AuditLog_created_at_idx" ON "AuditLog"("created_at");

-- CreateIndex
CREATE INDEX "EquipmentRecommendation_panel_id_idx" ON "EquipmentRecommendation"("panel_id");

-- CreateIndex
CREATE INDEX "EquipmentRecommendation_equipment_type_idx" ON "EquipmentRecommendation"("equipment_type");
