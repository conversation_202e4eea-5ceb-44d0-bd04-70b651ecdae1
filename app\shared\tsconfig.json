{"compilerOptions": {"target": "ES2022", "module": "ES2022", "lib": ["ES2022"], "outDir": "./dist", "rootDir": "./src", "declaration": true, "declarationMap": true, "sourceMap": true, "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}