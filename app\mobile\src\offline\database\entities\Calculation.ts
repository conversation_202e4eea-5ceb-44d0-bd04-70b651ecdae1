// Temporarily using mock TypeORM - will be replaced with proper database solution
// Note: Decorators removed for React Native compatibility
import { BaseEntity } from './BaseEntity';
import { Project } from './Project';

// Mock entity class without decorators
export class Calculation extends BaseEntity {
  name: string;
  type: string; // 'voltage-drop', 'wire-size', 'conduit-fill', 'load', etc.
  inputs: string; // JSON string of calculation inputs
  outputs: string; // JSON string of calculation results
  formula: string;
  codeReference: string;
  notes: string;
  metadata: string; // JSON string
  createdBy: string;
  projectId: string;
  project: Project;
}