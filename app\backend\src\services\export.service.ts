import { Response } from 'express';
import { Transform, Readable } from 'stream';
import { Parser } from 'json2csv';
import { prisma } from '../database/prisma';
import { format } from 'date-fns';

export class ExportService {
  /**
   * Stream JSON data with chunking for large datasets
   */
  static streamJSON(res: Response, dataStream: Readable, name: string): void {
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', `attachment; filename="${name}-${format(new Date(), 'yyyy-MM-dd')}.json"`);
    res.setHeader('X-Content-Type-Options', 'nosniff');
    
    res.write('[');
    
    let isFirst = true;
    const transformer = new Transform({
      objectMode: true,
      transform(chunk, encoding, callback) {
        const prefix = isFirst ? '' : ',';
        isFirst = false;
        callback(null, prefix + JSON.stringify(chunk));
      }
    });
    
    dataStream
      .pipe(transformer)
      .pipe(res);
    
    transformer.on('end', () => {
      res.write(']');
      res.end();
    });
  }

  /**
   * Stream CSV data for spreadsheet export
   */
  static streamCSV(res: Response, dataStream: Readable, fields: string[], name: string): void {
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', `attachment; filename="${name}-${format(new Date(), 'yyyy-MM-dd')}.csv"`);
    
    const parser = new Parser({ fields });
    const csvStream = dataStream.pipe(parser);
    csvStream.pipe(res);
  }

  /**
   * Stream customer data export
   */
  static async streamCustomers(res: Response, format: 'json' | 'csv' = 'json'): Promise<void> {
    const batchSize = 100;
    let skip = 0;
    let hasMore = true;
    
    const dataStream = new Readable({
      objectMode: true,
      async read() {
        if (!hasMore) {
          this.push(null);
          return;
        }
        
        const customers = await prisma.customer.findMany({
          where: { deleted_at: null },
          skip,
          take: batchSize,
          include: {
            _count: {
              select: { projects: true }
            }
          }
        });
        
        if (customers.length < batchSize) {
          hasMore = false;
        }
        
        skip += batchSize;
        
        for (const customer of customers) {
          this.push({
            id: customer.id,
            name: customer.name,
            email: customer.email,
            phone: customer.phone,
            address: customer.address,
            city: customer.city,
            state: customer.state,
            zip: customer.zip,
            license_number: customer.license_number,
            credit_limit: customer.credit_limit,
            payment_terms: customer.payment_terms,
            project_count: customer._count.projects,
            created_at: customer.created_at,
            updated_at: customer.updated_at
          });
        }
      }
    });
    
    if (format === 'csv') {
      const fields = [
        'id', 'name', 'email', 'phone', 'address', 'city', 'state', 'zip',
        'license_number', 'credit_limit', 'payment_terms', 'project_count',
        'created_at', 'updated_at'
      ];
      ExportService.streamCSV(res, dataStream, fields, 'customers');
    } else {
      ExportService.streamJSON(res, dataStream, 'customers');
    }
  }

  /**
   * Stream project data export
   */
  static async streamProjects(res: Response, format: 'json' | 'csv' = 'json', filters?: any): Promise<void> {
    const batchSize = 100;
    let skip = 0;
    let hasMore = true;
    
    const where = {
      ...(filters?.status && { status: filters.status }),
      ...(filters?.type && { type: filters.type }),
      ...(filters?.customer_id && { customer_id: filters.customer_id })
    };
    
    const dataStream = new Readable({
      objectMode: true,
      async read() {
        if (!hasMore) {
          this.push(null);
          return;
        }
        
        const projects = await prisma.project.findMany({
          where,
          skip,
          take: batchSize,
          include: {
            customer: {
              select: {
                name: true,
                email: true
              }
            },
            _count: {
              select: {
                estimates: true,
                calculations: true,
                panels: true
              }
            }
          }
        });
        
        if (projects.length < batchSize) {
          hasMore = false;
        }
        
        skip += batchSize;
        
        for (const project of projects) {
          this.push({
            id: project.id,
            name: project.name,
            customer_name: project.customer.name,
            customer_email: project.customer.email,
            address: project.address,
            city: project.city,
            state: project.state,
            zip: project.zip,
            type: project.type,
            status: project.status,
            voltage_system: project.voltage_system,
            service_size: project.service_size,
            square_footage: project.square_footage,
            permit_number: project.permit_number,
            inspection_status: project.inspection_status,
            estimate_count: project._count.estimates,
            calculation_count: project._count.calculations,
            panel_count: project._count.panels,
            created_at: project.created_at,
            updated_at: project.updated_at
          });
        }
      }
    });
    
    if (format === 'csv') {
      const fields = [
        'id', 'name', 'customer_name', 'customer_email', 'address', 'city', 'state', 'zip',
        'type', 'status', 'voltage_system', 'service_size', 'square_footage',
        'permit_number', 'inspection_status', 'estimate_count', 'calculation_count',
        'panel_count', 'created_at', 'updated_at'
      ];
      ExportService.streamCSV(res, dataStream, fields, 'projects');
    } else {
      ExportService.streamJSON(res, dataStream, 'projects');
    }
  }

  /**
   * Stream estimate data export with line items
   */
  static async streamEstimates(res: Response, format: 'json' | 'csv' = 'json', projectId?: string): Promise<void> {
    const batchSize = 50; // Smaller batch for estimates with line items
    let skip = 0;
    let hasMore = true;
    
    const where = projectId ? { project_id: projectId } : {};
    
    const dataStream = new Readable({
      objectMode: true,
      async read() {
        if (!hasMore) {
          this.push(null);
          return;
        }
        
        const estimates = await prisma.estimate.findMany({
          where,
          skip,
          take: batchSize,
          include: {
            project: {
              include: {
                customer: {
                  select: {
                    name: true
                  }
                }
              }
            },
            material_items: true,
            labor_items: true,
            creator: {
              select: {
                name: true,
                email: true
              }
            }
          }
        });
        
        if (estimates.length < batchSize) {
          hasMore = false;
        }
        
        skip += batchSize;
        
        for (const estimate of estimates) {
          if (format === 'csv') {
            // For CSV, flatten the structure
            this.push({
              id: estimate.id,
              project_name: estimate.project.name,
              customer_name: estimate.project.customer.name,
              version: estimate.version,
              status: estimate.status,
              valid_until: estimate.valid_until,
              subtotal: estimate.subtotal,
              tax_total: estimate.tax_total,
              total_amount: estimate.total_amount,
              profit_margin: estimate.profit_margin,
              contingency_percent: estimate.contingency_percent,
              material_items_count: estimate.material_items.length,
              labor_items_count: estimate.labor_items.length,
              created_by: estimate.creator.name,
              created_at: estimate.created_at
            });
          } else {
            // For JSON, include full structure
            this.push(estimate);
          }
        }
      }
    });
    
    if (format === 'csv') {
      const fields = [
        'id', 'project_name', 'customer_name', 'version', 'status',
        'valid_until', 'subtotal', 'tax_total', 'total_amount',
        'profit_margin', 'contingency_percent', 'material_items_count',
        'labor_items_count', 'created_by', 'created_at'
      ];
      ExportService.streamCSV(res, dataStream, fields, 'estimates');
    } else {
      ExportService.streamJSON(res, dataStream, 'estimates');
    }
  }

  /**
   * Stream calculation history export
   */
  static async streamCalculations(res: Response, format: 'json' | 'csv' = 'json', filters?: any): Promise<void> {
    const batchSize = 200;
    let skip = 0;
    let hasMore = true;
    
    const where = {
      ...(filters?.calculation_type && { calculation_type: filters.calculation_type }),
      ...(filters?.project_id && { project_id: filters.project_id }),
      ...(filters?.performed_by && { performed_by: filters.performed_by })
    };
    
    const dataStream = new Readable({
      objectMode: true,
      async read() {
        if (!hasMore) {
          this.push(null);
          return;
        }
        
        const calculations = await prisma.calculationLog.findMany({
          where,
          skip,
          take: batchSize,
          include: {
            user: {
              select: {
                name: true,
                email: true
              }
            },
            project: {
              select: {
                name: true
              }
            }
          }
        });
        
        if (calculations.length < batchSize) {
          hasMore = false;
        }
        
        skip += batchSize;
        
        for (const calc of calculations) {
          const inputData = JSON.parse(calc.input_data);
          const outputData = JSON.parse(calc.output_data);
          
          this.push({
            id: calc.id,
            calculation_type: calc.calculation_type,
            project_name: calc.project?.name || 'N/A',
            performed_by: calc.user.name,
            performed_by_email: calc.user.email,
            created_at: calc.created_at,
            // Include key input/output fields based on calculation type
            ...(calc.calculation_type === 'LOAD_CALC' && {
              square_footage: inputData.square_footage,
              total_load_va: outputData.total_load_va,
              service_size: outputData.recommended_service_size
            }),
            ...(calc.calculation_type === 'VOLTAGE_DROP' && {
              distance: inputData.distance,
              voltage: inputData.voltage,
              amperage: inputData.amperage,
              voltage_drop_percent: outputData.percent_drop
            }),
            ...(calc.calculation_type === 'WIRE_SIZE' && {
              load_amps: inputData.load_amps,
              voltage: inputData.voltage,
              selected_wire_size: outputData.final_wire_size
            })
          });
        }
      }
    });
    
    if (format === 'csv') {
      const fields = [
        'id', 'calculation_type', 'project_name', 'performed_by',
        'performed_by_email', 'created_at', 'square_footage',
        'total_load_va', 'service_size', 'distance', 'voltage',
        'amperage', 'voltage_drop_percent', 'load_amps', 'selected_wire_size'
      ];
      ExportService.streamCSV(res, dataStream, fields, 'calculations');
    } else {
      ExportService.streamJSON(res, dataStream, 'calculations');
    }
  }

  /**
   * Stream material price history export
   */
  static async streamMaterialPrices(res: Response, format: 'json' | 'csv' = 'json'): Promise<void> {
    const batchSize = 500;
    let skip = 0;
    let hasMore = true;
    
    const dataStream = new Readable({
      objectMode: true,
      async read() {
        if (!hasMore) {
          this.push(null);
          return;
        }
        
        const prices = await prisma.materialPriceHistory.findMany({
          skip,
          take: batchSize,
          orderBy: [
            { catalog_number: 'asc' },
            { effective_date: 'desc' }
          ]
        });
        
        if (prices.length < batchSize) {
          hasMore = false;
        }
        
        skip += batchSize;
        
        for (const price of prices) {
          this.push({
            id: price.id,
            catalog_number: price.catalog_number,
            supplier: price.supplier,
            unit_cost: price.unit_cost,
            effective_date: price.effective_date,
            created_at: price.created_at
          });
        }
      }
    });
    
    if (format === 'csv') {
      const fields = [
        'id', 'catalog_number', 'supplier', 'unit_cost',
        'effective_date', 'created_at'
      ];
      ExportService.streamCSV(res, dataStream, fields, 'material-prices');
    } else {
      ExportService.streamJSON(res, dataStream, 'material-prices');
    }
  }
}