{"hooks": {"PreToolUse": [{"matcher": ".*", "command": "echo 'Starting tool: {{tool}}' && /home/<USER>/.claude/hooks/guide_edits.sh"}, {"matcher": "Edit|Write", "command": "/home/<USER>/.claude/hooks/guide_edits.sh"}, {"matcher": ".*", "command": "/home/<USER>/.claude/hooks/track_progress.sh"}], "PostToolUse": [{"matcher": "Edit|Write", "command": "/home/<USER>/.claude/hooks/validate_code.sh"}, {"matcher": "Edit", "command": "/home/<USER>/.claude/hooks/visual_test.sh"}], "Stop": [{"matcher": ".*", "command": "/home/<USER>/.claude/hooks/cleanup_session.sh"}]}}