import { AxiosResponse } from 'axios';
import { requestThrottler } from './request-throttler';
import { errorService } from '../services/errorService';

interface BatchRequest<T> {
  id: string;
  request: () => Promise<T>;
  priority?: number;
  retryCount?: number;
  maxRetries?: number;
}

interface BatchResult<T> {
  id: string;
  success: boolean;
  data?: T;
  error?: Error;
}

interface BatchOptions {
  maxConcurrent?: number;
  delayBetweenBatches?: number;
  onProgress?: (completed: number, total: number) => void;
  stopOnError?: boolean;
}

export class BatchRequestHandler {
  /**
   * Execute multiple requests in batches with automatic throttling
   */
  static async executeBatch<T>(
    requests: BatchRequest<T>[],
    options: BatchOptions = {}
  ): Promise<BatchResult<T>[]> {
    const {
      maxConcurrent = 3,
      delayBetweenBatches = 100,
      onProgress,
      stopOnError = false,
    } = options;

    const results: BatchResult<T>[] = [];
    const queue = [...requests];
    let completed = 0;

    while (queue.length > 0) {
      // Get the next batch
      const batch = queue.splice(0, maxConcurrent);
      
      // Execute batch in parallel with throttling
      const batchPromises = batch.map(async (req) => {
        try {
          const data = await requestThrottler.throttle(
            req.request,
            undefined,
            req.priority || 0
          );
          
          results.push({
            id: req.id,
            success: true,
            data,
          });
          
          completed++;
          if (onProgress) {
            onProgress(completed, requests.length);
          }
        } catch (error) {
          const err = error as Error;
          
          // Check if we should retry
          const retryCount = req.retryCount || 0;
          const maxRetries = req.maxRetries || 2;
          
          if (retryCount < maxRetries && this.isRetriableError(err)) {
            // Add back to queue with increased retry count
            queue.push({
              ...req,
              retryCount: retryCount + 1,
              priority: (req.priority || 0) - 1, // Lower priority for retries
            });
            
            errorService.logWarning('Batch request failed, retrying', {
              id: req.id,
              attempt: retryCount + 1,
              maxAttempts: maxRetries,
              error: err.message,
            });
          } else {
            // Final failure
            results.push({
              id: req.id,
              success: false,
              error: err,
            });
            
            completed++;
            if (onProgress) {
              onProgress(completed, requests.length);
            }
            
            if (stopOnError) {
              throw err;
            }
          }
        }
      });

      // Wait for batch to complete
      await Promise.allSettled(batchPromises);

      // Add delay between batches to avoid overwhelming the server
      if (queue.length > 0) {
        await new Promise(resolve => setTimeout(resolve, delayBetweenBatches));
      }
    }

    return results;
  }

  /**
   * Check if an error is retriable
   */
  private static isRetriableError(error: Error): boolean {
    // Check for rate limit errors
    if (error.message.includes('429') || error.message.includes('Too many requests')) {
      return true;
    }
    
    // Check for network errors
    if (error.message.includes('Network Error') || error.message.includes('timeout')) {
      return true;
    }
    
    // Check for server errors
    if (error.message.includes('500') || error.message.includes('502') || 
        error.message.includes('503') || error.message.includes('504')) {
      return true;
    }
    
    return false;
  }

  /**
   * Create batched API calls for large datasets
   */
  static createBatchedRequests<T, R>(
    items: T[],
    requestFactory: (item: T) => () => Promise<R>,
    batchSize = 10
  ): BatchRequest<R>[][] {
    const batches: BatchRequest<R>[][] = [];
    
    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize).map((item, index) => ({
        id: `${i + index}`,
        request: requestFactory(item),
        priority: 0,
      }));
      
      batches.push(batch);
    }
    
    return batches;
  }

  /**
   * Execute requests with circuit breaker pattern
   */
  static async executeWithCircuitBreaker<T>(
    request: () => Promise<T>,
    options: {
      failureThreshold?: number;
      resetTimeout?: number;
      onOpen?: () => void;
    } = {}
  ): Promise<T> {
    const {
      failureThreshold = 5,
      resetTimeout = 60000, // 1 minute
      onOpen,
    } = options;

    // Circuit breaker state
    if (!this.circuitBreakerState) {
      this.circuitBreakerState = new Map();
    }

    const key = request.toString();
    let state = this.circuitBreakerState.get(key) || {
      failures: 0,
      lastFailure: 0,
      isOpen: false,
    };

    // Check if circuit is open
    if (state.isOpen) {
      const timeSinceLastFailure = Date.now() - state.lastFailure;
      if (timeSinceLastFailure < resetTimeout) {
        throw new Error('Circuit breaker is open - request blocked');
      } else {
        // Reset circuit
        state.isOpen = false;
        state.failures = 0;
      }
    }

    try {
      const result = await request();
      
      // Reset failure count on success
      state.failures = 0;
      this.circuitBreakerState.set(key, state);
      
      return result;
    } catch (error) {
      state.failures++;
      state.lastFailure = Date.now();
      
      if (state.failures >= failureThreshold) {
        state.isOpen = true;
        if (onOpen) {
          onOpen();
        }
        
        errorService.logError('Circuit breaker opened', {
          failures: state.failures,
          threshold: failureThreshold,
        });
      }
      
      this.circuitBreakerState.set(key, state);
      throw error;
    }
  }

  private static circuitBreakerState: Map<string, {
    failures: number;
    lastFailure: number;
    isOpen: boolean;
  }>;
}

// Export convenience functions
export const executeBatch = BatchRequestHandler.executeBatch.bind(BatchRequestHandler);
export const createBatchedRequests = BatchRequestHandler.createBatchedRequests.bind(BatchRequestHandler);
export const executeWithCircuitBreaker = BatchRequestHandler.executeWithCircuitBreaker.bind(BatchRequestHandler);