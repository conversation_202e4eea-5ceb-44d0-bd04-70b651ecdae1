import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Alert } from '../ui/alert';
import inspectionService, { InspectionType } from '../../services/inspectionService';

export const InspectionForm: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [inspectionTypes, setInspectionTypes] = useState<InspectionType[]>([]);
  const [selectedType, setSelectedType] = useState<InspectionType | null>(null);
  
  const [formData, setFormData] = useState({
    inspectionType: '',
    inspectionSubtype: '',
    scheduledDate: '',
    permitDocumentId: ''
  });

  useEffect(() => {
    loadInspectionTypes();
  }, []);

  const loadInspectionTypes = async () => {
    try {
      const types = await inspectionService.getInspectionTypes();
      setInspectionTypes(types);
    } catch (err) {
      setError('Failed to load inspection types');
      console.error(err);
    }
  };

  const handleTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const typeKey = e.target.value;
    setFormData({ ...formData, inspectionType: typeKey });
    
    const type = inspectionTypes.find(t => t.key === typeKey);
    setSelectedType(type || null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!projectId) return;

    setLoading(true);
    setError('');

    try {
      const inspection = await inspectionService.createInspection({
        projectId,
        inspectionType: formData.inspectionType,
        inspectionSubtype: formData.inspectionSubtype || undefined,
        scheduledDate: formData.scheduledDate ? new Date(formData.scheduledDate) : undefined,
        permitDocumentId: formData.permitDocumentId || undefined
      });

      navigate(`/projects/${projectId}/inspections/${inspection.id}`);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to create inspection');
    } finally {
      setLoading(false);
    }
  };

  const getInspectionSubtypes = (type: string): string[] => {
    switch (type) {
      case 'POOL_SPA':
        return ['POOL_BONDING', 'SPA_EQUIPMENT', 'POOL_LIGHTING'];
      case 'SOLAR':
        return ['ROOFTOP_SOLAR', 'GROUND_MOUNT', 'BATTERY_STORAGE'];
      case 'GENERATOR':
        return ['PORTABLE_GENERATOR', 'STANDBY_GENERATOR', 'TRANSFER_SWITCH'];
      case 'EMERGENCY_SYSTEM':
        return ['EXIT_LIGHTING', 'EMERGENCY_POWER', 'FIRE_ALARM'];
      default:
        return [];
    }
  };

  return (
    <div className="max-w-2xl mx-auto">
      <Card className="p-6">
        <h2 className="text-2xl font-bold mb-6">Schedule New Inspection</h2>

        {error && (
          <Alert variant="destructive" className="mb-4">
            {error}
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <Label htmlFor="inspectionType">Inspection Type</Label>
            <select
              id="inspectionType"
              value={formData.inspectionType}
              onChange={handleTypeChange}
              className="mt-1 input"
              required
            >
              <option value="">Select inspection type</option>
              {inspectionTypes.map((type) => (
                <option key={type.key} value={type.key}>
                  {type.name}
                </option>
              ))}
            </select>
          </div>

          {selectedType && (
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-semibold mb-2">Inspection will include:</h4>
              <div className="grid grid-cols-2 gap-2 text-sm">
                {selectedType.categories.map((category) => (
                  <div key={category.name}>
                    • {category.name.replace(/_/g, ' ')} ({category.itemCount} items)
                  </div>
                ))}
              </div>
            </div>
          )}

          {formData.inspectionType && getInspectionSubtypes(formData.inspectionType).length > 0 && (
            <div>
              <Label htmlFor="inspectionSubtype">Inspection Subtype (Optional)</Label>
              <select
                id="inspectionSubtype"
                value={formData.inspectionSubtype}
                onChange={(e) => setFormData({ ...formData, inspectionSubtype: e.target.value })}
                className="mt-1 input"
              >
                <option value="">Select subtype (optional)</option>
                {getInspectionSubtypes(formData.inspectionType).map((subtype) => (
                  <option key={subtype} value={subtype}>
                    {subtype.replace(/_/g, ' ')}
                  </option>
                ))}
              </select>
            </div>
          )}

          <div>
            <Label htmlFor="scheduledDate">Scheduled Date (Optional)</Label>
            <Input
              id="scheduledDate"
              type="datetime-local"
              value={formData.scheduledDate}
              onChange={(e) => setFormData({ ...formData, scheduledDate: e.target.value })}
            />
            <p className="text-sm text-gray-500 mt-1">
              Leave blank if inspection date is not yet scheduled
            </p>
          </div>

          <div>
            <Label htmlFor="permitDocumentId">Associated Permit Document (Optional)</Label>
            <Input
              id="permitDocumentId"
              type="text"
              value={formData.permitDocumentId}
              onChange={(e) => setFormData({ ...formData, permitDocumentId: e.target.value })}
              placeholder="Enter permit document ID if applicable"
            />
          </div>

          <div className="flex gap-4">
            <Button
              type="submit"
              disabled={loading || !formData.inspectionType}
            >
              {loading ? 'Creating...' : 'Create Inspection Checklist'}
            </Button>
            <Button
              type="button"
              className="btn-secondary"
              onClick={() => navigate(`/projects/${projectId}/inspections`)}
            >
              Cancel
            </Button>
          </div>
        </form>
      </Card>
    </div>
  );
};