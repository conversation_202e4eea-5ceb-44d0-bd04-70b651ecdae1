<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Connection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        button {
            margin: 5px;
            padding: 10px 20px;
            font-size: 16px;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>API Connection Test</h1>
    
    <h2>Test 1: Direct Backend Connection</h2>
    <button onclick="testDirectConnection()">Test Direct Connection (http://localhost:3001/api)</button>
    <div id="direct-result"></div>
    
    <h2>Test 2: Proxied Connection</h2>
    <button onclick="testProxiedConnection()">Test Proxied Connection (/api)</button>
    <div id="proxy-result"></div>
    
    <h2>Test 3: Login Test</h2>
    <button onclick="testLogin()">Test Login (<EMAIL>)</button>
    <div id="login-result"></div>

    <script>
        async function testDirectConnection() {
            const resultDiv = document.getElementById('direct-result');
            resultDiv.innerHTML = '<div class="test-result">Testing...</div>';
            
            try {
                const response = await fetch('http://localhost:3001/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'test'
                    })
                });
                
                const data = await response.text();
                
                if (response.status === 401) {
                    resultDiv.innerHTML = `
                        <div class="test-result success">
                            ✅ Direct connection successful! 
                            <br>Status: ${response.status} (Expected 401 for invalid credentials)
                            <br>Response: <pre>${data}</pre>
                        </div>`;
                } else {
                    resultDiv.innerHTML = `
                        <div class="test-result success">
                            ✅ Connection successful! 
                            <br>Status: ${response.status}
                            <br>Response: <pre>${data}</pre>
                        </div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="test-result error">
                        ❌ Connection failed!
                        <br>Error: ${error.message}
                        <br>This might be due to CORS. Check browser console for details.
                    </div>`;
                console.error('Direct connection error:', error);
            }
        }

        async function testProxiedConnection() {
            const resultDiv = document.getElementById('proxy-result');
            resultDiv.innerHTML = '<div class="test-result">Testing...</div>';
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'test'
                    })
                });
                
                const data = await response.text();
                
                if (response.status === 401) {
                    resultDiv.innerHTML = `
                        <div class="test-result success">
                            ✅ Proxied connection successful! 
                            <br>Status: ${response.status} (Expected 401 for invalid credentials)
                            <br>Response: <pre>${data}</pre>
                        </div>`;
                } else {
                    resultDiv.innerHTML = `
                        <div class="test-result success">
                            ✅ Connection successful! 
                            <br>Status: ${response.status}
                            <br>Response: <pre>${data}</pre>
                        </div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="test-result error">
                        ❌ Connection failed!
                        <br>Error: ${error.message}
                    </div>`;
                console.error('Proxied connection error:', error);
            }
        }

        async function testLogin() {
            const resultDiv = document.getElementById('login-result');
            resultDiv.innerHTML = '<div class="test-result">Testing...</div>';
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'password123'
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="test-result success">
                            ✅ Login successful!
                            <br>User: ${data.user.name} (${data.user.email})
                            <br>Role: ${data.user.role}
                            <br>Token: ${data.accessToken.substring(0, 50)}...
                        </div>`;
                    
                    // Store the token for further tests
                    localStorage.setItem('test-token', data.accessToken);
                } else {
                    resultDiv.innerHTML = `
                        <div class="test-result error">
                            ❌ Login failed!
                            <br>Status: ${response.status}
                            <br>Response: <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="test-result error">
                        ❌ Connection failed!
                        <br>Error: ${error.message}
                    </div>`;
                console.error('Login error:', error);
            }
        }
        
        // Test on page load
        window.onload = () => {
            console.log('API Connection Test Page Loaded');
            console.log('Current URL:', window.location.href);
        };
    </script>
</body>
</html>