// Simple EventEmitter implementation for React Native
// Replaces Node.js 'events' module which is not available in React Native

type EventListener = (...args: any[]) => void;

export class EventEmitter {
  private events: Map<string, EventListener[]> = new Map();

  on(event: string, listener: EventListener): this {
    if (!this.events.has(event)) {
      this.events.set(event, []);
    }
    this.events.get(event)!.push(listener);
    return this;
  }

  // Alias for compatibility
  addEventListener(event: string, listener: EventListener): this {
    return this.on(event, listener);
  }

  // Alias for compatibility
  addListener(event: string, listener: EventListener): this {
    return this.on(event, listener);
  }

  emit(event: string, ...args: any[]): boolean {
    if (!this.events.has(event)) {
      return false;
    }
    
    const listeners = this.events.get(event)!;
    listeners.forEach(listener => {
      try {
        listener(...args);
      } catch (error) {
        console.error(`Error in event listener for ${event}:`, error);
      }
    });
    
    return true;
  }

  off(event: string, listener: EventListener): this {
    if (!this.events.has(event)) {
      return this;
    }
    
    const listeners = this.events.get(event)!;
    const index = listeners.indexOf(listener);
    if (index !== -1) {
      listeners.splice(index, 1);
    }
    
    if (listeners.length === 0) {
      this.events.delete(event);
    }
    
    return this;
  }

  // Alias for compatibility
  removeEventListener(event: string, listener: EventListener): this {
    return this.off(event, listener);
  }

  // Alias for compatibility
  removeListener(event: string, listener: EventListener): this {
    return this.off(event, listener);
  }

  once(event: string, listener: EventListener): this {
    const onceListener = (...args: any[]) => {
      this.off(event, onceListener);
      listener(...args);
    };
    return this.on(event, onceListener);
  }

  removeAllListeners(event?: string): this {
    if (event) {
      this.events.delete(event);
    } else {
      this.events.clear();
    }
    return this;
  }

  listenerCount(event: string): number {
    return this.events.get(event)?.length || 0;
  }
}