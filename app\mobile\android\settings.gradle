rootProject.name = 'ElectricalContractor'

// Disable NetBeans Gradle tooling to prevent ConcurrentModificationException
System.setProperty('netbeans.gradle.project.disabled', 'true')

// Use standard npm paths - React Native modules are in node_modules
apply from: file("../node_modules/@react-native-community/cli-platform-android/native_modules.gradle")
applyNativeModulesSettingsGradle(settings)

include ':app'

// Include React Native gradle plugin
includeBuild('../node_modules/@react-native/gradle-plugin')