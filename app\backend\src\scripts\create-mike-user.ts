import { PrismaClient } from '@prisma/client';
import * as bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  try {
    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });
    
    if (existingUser) {
      console.log('User already exists, updating password...');
      const hashedPassword = await bcrypt.hash('itsMike818!', 10);
      await prisma.user.update({
        where: { email: '<EMAIL>' },
        data: { password_hash: hashedPassword }
      });
      console.log('Password updated');
    } else {
      console.log('Creating user...');
      const hashedPassword = await bcrypt.hash('itsMike818!', 10);
      await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password_hash: hashedPassword,
          name: '<PERSON>',
          role: 'admin',
          company_id: 'default-company'
        }
      });
      console.log('User created');
    }
    
    console.log('\nLogin credentials:');
    console.log('Email: <EMAIL>');
    console.log('Password: itsMike818!');
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();