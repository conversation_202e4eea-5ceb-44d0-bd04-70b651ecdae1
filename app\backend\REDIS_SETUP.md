# Redis Configuration and Management

## Overview

The application uses Redis for:
- Caching electrical calculations and material prices
- Rate limiting API requests
- Session management
- Job queue management (BullMQ)
- Real-time features via Socket.io adapter

## Key Features

### 1. Graceful Fallback
The application can run without Redis. When Redis is unavailable:
- **In Development**: Application continues normally without caching/rate limiting
- **In Production**: Application logs warnings but continues operation
- **Rate Limiting**: Falls back to in-memory rate limiting
- **Caching**: Returns null for cache misses, skips cache writes

### 2. Automatic Recovery
The RedisManager automatically:
- Detects disconnections
- Attempts reconnection (configurable attempts)
- Switches between Redis and memory-based implementations
- Emits events for connection state changes

### 3. Enhanced Rate Limiting
- **Development Mode**: 10x more lenient limits when Redis is unavailable
- **Memory Fallback**: Automatic switch to in-memory rate limiting
- **Tier-based**: Different limits for basic/pro/enterprise users

## Environment Variables

```env
# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=          # Optional, leave empty for no auth

# Rate Limiting (optional, has defaults)
RATE_LIMIT_WINDOW_MS=900000    # 15 minutes
RATE_LIMIT_MAX_REQUESTS=1000   # Higher for development
```

## Testing Redis

Run the test script to verify Redis functionality:

```bash
cd app/backend
npx tsx src/scripts/test-redis.ts
```

## Common Issues and Solutions

### Issue: 429 Too Many Requests Errors

**Symptoms**: API returns 429 errors frequently

**Solutions**:
1. Check if Redis is running: `redis-cli ping`
2. Increase rate limits in `.env` for development
3. The application now handles this gracefully with fallback

### Issue: Redis Connection Failed

**Symptoms**: Warning logs about Redis connection

**Solutions**:
1. Start Redis: `redis-server` or `docker run -p 6379:6379 redis`
2. Check connection settings in `.env`
3. Application will continue without Redis (with reduced features)

### Issue: Cache Not Working

**Symptoms**: Repeated calculations, slow performance

**Solutions**:
1. Check Redis connection in health endpoint: `GET /health`
2. Monitor Redis health: `GET /health/metrics`
3. Run test script to diagnose issues

## Monitoring

### Health Endpoints

```bash
# Basic health check
GET /health

# Detailed metrics including Redis status
GET /health/metrics
```

### Redis Manager Events

The RedisManager emits these events:
- `connected`: Redis connection established
- `disconnected`: Redis connection lost
- `reconnecting`: Attempting to reconnect
- `ready`: Redis ready for operations
- `error`: Redis error occurred
- `maxReconnectAttemptsReached`: Given up reconnecting

## Architecture

### RedisManager (`redis-manager.ts`)
- Singleton pattern for consistent connection management
- Event-driven architecture for state changes
- Safe operation wrappers with automatic fallback
- Configurable reconnection logic

### CacheService (`cache.service.ts`)
- Uses RedisManager for all operations
- Graceful handling of Redis unavailability
- Type-safe cache operations
- Automatic cache key generation

### Rate Limiting (`rate-limiting.ts`)
- Dual implementation: Redis and Memory
- Automatic switching based on availability
- Development-friendly configuration
- Progressive penalties for violations

## Best Practices

1. **Always use RedisManager**: Don't create direct Redis connections
2. **Handle null returns**: Cache operations may return null
3. **Monitor health**: Check `/health/metrics` in production
4. **Test locally**: Run without Redis to ensure fallback works
5. **Set appropriate limits**: Configure rate limits for your use case

## Docker Setup (Optional)

For consistent Redis setup across environments:

```yaml
# docker-compose.yml
services:
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    command: redis-server --appendonly yes

volumes:
  redis-data:
```

## Production Considerations

1. **Use Redis persistence**: Configure AOF or RDB
2. **Set memory limits**: Prevent OOM issues
3. **Monitor metrics**: Track hit/miss ratios
4. **Configure eviction**: Use appropriate eviction policy
5. **Secure connection**: Use password in production