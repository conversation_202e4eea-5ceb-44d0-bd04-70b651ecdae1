import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { api } from '../../services/api';
import { 
  ArrowLeftIcon, 
  SparklesIcon,
  PhotoIcon,
  PlusIcon,
  TrashIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

interface ClarificationQuestion {
  question: string;
  answer: string;
}

interface GeneratedQuote {
  id: string;
  quote_number: string;
  status: string;
  questions?: string[];
  suggestions?: string[];
}

export const QuoteAIAssistantPage: React.FC = () => {
  const navigate = useNavigate();
  const [inputType, setInputType] = useState<'text' | 'image' | 'mixed'>('text');
  const [textInput, setTextInput] = useState('');
  const [imageFiles, setImageFiles] = useState<File[]>([]);
  const [loading, setLoading] = useState(false);
  const [generatedQuote, setGeneratedQuote] = useState<GeneratedQuote | null>(null);
  const [clarificationQuestions, setClarificationQuestions] = useState<ClarificationQuestion[]>([]);
  const [selectedCustomerId, setSelectedCustomerId] = useState('');
  const [selectedProjectId, setSelectedProjectId] = useState('');
  const [customers, setCustomers] = useState<any[]>([]);
  const [projects, setProjects] = useState<any[]>([]);

  React.useEffect(() => {
    loadCustomers();
  }, []);

  React.useEffect(() => {
    if (selectedCustomerId) {
      loadProjects(selectedCustomerId);
    } else {
      setProjects([]);
      setSelectedProjectId('');
    }
  }, [selectedCustomerId]);

  const loadCustomers = async () => {
    try {
      const response = await api.get('/customers');
      setCustomers(response.data?.data || []);
    } catch (error) {
      console.error('Failed to load customers:', error);
    }
  };

  const loadProjects = async (customerId: string) => {
    try {
      const response = await api.get(`/projects?customerId=${customerId}`);
      setProjects(response.data?.data || []);
    } catch (error) {
      console.error('Failed to load projects:', error);
    }
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setImageFiles(prev => [...prev, ...files]);
  };

  const removeImage = (index: number) => {
    setImageFiles(prev => prev.filter((_, i) => i !== index));
  };

  const generateQuote = async () => {
    if (!textInput.trim() && imageFiles.length === 0) {
      toast.error('Please provide some input or upload images');
      return;
    }

    try {
      setLoading(true);
      
      let inputData = textInput;
      
      // If images are provided, convert them to base64
      if (imageFiles.length > 0) {
        const imagePromises = imageFiles.map(file => {
          return new Promise<string>((resolve, reject) => {
            const reader = new FileReader();
            reader.onloadend = () => resolve(reader.result as string);
            reader.onerror = reject;
            reader.readAsDataURL(file);
          });
        });
        
        const base64Images = await Promise.all(imagePromises);
        inputData = JSON.stringify({
          text: textInput,
          images: base64Images
        });
      }

      const response = await api.post('/quotes/generate-ai', {
        inputType,
        inputData,
        customerId: selectedCustomerId || undefined,
        projectId: selectedProjectId || undefined,
        includeImages: imageFiles.length > 0
      });

      if (response.data.status === 'pending_questions') {
        setGeneratedQuote(response.data);
        setClarificationQuestions(
          response.data.questions.map((q: string) => ({ question: q, answer: '' }))
        );
        toast.success('Please answer the clarification questions');
      } else {
        toast.success('Quote generated successfully!');
        navigate(`/quotes/${response.data.quote.id}`);
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to generate quote');
    } finally {
      setLoading(false);
    }
  };

  const submitAnswers = async () => {
    if (!generatedQuote) return;

    const unanswered = clarificationQuestions.filter(q => !q.answer.trim());
    if (unanswered.length > 0) {
      toast.error('Please answer all questions');
      return;
    }

    try {
      setLoading(true);
      
      const answers: Record<string, string> = {};
      clarificationQuestions.forEach((q, index) => {
        answers[index.toString()] = q.answer;
      });

      const response = await api.post(`/quotes/${generatedQuote.id}/answer-questions`, {
        answers
      });

      toast.success('Quote completed successfully!');
      navigate(`/quotes/${response.data.quote.id}`);
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to submit answers');
    } finally {
      setLoading(false);
    }
  };

  const updateAnswer = (index: number, answer: string) => {
    setClarificationQuestions(prev => 
      prev.map((q, i) => i === index ? { ...q, answer } : q)
    );
  };

  const examplePrompts = [
    "Install 200A electrical panel with 20 circuits for a 2000 sq ft residential home",
    "Wire a new kitchen with 4 dedicated circuits, GFCI outlets, and under-cabinet lighting",
    "Complete electrical installation for a 5000 sq ft commercial office space",
    "Install EV charger with 50A circuit and outdoor NEMA 14-50 outlet",
    "Upgrade service from 100A to 200A with new meter and panel"
  ];

  return (
    <div className="px-4 sm:px-6 lg:px-8 py-8 max-w-4xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <button
          onClick={() => navigate('/quotes')}
          className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
        >
          <ArrowLeftIcon className="h-4 w-4 mr-1" />
          Back to Quotes
        </button>
        
        <div className="mt-2">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
            <SparklesIcon className="h-8 w-8 mr-2 text-purple-600" />
            AI Quote Assistant
          </h1>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Generate professional quotes using natural language or images
          </p>
        </div>
      </div>

      {!generatedQuote ? (
        <>
          {/* Customer & Project Selection */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Customer & Project (Optional)
            </h2>
            
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Customer
                </label>
                <select
                  value={selectedCustomerId}
                  onChange={(e) => setSelectedCustomerId(e.target.value)}
                  className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:ring-purple-500 focus:border-purple-500"
                >
                  <option value="">Select a customer (optional)</option>
                  {customers.map((customer) => (
                    <option key={customer.id} value={customer.id}>
                      {customer.name}
                    </option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Project
                </label>
                <select
                  value={selectedProjectId}
                  onChange={(e) => setSelectedProjectId(e.target.value)}
                  disabled={!selectedCustomerId}
                  className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:ring-purple-500 focus:border-purple-500 disabled:opacity-50"
                >
                  <option value="">Select a project (optional)</option>
                  {projects.map((project) => (
                    <option key={project.id} value={project.id}>
                      {project.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Input Type Selection */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              How would you like to describe the work?
            </h2>
            
            <div className="grid grid-cols-3 gap-4">
              <button
                onClick={() => setInputType('text')}
                className={`p-4 rounded-lg border-2 transition-colors ${
                  inputType === 'text'
                    ? 'border-purple-600 bg-purple-50 dark:bg-purple-900/20'
                    : 'border-gray-300 dark:border-gray-600 hover:border-gray-400'
                }`}
              >
                <div className="text-center">
                  <div className="text-2xl mb-2">💬</div>
                  <div className="font-medium text-gray-900 dark:text-white">Text Description</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Describe in words</div>
                </div>
              </button>
              
              <button
                onClick={() => setInputType('image')}
                className={`p-4 rounded-lg border-2 transition-colors ${
                  inputType === 'image'
                    ? 'border-purple-600 bg-purple-50 dark:bg-purple-900/20'
                    : 'border-gray-300 dark:border-gray-600 hover:border-gray-400'
                }`}
              >
                <div className="text-center">
                  <div className="text-2xl mb-2">📷</div>
                  <div className="font-medium text-gray-900 dark:text-white">Images Only</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Upload photos</div>
                </div>
              </button>
              
              <button
                onClick={() => setInputType('mixed')}
                className={`p-4 rounded-lg border-2 transition-colors ${
                  inputType === 'mixed'
                    ? 'border-purple-600 bg-purple-50 dark:bg-purple-900/20'
                    : 'border-gray-300 dark:border-gray-600 hover:border-gray-400'
                }`}
              >
                <div className="text-center">
                  <div className="text-2xl mb-2">🎨</div>
                  <div className="font-medium text-gray-900 dark:text-white">Text + Images</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Both combined</div>
                </div>
              </button>
            </div>
          </div>

          {/* Text Input */}
          {(inputType === 'text' || inputType === 'mixed') && (
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6">
              <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Describe the electrical work
              </h2>
              
              <textarea
                value={textInput}
                onChange={(e) => setTextInput(e.target.value)}
                rows={6}
                placeholder="Example: Install a 200A electrical panel with 20 circuits for a residential home. Include all necessary breakers, wiring, and labor..."
                className="w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:ring-purple-500 focus:border-purple-500"
              />
              
              <div className="mt-4">
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">Example prompts:</p>
                <div className="flex flex-wrap gap-2">
                  {examplePrompts.map((prompt, index) => (
                    <button
                      key={index}
                      onClick={() => setTextInput(prompt)}
                      className="text-xs px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600"
                    >
                      {prompt}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Image Upload */}
          {(inputType === 'image' || inputType === 'mixed') && (
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6">
              <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Upload images
              </h2>
              
              <div className="mb-4">
                <label className="block">
                  <span className="sr-only">Choose photos</span>
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="block w-full text-sm text-gray-500 dark:text-gray-400
                      file:mr-4 file:py-2 file:px-4
                      file:rounded-full file:border-0
                      file:text-sm file:font-semibold
                      file:bg-purple-50 file:text-purple-700
                      hover:file:bg-purple-100
                      dark:file:bg-purple-900 dark:file:text-purple-300
                      dark:hover:file:bg-purple-800"
                  />
                </label>
              </div>
              
              {imageFiles.length > 0 && (
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
                  {imageFiles.map((file, index) => (
                    <div key={index} className="relative group">
                      <img
                        src={URL.createObjectURL(file)}
                        alt={`Upload ${index + 1}`}
                        className="w-full h-32 object-cover rounded-lg"
                      />
                      <button
                        onClick={() => removeImage(index)}
                        className="absolute top-2 right-2 p-1 bg-red-600 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
              
              <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                Upload photos of electrical plans, existing installations, or areas where work is needed
              </p>
            </div>
          )}

          {/* Generate Button */}
          <div className="flex justify-end">
            <button
              onClick={generateQuote}
              disabled={loading || (!textInput.trim() && imageFiles.length === 0)}
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  Generating...
                </>
              ) : (
                <>
                  <SparklesIcon className="h-5 w-5 mr-2" />
                  Generate Quote
                </>
              )}
            </button>
          </div>
        </>
      ) : (
        <>
          {/* Clarification Questions */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Additional Information Needed
            </h2>
            
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
              Please answer these questions to help us create a more accurate quote:
            </p>
            
            <div className="space-y-6">
              {clarificationQuestions.map((q, index) => (
                <div key={index}>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {q.question}
                  </label>
                  <textarea
                    value={q.answer}
                    onChange={(e) => updateAnswer(index, e.target.value)}
                    rows={3}
                    className="w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:ring-purple-500 focus:border-purple-500"
                  />
                </div>
              ))}
            </div>
            
            {generatedQuote.suggestions && generatedQuote.suggestions.length > 0 && (
              <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <h3 className="text-sm font-medium text-blue-900 dark:text-blue-300 mb-2">
                  AI Suggestions:
                </h3>
                <ul className="list-disc list-inside space-y-1">
                  {generatedQuote.suggestions.map((suggestion, index) => (
                    <li key={index} className="text-sm text-blue-800 dark:text-blue-400">
                      {suggestion}
                    </li>
                  ))}
                </ul>
              </div>
            )}
            
            <div className="mt-6 flex justify-end space-x-4">
              <button
                onClick={() => {
                  setGeneratedQuote(null);
                  setClarificationQuestions([]);
                }}
                className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm hover:bg-gray-50 dark:hover:bg-gray-600"
              >
                Start Over
              </button>
              <button
                onClick={submitAnswers}
                disabled={loading || clarificationQuestions.some(q => !q.answer.trim())}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Processing...
                  </>
                ) : (
                  <>
                    <CheckCircleIcon className="h-4 w-4 mr-2" />
                    Complete Quote
                  </>
                )}
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  );
};