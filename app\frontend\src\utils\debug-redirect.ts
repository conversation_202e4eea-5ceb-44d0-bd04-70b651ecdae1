// Debug utility to track redirects
export class RedirectDebugger {
  private static logs: any[] = [];
  
  static log(event: string, data: any) {
    const entry = {
      timestamp: new Date().toISOString(),
      event,
      data,
      stack: new Error().stack
    };
    
    this.logs.push(entry);
    console.log(`[REDIRECT DEBUG] ${event}:`, data);
    
    // Store in sessionStorage for persistence
    try {
      sessionStorage.setItem('redirect-debug', JSON.stringify(this.logs));
    } catch (e) {
      // Ignore storage errors
    }
  }
  
  static getLogs() {
    return this.logs;
  }
  
  static clear() {
    this.logs = [];
    sessionStorage.removeItem('redirect-debug');
  }
  
  static restore() {
    try {
      const stored = sessionStorage.getItem('redirect-debug');
      if (stored) {
        this.logs = JSON.parse(stored);
      }
    } catch (e) {
      // Ignore parse errors
    }
  }
}

// Restore logs on load
RedirectDebugger.restore();

// Export to window for console access
if (typeof window !== 'undefined') {
  (window as any).RedirectDebugger = RedirectDebugger;
}