import { Decimal } from 'decimal.js';
import { 
  GENERAL_LIGHTING_LOADS, 
  DWELLING_DEMAND_FACTORS,
  STANDARD_SERVICE_SIZES 
} from '@electrical/shared';

interface LoadCalculationInput {
  calculation_type: 'STANDARD' | 'OPTIONAL' | 'EXISTING';
  building_type: keyof typeof GENERAL_LIGHTING_LOADS;
  square_footage: number;
  small_appliance_circuits: number;
  laundry_circuit: boolean;
  appliances?: Array<{
    name: string;
    va: number;
    quantity: number;
  }>;
  heating_va: number;
  cooling_va: number;
  largest_motor_va: number;
  other_loads_va: number;
}

interface LoadCalculationResult {
  general_lighting_va: number;
  small_appliance_va: number;
  laundry_va: number;
  appliance_loads: Array<{
    name: string;
    va: number;
    demand_factor: number;
    demand_va: number;
  }>;
  heating_cooling_va: number;
  largest_motor_va: number;
  motor_additional_va: number;
  other_loads_va: number;
  total_computed_va: number;
  demand_factor_applied: boolean;
  demand_factor: number;
  total_demand_va: number;
  required_amperage_240v: number;
  required_amperage_208v: number;
  recommended_service_240v: number;
  recommended_service_208v: number;
  necReferences: string[];
  calculation_steps: string[];
}

export class LoadCalculationService {
  async calculate(input: LoadCalculationInput): Promise<LoadCalculationResult> {
    const steps: string[] = [];
    const necRefs: string[] = [];
    
    // Step 1: General Lighting Load (NEC 220.12)
    const lightingVaPerSqFt = GENERAL_LIGHTING_LOADS[input.building_type];
    const generalLightingVa = new Decimal(input.square_footage).times(lightingVaPerSqFt);
    steps.push(`General lighting: ${input.square_footage} sq ft × ${lightingVaPerSqFt} VA/sq ft = ${generalLightingVa.toNumber()} VA`);
    necRefs.push('220.12');
    
    // Step 2: Small Appliance Circuits (NEC 220.52)
    const smallApplianceVa = new Decimal(input.small_appliance_circuits).times(1500);
    steps.push(`Small appliance circuits: ${input.small_appliance_circuits} × 1500 VA = ${smallApplianceVa.toNumber()} VA`);
    necRefs.push('220.52');
    
    // Step 3: Laundry Circuit (NEC 220.52)
    const laundryVa = input.laundry_circuit ? 1500 : 0;
    if (input.laundry_circuit) {
      steps.push(`Laundry circuit: 1500 VA`);
      necRefs.push('220.52(B)');
    }
    
    // Step 4: Apply demand factors to general loads (NEC 220.42)
    let generalLoadsVa = generalLightingVa.plus(smallApplianceVa).plus(laundryVa);
    let demandFactor = new Decimal(1);
    let demandFactorApplied = false;
    
    if (input.building_type === 'DWELLING' && input.calculation_type === 'STANDARD') {
      // Apply dwelling unit demand factors
      let remainingVa = generalLoadsVa;
      let demandVa = new Decimal(0);
      
      for (const factor of DWELLING_DEMAND_FACTORS) {
        if (remainingVa.greaterThan(0)) {
          const applicable = Decimal.min(
            remainingVa,
            new Decimal(factor.max).minus(factor.min)
          );
          demandVa = demandVa.plus(applicable.times(factor.factor));
          remainingVa = remainingVa.minus(applicable);
          
          if (applicable.greaterThan(0)) {
            steps.push(`Demand factor: ${applicable.toNumber()} VA × ${factor.factor} = ${applicable.times(factor.factor).toNumber()} VA`);
          }
        }
      }
      
      generalLoadsVa = demandVa;
      demandFactorApplied = true;
      demandFactor = demandVa.dividedBy(generalLightingVa.plus(smallApplianceVa).plus(laundryVa));
      necRefs.push('220.42', 'Table 220.42');
    }
    
    // Step 5: Fixed Appliances (NEC 220.53)
    const applianceLoads: LoadCalculationResult['appliance_loads'] = [];
    let fixedApplianceVa = new Decimal(0);
    
    if (input.appliances && input.appliances.length > 0) {
      const totalApplianceVa = input.appliances.reduce((sum, app) => 
        sum.plus(new Decimal(app.va).times(app.quantity)), new Decimal(0)
      );
      
      // Apply 75% demand factor if 4 or more appliances
      const applianceDemandFactor = input.appliances.length >= 4 ? 0.75 : 1;
      
      for (const appliance of input.appliances) {
        const va = new Decimal(appliance.va).times(appliance.quantity);
        const demandVa = va.times(applianceDemandFactor);
        
        applianceLoads.push({
          name: appliance.name,
          va: va.toNumber(),
          demand_factor: applianceDemandFactor,
          demand_va: demandVa.toNumber()
        });
        
        fixedApplianceVa = fixedApplianceVa.plus(demandVa);
      }
      
      if (applianceDemandFactor < 1) {
        steps.push(`Fixed appliances: ${input.appliances.length} appliances, 75% demand factor applied`);
        necRefs.push('220.53');
      }
    }
    
    // Step 6: Heating and Cooling (NEC 220.60)
    const heatingVa = new Decimal(input.heating_va);
    const coolingVa = new Decimal(input.cooling_va);
    const heatingCoolingVa = Decimal.max(heatingVa, coolingVa);
    
    if (heatingVa.greaterThan(0) || coolingVa.greaterThan(0)) {
      steps.push(`Heating/Cooling: Larger of ${heatingVa.toNumber()} VA (heat) or ${coolingVa.toNumber()} VA (cool) = ${heatingCoolingVa.toNumber()} VA`);
      necRefs.push('220.60');
    }
    
    // Step 7: Motor Loads (NEC 220.50)
    const largestMotorVa = new Decimal(input.largest_motor_va);
    const motorAdditionalVa = largestMotorVa.times(0.25); // 25% of largest motor
    
    if (largestMotorVa.greaterThan(0)) {
      steps.push(`Largest motor: ${largestMotorVa.toNumber()} VA + 25% = ${largestMotorVa.plus(motorAdditionalVa).toNumber()} VA`);
      necRefs.push('220.50', '430.24');
    }
    
    // Step 8: Other Loads
    const otherLoadsVa = new Decimal(input.other_loads_va);
    
    // Step 9: Calculate Total
    const totalComputedVa = generalLoadsVa
      .plus(fixedApplianceVa)
      .plus(heatingCoolingVa)
      .plus(largestMotorVa)
      .plus(motorAdditionalVa)
      .plus(otherLoadsVa);
    
    steps.push(`Total computed load: ${totalComputedVa.toNumber()} VA`);
    
    // Step 10: Calculate required amperage
    const requiredAmperage240v = totalComputedVa.dividedBy(240);
    const requiredAmperage208v = totalComputedVa.dividedBy(208);
    
    steps.push(`Required amperage: ${requiredAmperage240v.toFixed(1)} A @ 240V, ${requiredAmperage208v.toFixed(1)} A @ 208V`);
    
    // Step 11: Determine service size
    const recommendedService240v = this.getNextServiceSize(requiredAmperage240v.toNumber());
    const recommendedService208v = this.getNextServiceSize(requiredAmperage208v.toNumber());
    
    steps.push(`Recommended service: ${recommendedService240v} A @ 240V, ${recommendedService208v} A @ 208V`);
    necRefs.push('230.42', '230.79');
    
    return {
      general_lighting_va: generalLightingVa.toNumber(),
      small_appliance_va: smallApplianceVa.toNumber(),
      laundry_va: laundryVa,
      appliance_loads: applianceLoads,
      heating_cooling_va: heatingCoolingVa.toNumber(),
      largest_motor_va: largestMotorVa.toNumber(),
      motor_additional_va: motorAdditionalVa.toNumber(),
      other_loads_va: otherLoadsVa.toNumber(),
      total_computed_va: totalComputedVa.toNumber(),
      demand_factor_applied: demandFactorApplied,
      demand_factor: demandFactor.toNumber(),
      total_demand_va: totalComputedVa.toNumber(),
      required_amperage_240v: requiredAmperage240v.toNumber(),
      required_amperage_208v: requiredAmperage208v.toNumber(),
      recommended_service_240v: recommendedService240v,
      recommended_service_208v: recommendedService208v,
      necReferences: [...new Set(necRefs)],
      calculation_steps: steps
    };
  }
  
  private getNextServiceSize(amperage: number): number {
    for (const size of STANDARD_SERVICE_SIZES) {
      if (size >= amperage) {
        return size;
      }
    }
    return STANDARD_SERVICE_SIZES[STANDARD_SERVICE_SIZES.length - 1];
  }
}