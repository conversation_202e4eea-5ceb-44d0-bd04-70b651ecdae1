#!/bin/bash

# Test script to verify Claude Code hooks are working
# Run this after restarting Claude Code with --debug flag

echo "================================================"
echo "Claude Code Hooks Verification Test"
echo "================================================"
echo

# Check if Claude Code is running with debug flag
echo "1. Checking Claude Code process..."
if ps aux | grep -q "claude.*--debug"; then
    echo "   ✓ Claude Code is running with --debug flag"
else
    echo "   ✗ Claude Code is NOT running with --debug flag"
    echo "   Please restart with: claude --debug"
    exit 1
fi
echo

# Check hook configuration
echo "2. Verifying hook configuration..."
if [ -f ~/.claude/settings.json ]; then
    echo "   ✓ Hook configuration file exists"
    # Count configured hooks
    hook_count=$(grep -c "command" ~/.claude/settings.json)
    echo "   ✓ Found $hook_count hook commands configured"
else
    echo "   ✗ Hook configuration file not found"
    exit 1
fi
echo

# Check hook scripts
echo "3. Verifying hook scripts..."
hook_dir=~/.claude/hooks
if [ -d "$hook_dir" ]; then
    echo "   ✓ Hook directory exists"
    for script in "$hook_dir"/*.sh; do
        if [ -x "$script" ]; then
            echo "   ✓ $(basename "$script") is executable"
        else
            echo "   ✗ $(basename "$script") is NOT executable"
        fi
    done
else
    echo "   ✗ Hook directory not found"
    exit 1
fi
echo

# Check log files
echo "4. Checking hook log files..."
log_dir=~/.claude/logs
if [ -d "$log_dir" ]; then
    echo "   ✓ Log directory exists"
    today=$(date +%Y-%m-%d)
    if [ -f "$log_dir/activity_$today.log" ]; then
        echo "   ✓ Today's activity log exists"
        recent_entries=$(tail -5 "$log_dir/activity_$today.log" 2>/dev/null | wc -l)
        echo "   ✓ Found $recent_entries recent log entries"
    else
        echo "   ℹ No activity log for today yet"
    fi
else
    echo "   ℹ Log directory not created yet (will be created on first hook execution)"
fi
echo

# Check session progress
echo "5. Checking session progress tracking..."
if [ -f ~/.claude/session_progress.log ]; then
    echo "   ✓ Session progress log exists"
    last_entry=$(tail -1 ~/.claude/session_progress.log)
    echo "   ✓ Last entry: $last_entry"
else
    echo "   ℹ No session progress logged yet"
fi
echo

echo "================================================"
echo "Hook Testing Instructions:"
echo "================================================"
echo "1. Create a test file to trigger Write hook:"
echo "   - Ask Claude to create a file called test.txt"
echo "   - You should see guide_edits.sh output"
echo
echo "2. Edit the file to trigger Edit hook:"
echo "   - Ask Claude to modify test.txt"
echo "   - You should see validation suggestions"
echo
echo "3. Use an MCP tool (if available):"
echo "   - This will trigger mcp_reminder.sh"
echo
echo "4. End the session:"
echo "   - Type 'exit' to trigger cleanup_session.sh"
echo
echo "Watch for hook output messages in the terminal!"
echo "================================================"