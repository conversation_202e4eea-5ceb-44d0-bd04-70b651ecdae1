import { useCallback } from 'react';

interface ToastOptions {
  title: string;
  description?: string;
  variant?: 'success' | 'error' | 'warning' | 'info';
  duration?: number;
}

export function useToast() {
  const showToast = useCallback((options: ToastOptions) => {
    // For now, use console logging
    // In production, integrate with your toast library (e.g., react-toastify, sonner, etc.)
    const { title, description, variant = 'info' } = options;
    
    const message = description ? `${title}: ${description}` : title;
    
    switch (variant) {
      case 'error':
        console.error(`[Toast Error] ${message}`);
        break;
      case 'warning':
        console.warn(`[Toast Warning] ${message}`);
        break;
      default:
        console.log(`[Toast ${variant}] ${message}`);
    }
    
    // TODO: Replace with actual toast implementation
    // Example with react-toastify:
    // toast[variant](message, { autoClose: duration });
  }, []);

  return { showToast };
}