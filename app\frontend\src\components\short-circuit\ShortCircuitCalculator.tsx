import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Alert, AlertDescription, AlertTitle } from '../ui/alert';
import { Badge } from '../ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { AlertCircle, Calculator, CheckCircle2, XCircle, Zap, Shield, FileText } from 'lucide-react';
import { shortCircuitService } from '../../services/shortCircuitService';
import { panelService } from '../../services/panelService';

interface Panel {
  id: string;
  name: string;
  location: string;
  voltage_system: string;
  ampere_rating: number;
  bus_rating: number;
  main_breaker_size: number | null;
}

interface ShortCircuitResult {
  id: string;
  symmetrical_fault_3ph: number;
  symmetrical_fault_lg: number;
  symmetrical_fault_ll: number;
  asymmetrical_fault_3ph: number;
  peak_fault_current: number;
  total_x_r_ratio: number;
  bus_bracing_adequate: boolean;
  main_breaker_adequate: boolean;
  branch_breaker_adequate: boolean;
  calculation_notes: string;
  assumptions: string;
  calculated_by: string;
  calculation_date: string;
}

export const ShortCircuitCalculator: React.FC<{ projectId: string }> = ({ projectId }) => {
  const [panels, setPanels] = useState<Panel[]>([]);
  const [selectedPanel, setSelectedPanel] = useState<Panel | null>(null);
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<ShortCircuitResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  
  // Input states
  const [utilityVoltage, setUtilityVoltage] = useState('480');
  const [utilityFaultCurrent, setUtilityFaultCurrent] = useState('50');
  const [utilityXRRatio, setUtilityXRRatio] = useState('10');
  
  // Transformer data
  const [hasTransformer, setHasTransformer] = useState(false);
  const [transformerKva, setTransformerKva] = useState('1500');
  const [transformerImpedance, setTransformerImpedance] = useState('5.75');
  const [transformerXRRatio, setTransformerXRRatio] = useState('8');
  const [transformerPrimaryV, setTransformerPrimaryV] = useState('13800');
  const [transformerSecondaryV, setTransformerSecondaryV] = useState('480');
  const [transformerType, setTransformerType] = useState('DRY_TYPE');
  
  // Conductor data
  const [conductorLength, setConductorLength] = useState('100');
  const [conductorSize, setConductorSize] = useState('500_kcmil');
  const [conductorMaterial, setConductorMaterial] = useState('COPPER');
  const [conductorType, setConductorType] = useState('THHN');
  const [conductorsPerPhase, setConductorsPerPhase] = useState('1');
  const [conduitType, setConduitType] = useState('STEEL');
  
  // Motor contribution
  const [includeMotorContribution, setIncludeMotorContribution] = useState(false);
  const [motorHpTotal, setMotorHpTotal] = useState('100');
  
  // Load panels using React Query for better performance and caching
  const { data: panelList, isLoading: isPanelsLoading, error: panelsError } = useQuery({
    queryKey: ['panels', 'project', projectId],
    queryFn: () => panelService.getPanelsByProject(projectId),
    enabled: !!projectId,
    staleTime: 3 * 60 * 1000, // Consider data stale after 3 minutes
    gcTime: 5 * 60 * 1000, // Keep in cache for 5 minutes
    retry: 2,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
  
  // Update panels state when data changes
  useEffect(() => {
    if (panelList) {
      setPanels(panelList);
    }
  }, [panelList]);
  
  // Handle panel loading error
  useEffect(() => {
    if (panelsError) {
      setError('Failed to load panels. Please try again.');
    }
  }, [panelsError]);
  
  // Auto-populate voltage when panel is selected
  useEffect(() => {
    if (selectedPanel) {
      const voltage = selectedPanel.voltage_system.split('/')[0].replace(/[^0-9]/g, '');
      setUtilityVoltage(voltage);
    }
  }, [selectedPanel]);
  
  const handleCalculate = async () => {
    if (!selectedPanel) {
      setError('Please select a panel');
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      const input = {
        utilityVoltage: parseFloat(utilityVoltage),
        utilityFaultCurrent: parseFloat(utilityFaultCurrent),
        utilityXRRatio: parseFloat(utilityXRRatio),
        
        // Transformer data (if applicable)
        ...(hasTransformer && {
          transformerKva: parseFloat(transformerKva),
          transformerImpedance: parseFloat(transformerImpedance),
          transformerXRRatio: parseFloat(transformerXRRatio),
          transformerPrimaryV: parseFloat(transformerPrimaryV),
          transformerSecondaryV: parseFloat(transformerSecondaryV),
          transformerType,
        }),
        
        // Conductor data
        conductorLength: parseFloat(conductorLength),
        conductorSize,
        conductorMaterial,
        conductorType,
        conductorsPerPhase: parseInt(conductorsPerPhase),
        conduitType,
        
        // Motor contribution
        includeMotorContribution,
        ...(includeMotorContribution && {
          motorHpTotal: parseFloat(motorHpTotal),
        }),
      };
      
      const calculation = await shortCircuitService.calculate(selectedPanel.id, input);
      setResult(calculation);
    } catch (err: any) {
      setError(err.message || 'Calculation failed');
    } finally {
      setLoading(false);
    }
  };
  
  const getAicBadgeColor = (adequate: boolean) => {
    return adequate ? 'bg-green-500' : 'bg-red-500';
  };
  
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Short Circuit Analysis
          </CardTitle>
          <CardDescription>
            Calculate available fault current using the point-to-point method per IEEE standards
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Panel Selection */}
            <div className="space-y-2">
              <Label htmlFor="panel">Select Panel</Label>
              <Select
                value={selectedPanel?.id || ''}
                onValueChange={(value) => {
                  const panel = panels.find(p => p.id === value);
                  setSelectedPanel(panel || null);
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Choose a panel" />
                </SelectTrigger>
                <SelectContent>
                  {panels.map((panel) => (
                    <SelectItem key={panel.id} value={panel.id}>
                      {panel.name} - {panel.location} ({panel.voltage_system})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            {/* Utility/Source Data */}
            <div className="space-y-2">
              <Label htmlFor="utilityVoltage">Utility Voltage (V)</Label>
              <Input
                id="utilityVoltage"
                type="number"
                value={utilityVoltage}
                onChange={(e) => setUtilityVoltage(e.target.value)}
                placeholder="480"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="utilityFaultCurrent">Available Fault Current (kA)</Label>
              <Input
                id="utilityFaultCurrent"
                type="number"
                value={utilityFaultCurrent}
                onChange={(e) => setUtilityFaultCurrent(e.target.value)}
                placeholder="50"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="utilityXRRatio">Utility X/R Ratio</Label>
              <Input
                id="utilityXRRatio"
                type="number"
                value={utilityXRRatio}
                onChange={(e) => setUtilityXRRatio(e.target.value)}
                placeholder="10"
              />
            </div>
            
            {/* Transformer Section */}
            <div className="md:col-span-2">
              <div className="flex items-center space-x-2 mb-4">
                <input
                  type="checkbox"
                  id="hasTransformer"
                  checked={hasTransformer}
                  onChange={(e) => setHasTransformer(e.target.checked)}
                  className="rounded border-gray-300"
                />
                <Label htmlFor="hasTransformer">Include Transformer</Label>
              </div>
              
              {hasTransformer && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 border rounded-lg">
                  <div className="space-y-2">
                    <Label htmlFor="transformerKva">Transformer kVA</Label>
                    <Input
                      id="transformerKva"
                      type="number"
                      value={transformerKva}
                      onChange={(e) => setTransformerKva(e.target.value)}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="transformerImpedance">Impedance (%)</Label>
                    <Input
                      id="transformerImpedance"
                      type="number"
                      step="0.01"
                      value={transformerImpedance}
                      onChange={(e) => setTransformerImpedance(e.target.value)}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="transformerXRRatio">X/R Ratio</Label>
                    <Input
                      id="transformerXRRatio"
                      type="number"
                      value={transformerXRRatio}
                      onChange={(e) => setTransformerXRRatio(e.target.value)}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="transformerPrimaryV">Primary Voltage (V)</Label>
                    <Input
                      id="transformerPrimaryV"
                      type="number"
                      value={transformerPrimaryV}
                      onChange={(e) => setTransformerPrimaryV(e.target.value)}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="transformerSecondaryV">Secondary Voltage (V)</Label>
                    <Input
                      id="transformerSecondaryV"
                      type="number"
                      value={transformerSecondaryV}
                      onChange={(e) => setTransformerSecondaryV(e.target.value)}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="transformerType">Type</Label>
                    <Select value={transformerType} onValueChange={setTransformerType}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="DRY_TYPE">Dry Type</SelectItem>
                        <SelectItem value="OIL_FILLED">Oil Filled</SelectItem>
                        <SelectItem value="CAST_RESIN">Cast Resin</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              )}
            </div>
            
            {/* Conductor Data */}
            <div className="space-y-2">
              <Label htmlFor="conductorLength">Conductor Length (ft)</Label>
              <Input
                id="conductorLength"
                type="number"
                value={conductorLength}
                onChange={(e) => setConductorLength(e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="conductorSize">Conductor Size</Label>
              <Select value={conductorSize} onValueChange={setConductorSize}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="14_AWG">14 AWG</SelectItem>
                  <SelectItem value="12_AWG">12 AWG</SelectItem>
                  <SelectItem value="10_AWG">10 AWG</SelectItem>
                  <SelectItem value="8_AWG">8 AWG</SelectItem>
                  <SelectItem value="6_AWG">6 AWG</SelectItem>
                  <SelectItem value="4_AWG">4 AWG</SelectItem>
                  <SelectItem value="3_AWG">3 AWG</SelectItem>
                  <SelectItem value="2_AWG">2 AWG</SelectItem>
                  <SelectItem value="1_AWG">1 AWG</SelectItem>
                  <SelectItem value="1/0_AWG">1/0 AWG</SelectItem>
                  <SelectItem value="2/0_AWG">2/0 AWG</SelectItem>
                  <SelectItem value="3/0_AWG">3/0 AWG</SelectItem>
                  <SelectItem value="4/0_AWG">4/0 AWG</SelectItem>
                  <SelectItem value="250_kcmil">250 kcmil</SelectItem>
                  <SelectItem value="300_kcmil">300 kcmil</SelectItem>
                  <SelectItem value="350_kcmil">350 kcmil</SelectItem>
                  <SelectItem value="400_kcmil">400 kcmil</SelectItem>
                  <SelectItem value="500_kcmil">500 kcmil</SelectItem>
                  <SelectItem value="600_kcmil">600 kcmil</SelectItem>
                  <SelectItem value="750_kcmil">750 kcmil</SelectItem>
                  <SelectItem value="1000_kcmil">1000 kcmil</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="conductorMaterial">Conductor Material</Label>
              <Select value={conductorMaterial} onValueChange={setConductorMaterial}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="COPPER">Copper</SelectItem>
                  <SelectItem value="ALUMINUM">Aluminum</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="conduitType">Conduit Type</Label>
              <Select value={conduitType} onValueChange={setConduitType}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="STEEL">Steel (EMT/RMC)</SelectItem>
                  <SelectItem value="ALUMINUM">Aluminum</SelectItem>
                  <SelectItem value="PVC">PVC</SelectItem>
                  <SelectItem value="CABLE_TRAY">Cable Tray</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="conductorsPerPhase">Conductors per Phase</Label>
              <Input
                id="conductorsPerPhase"
                type="number"
                min="1"
                max="10"
                value={conductorsPerPhase}
                onChange={(e) => setConductorsPerPhase(e.target.value)}
              />
            </div>
            
            {/* Motor Contribution */}
            <div className="md:col-span-2">
              <div className="flex items-center space-x-2 mb-4">
                <input
                  type="checkbox"
                  id="includeMotorContribution"
                  checked={includeMotorContribution}
                  onChange={(e) => setIncludeMotorContribution(e.target.checked)}
                  className="rounded border-gray-300"
                />
                <Label htmlFor="includeMotorContribution">Include Motor Contribution</Label>
              </div>
              
              {includeMotorContribution && (
                <div className="space-y-2">
                  <Label htmlFor="motorHpTotal">Total Motor HP</Label>
                  <Input
                    id="motorHpTotal"
                    type="number"
                    value={motorHpTotal}
                    onChange={(e) => setMotorHpTotal(e.target.value)}
                  />
                </div>
              )}
            </div>
          </div>
          
          {error && (
            <Alert className="mt-4" variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          <div className="mt-6 flex justify-end">
            <Button
              onClick={handleCalculate}
              disabled={loading || !selectedPanel}
              className="flex items-center gap-2"
            >
              <Calculator className="h-4 w-4" />
              {loading ? 'Calculating...' : 'Calculate'}
            </Button>
          </div>
        </CardContent>
      </Card>
      
      {result && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Short Circuit Analysis Results
            </CardTitle>
            <CardDescription>
              Calculated on {new Date(result.calculation_date).toLocaleString()}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="summary" className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="summary">Summary</TabsTrigger>
                <TabsTrigger value="faults">Fault Currents</TabsTrigger>
                <TabsTrigger value="equipment">Equipment</TabsTrigger>
                <TabsTrigger value="details">Details</TabsTrigger>
              </TabsList>
              
              <TabsContent value="summary" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg">3-Phase Fault</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-primary">
                        {result.asymmetrical_fault_3ph.toFixed(2)} kA
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Asymmetrical RMS
                      </p>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg">X/R Ratio</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">
                        {result.total_x_r_ratio.toFixed(2)}
                      </div>
                      <p className="text-sm text-muted-foreground">
                        At fault point
                      </p>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg">Peak Current</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-orange-600">
                        {result.peak_fault_current.toFixed(2)} kA
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Instantaneous
                      </p>
                    </CardContent>
                  </Card>
                </div>
                
                <Alert>
                  <Zap className="h-4 w-4" />
                  <AlertTitle>Required AIC Rating</AlertTitle>
                  <AlertDescription>
                    Equipment must have an interrupting rating of at least{' '}
                    <span className="font-bold">{Math.ceil(result.asymmetrical_fault_3ph)} kA</span>
                  </AlertDescription>
                </Alert>
              </TabsContent>
              
              <TabsContent value="faults" className="space-y-4">
                <div className="space-y-4">
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Symmetrical Fault Currents</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="p-4 border rounded-lg">
                        <p className="text-sm text-muted-foreground">Three-Phase</p>
                        <p className="text-xl font-bold">{result.symmetrical_fault_3ph.toFixed(2)} kA</p>
                      </div>
                      <div className="p-4 border rounded-lg">
                        <p className="text-sm text-muted-foreground">Line-to-Ground</p>
                        <p className="text-xl font-bold">{result.symmetrical_fault_lg.toFixed(2)} kA</p>
                      </div>
                      <div className="p-4 border rounded-lg">
                        <p className="text-sm text-muted-foreground">Line-to-Line</p>
                        <p className="text-xl font-bold">{result.symmetrical_fault_ll.toFixed(2)} kA</p>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Asymmetrical Values</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="p-4 border rounded-lg">
                        <p className="text-sm text-muted-foreground">RMS Asymmetrical</p>
                        <p className="text-xl font-bold text-primary">
                          {result.asymmetrical_fault_3ph.toFixed(2)} kA
                        </p>
                      </div>
                      <div className="p-4 border rounded-lg">
                        <p className="text-sm text-muted-foreground">Peak Instantaneous</p>
                        <p className="text-xl font-bold text-orange-600">
                          {result.peak_fault_current.toFixed(2)} kA
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>
              
              <TabsContent value="equipment" className="space-y-4">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Equipment Adequacy Check</h3>
                  
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <p className="font-medium">Bus Bracing</p>
                        <p className="text-sm text-muted-foreground">
                          {selectedPanel?.bus_rating} kA rating
                        </p>
                      </div>
                      <Badge className={getAicBadgeColor(result.bus_bracing_adequate)}>
                        {result.bus_bracing_adequate ? (
                          <>
                            <CheckCircle2 className="h-4 w-4 mr-1" />
                            Adequate
                          </>
                        ) : (
                          <>
                            <XCircle className="h-4 w-4 mr-1" />
                            Inadequate
                          </>
                        )}
                      </Badge>
                    </div>
                    
                    {selectedPanel?.main_breaker_size && (
                      <div className="flex items-center justify-between p-4 border rounded-lg">
                        <div>
                          <p className="font-medium">Main Breaker</p>
                          <p className="text-sm text-muted-foreground">
                            {selectedPanel.main_breaker_size}A breaker
                          </p>
                        </div>
                        <Badge className={getAicBadgeColor(result.main_breaker_adequate)}>
                          {result.main_breaker_adequate ? (
                            <>
                              <CheckCircle2 className="h-4 w-4 mr-1" />
                              Adequate
                            </>
                          ) : (
                            <>
                              <XCircle className="h-4 w-4 mr-1" />
                              Inadequate
                            </>
                          )}
                        </Badge>
                      </div>
                    )}
                    
                    <div className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <p className="font-medium">Branch Breakers</p>
                        <p className="text-sm text-muted-foreground">
                          Standard 10 kA rating
                        </p>
                      </div>
                      <Badge className={getAicBadgeColor(result.branch_breaker_adequate)}>
                        {result.branch_breaker_adequate ? (
                          <>
                            <CheckCircle2 className="h-4 w-4 mr-1" />
                            Adequate
                          </>
                        ) : (
                          <>
                            <XCircle className="h-4 w-4 mr-1" />
                            Series Rating Required
                          </>
                        )}
                      </Badge>
                    </div>
                  </div>
                  
                  {(!result.bus_bracing_adequate || !result.main_breaker_adequate || !result.branch_breaker_adequate) && (
                    <Alert variant="destructive">
                      <AlertCircle className="h-4 w-4" />
                      <AlertTitle>Equipment Upgrade Required</AlertTitle>
                      <AlertDescription>
                        Some equipment does not meet the required fault current rating.
                        Consider using current-limiting devices or series-rated combinations.
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              </TabsContent>
              
              <TabsContent value="details" className="space-y-4">
                <div className="space-y-4">
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Calculation Notes</h3>
                    <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <pre className="text-sm whitespace-pre-wrap font-mono">
                        {result.calculation_notes}
                      </pre>
                    </div>
                  </div>
                  
                  {result.assumptions && (
                    <div>
                      <h3 className="text-lg font-semibold mb-2">Warnings & Assumptions</h3>
                      <Alert>
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription>
                          <pre className="text-sm whitespace-pre-wrap">
                            {result.assumptions}
                          </pre>
                        </AlertDescription>
                      </Alert>
                    </div>
                  )}
                  
                  <div className="flex justify-end gap-2">
                    <Button variant="outline" className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      Generate Report
                    </Button>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}
    </div>
  );
};