const puppeteer = require('puppeteer');
const fs = require('fs').promises;
const path = require('path');

// Create screenshots directory
const screenshotsDir = path.join(__dirname, 'test-screenshots');

async function ensureDir(dir) {
  try {
    await fs.mkdir(dir, { recursive: true });
  } catch (err) {
    console.error('Error creating directory:', err);
  }
}

async function takeScreenshot(page, name) {
  const filename = path.join(screenshotsDir, `${name}.png`);
  await page.screenshot({ path: filename, fullPage: true });
  console.log(`Screenshot saved: ${filename}`);
}

async function testApplication() {
  await ensureDir(screenshotsDir);
  
  const browser = await puppeteer.launch({
    headless: false,
    args: ['--no-sandbox', '--disable-setuid-sandbox'],
    defaultViewport: { width: 1920, height: 1080 }
  });

  const page = await browser.newPage();
  
  // Enable console logging
  page.on('console', msg => console.log('Browser log:', msg.text()));
  page.on('pageerror', err => console.error('Page error:', err));

  try {
    console.log('Testing Electrical Contracting Application...\n');

    // 1. Test Login Page
    console.log('1. Testing Login Page...');
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle0' });
    await takeScreenshot(page, '01-login-page');
    
    // Check for login form
    const loginForm = await page.$('form');
    if (!loginForm) {
      console.error('ERROR: Login form not found!');
      await takeScreenshot(page, '01-login-error');
    }

    // Try to login with test credentials
    await page.type('input[name="email"], input[type="email"]', '<EMAIL>', { delay: 50 });
    await page.type('input[name="password"], input[type="password"]', 'password123', { delay: 50 });
    await takeScreenshot(page, '02-login-filled');
    
    // Click login button
    await page.click('button[type="submit"]');
    
    // Wait for navigation or error
    await page.waitForTimeout(3000);
    await takeScreenshot(page, '03-after-login');

    // Check if we're on dashboard
    const currentUrl = page.url();
    console.log('Current URL after login:', currentUrl);

    // 2. Test Dashboard
    console.log('\n2. Testing Dashboard...');
    if (!currentUrl.includes('/dashboard')) {
      console.log('Not on dashboard, navigating...');
      await page.goto('http://localhost:3000/dashboard', { waitUntil: 'networkidle0' });
    }
    await takeScreenshot(page, '04-dashboard');

    // Test navigation menu
    console.log('\n3. Testing Navigation Menu...');
    const navItems = await page.$$('nav a, aside a, [role="navigation"] a');
    console.log(`Found ${navItems.length} navigation items`);

    // Get all navigation links
    const navLinks = await page.evaluate(() => {
      const links = Array.from(document.querySelectorAll('nav a, aside a, [role="navigation"] a'));
      return links.map(link => ({
        text: link.textContent.trim(),
        href: link.href
      }));
    });

    console.log('Navigation items found:', navLinks);

    // Test each navigation item
    for (let i = 0; i < navLinks.length; i++) {
      const link = navLinks[i];
      if (link.text && !link.href.includes('#')) {
        console.log(`\nTesting: ${link.text}`);
        
        try {
          // Click the nav item
          await page.evaluate((text) => {
            const link = Array.from(document.querySelectorAll('nav a, aside a, [role="navigation"] a'))
              .find(a => a.textContent.trim() === text);
            if (link) link.click();
          }, link.text);
          
          await page.waitForTimeout(2000);
          await takeScreenshot(page, `nav-${link.text.toLowerCase().replace(/\s+/g, '-')}`);
          
          // Test specific functionality based on section
          await testSectionFunctionality(page, link.text);
          
        } catch (err) {
          console.error(`Error testing ${link.text}:`, err.message);
          await takeScreenshot(page, `error-${link.text.toLowerCase().replace(/\s+/g, '-')}`);
        }
      }
    }

    console.log('\nTest completed!');
    
  } catch (error) {
    console.error('Test failed:', error);
    await takeScreenshot(page, 'error-final');
  } finally {
    await browser.close();
  }
}

async function testSectionFunctionality(page, sectionName) {
  const section = sectionName.toLowerCase();
  
  if (section.includes('project')) {
    console.log('Testing Projects functionality...');
    // Test create button
    const createBtn = await page.$('button:has-text("Create"), button:has-text("Add"), button:has-text("New")');
    if (createBtn) {
      await createBtn.click();
      await page.waitForTimeout(1000);
      await takeScreenshot(page, 'projects-create-modal');
      
      // Close modal if open
      const closeBtn = await page.$('button[aria-label="Close"], button:has-text("Cancel")');
      if (closeBtn) await closeBtn.click();
    }
  } 
  else if (section.includes('estimate')) {
    console.log('Testing Estimates functionality...');
    await takeScreenshot(page, 'estimates-page');
    
    // Look for estimate creation or list
    const estimateElements = await page.$$('[class*="estimate"]');
    console.log(`Found ${estimateElements.length} estimate elements`);
  }
  else if (section.includes('calculation')) {
    console.log('Testing Calculations functionality...');
    await takeScreenshot(page, 'calculations-page');
    
    // Test calculator tabs or sections
    const calculatorSections = await page.$$('[role="tab"], .calculator-section');
    console.log(`Found ${calculatorSections.length} calculator sections`);
  }
  else if (section.includes('panel')) {
    console.log('Testing Panels functionality...');
    await takeScreenshot(page, 'panels-page');
    
    // Test panel schedule view
    const panelElements = await page.$$('[class*="panel"], table');
    console.log(`Found ${panelElements.length} panel elements`);
  }
  else if (section.includes('material')) {
    console.log('Testing Materials functionality...');
    await takeScreenshot(page, 'materials-page');
    
    // Test search functionality
    const searchInput = await page.$('input[type="search"], input[placeholder*="Search"]');
    if (searchInput) {
      await searchInput.type('wire');
      await page.waitForTimeout(1000);
      await takeScreenshot(page, 'materials-search-results');
    }
  }
  else if (section.includes('inspection')) {
    console.log('Testing Inspections functionality...');
    await takeScreenshot(page, 'inspections-page');
    
    // Look for inspection forms or checklists
    const inspectionElements = await page.$$('[class*="inspection"], form');
    console.log(`Found ${inspectionElements.length} inspection elements`);
  }
  else if (section.includes('customer')) {
    console.log('Testing Customers functionality...');
    await takeScreenshot(page, 'customers-page');
    
    // Test customer list
    const customerElements = await page.$$('[class*="customer"], tr');
    console.log(`Found ${customerElements.length} customer elements`);
  }
  else if (section.includes('analytic')) {
    console.log('Testing Analytics functionality...');
    await takeScreenshot(page, 'analytics-page');
    
    // Look for charts and dashboards
    const chartElements = await page.$$('canvas, svg, [class*="chart"]');
    console.log(`Found ${chartElements.length} chart elements`);
  }
  
  // Test form inputs and buttons in the current section
  const inputs = await page.$$('input:not([type="hidden"]), select, textarea');
  const buttons = await page.$$('button:not([disabled])');
  
  console.log(`Found ${inputs.length} inputs and ${buttons.length} buttons`);
}

// Run the test
testApplication().catch(console.error);