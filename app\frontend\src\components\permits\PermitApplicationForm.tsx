import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Alert } from '../ui/alert';
import { permitService, JurisdictionTemplate, PermitFormData } from '../../services/permitService';
import { panelService } from '../../services/panelService';
import { X, AlertCircle } from 'lucide-react';

interface PermitApplicationFormProps {
  projectId: string;
  onClose: () => void;
  onSuccess: () => void;
}

export const PermitApplicationForm: React.FC<PermitApplicationFormProps> = ({
  projectId,
  onClose,
  onSuccess
}) => {
  const [loading, setLoading] = useState(false);
  const [templates, setTemplates] = useState<JurisdictionTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<JurisdictionTemplate | null>(null);
  const [panels, setPanels] = useState<any[]>([]);
  const [project, setProject] = useState<any>(null);
  const [formData, setFormData] = useState<PermitFormData>({
    applicant_name: '',
    applicant_license: '',
    applicant_phone: '',
    applicant_email: '',
    property_address: '',
    property_city: '',
    property_state: '',
    property_zip: '',
    property_owner: '',
    property_type: 'RESIDENTIAL',
    project_type: 'NEW',
    project_description: '',
    estimated_cost: 0,
    square_footage: undefined,
    service_size: 0,
    voltage_system: '',
    main_disconnect_type: '',
    panel_count: 0,
    total_circuits: 0,
    grounding_system: '',
    has_generator: false,
    generator_size: undefined,
    has_solar: false,
    solar_system_size: undefined,
    total_connected_load: 0,
    total_demand_load: 0,
    load_calculation_method: 'STANDARD',
    nec_edition: '2023',
    local_amendments: '',
    contractor_name: '',
    contractor_license: '',
    contractor_phone: '',
    contractor_email: '',
    contractor_insurance: '',
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      // Load jurisdiction templates
      const templatesData = await permitService.getJurisdictionTemplates(undefined, 'PERMIT_APPLICATION');
      setTemplates(templatesData);

      // Load project data
      const projectResponse = await fetch(`/api/projects/${projectId}`);
      const projectData = await projectResponse.json();
      setProject(projectData);

      // Load panels for the project
      const panelsData = await panelService.getPanelsByProject(projectId);
      setPanels(panelsData);

      // Pre-fill form with project data
      setFormData(prev => ({
        ...prev,
        property_address: projectData.address || '',
        property_city: projectData.city || '',
        property_state: projectData.state || '',
        property_zip: projectData.zip || '',
        property_owner: projectData.customer?.name || '',
        property_type: projectData.type || 'RESIDENTIAL',
        service_size: projectData.service_size || 0,
        voltage_system: projectData.voltage_system || '',
        main_disconnect_type: projectData.main_disconnect_type || '',
        panel_count: panelsData.length,
        has_generator: projectData.has_generator || false,
        has_solar: projectData.has_solar || false,
        square_footage: projectData.square_footage || undefined,
      }));

      // Calculate total circuits and loads from panels
      if (panelsData.length > 0) {
        const totalCircuits = panelsData.reduce((sum: number, panel: any) => 
          sum + (panel.circuits?.length || 0), 0
        );
        const totalConnectedLoad = panelsData.reduce((sum: number, panel: any) => 
          sum + (panel.load_calculations?.[0]?.total_connected_load || 0), 0
        );
        const totalDemandLoad = panelsData.reduce((sum: number, panel: any) => 
          sum + (panel.load_calculations?.[0]?.total_demand_load || 0), 0
        );

        setFormData(prev => ({
          ...prev,
          total_circuits: totalCircuits,
          total_connected_load: totalConnectedLoad,
          total_demand_load: totalDemandLoad,
        }));
      }
    } catch (error) {
      console.error('Failed to load data:', error);
    }
  };

  const handleTemplateSelect = (templateId: string) => {
    const template = templates.find(t => t.id === templateId);
    setSelectedTemplate(template || null);
    
    if (template) {
      // Update form with template's jurisdiction info
      setFormData(prev => ({
        ...prev,
        nec_edition: template.nec_edition,
        local_amendments: template.local_amendments || '',
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setLoading(true);

      const jurisdiction = selectedTemplate?.jurisdiction_name || 'Unknown';
      const jurisdiction_code = selectedTemplate?.jurisdiction_code;

      await permitService.createPermitDocument({
        project_id: projectId,
        document_type: 'PERMIT_APPLICATION',
        jurisdiction,
        jurisdiction_code,
        title: `Electrical Permit Application - ${project?.name || 'Project'}`,
        description: `Permit application for ${formData.project_description}`,
        template_id: selectedTemplate?.id,
        form_data: formData,
      });

      onSuccess();
    } catch (error) {
      console.error('Failed to create permit document:', error);
      alert('Failed to create permit document');
    } finally {
      setLoading(false);
    }
  };

  const updateFormData = (field: keyof PermitFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-hidden">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>New Permit Application</CardTitle>
          <Button
            className="btn-secondary"
            size="sm"
            onClick={onClose}
          >
            <X className="h-4 w-4" />
          </Button>
        </CardHeader>
        <CardContent className="overflow-y-auto max-h-[calc(90vh-120px)]">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Jurisdiction Selection */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Jurisdiction</h3>
              <div>
                <Label htmlFor="jurisdiction">Select Jurisdiction Template</Label>
                <select
                  id="jurisdiction"
                  value={selectedTemplate?.id || ''}
                  onChange={(e) => handleTemplateSelect(e.target.value)}
                  className="mt-1 input"
                >
                  <option value="">Select a jurisdiction...</option>
                  {templates.map(template => (
                    <option key={template.id} value={template.id}>
                      {template.jurisdiction_name}, {template.state}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Applicant Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Applicant Information</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="applicant_name">Applicant Name *</Label>
                  <Input
                    id="applicant_name"
                    value={formData.applicant_name}
                    onChange={(e) => updateFormData('applicant_name', e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="applicant_license">License Number</Label>
                  <Input
                    id="applicant_license"
                    value={formData.applicant_license}
                    onChange={(e) => updateFormData('applicant_license', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="applicant_phone">Phone Number *</Label>
                  <Input
                    id="applicant_phone"
                    type="tel"
                    value={formData.applicant_phone}
                    onChange={(e) => updateFormData('applicant_phone', e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="applicant_email">Email Address *</Label>
                  <Input
                    id="applicant_email"
                    type="email"
                    value={formData.applicant_email}
                    onChange={(e) => updateFormData('applicant_email', e.target.value)}
                    required
                  />
                </div>
              </div>
            </div>

            {/* Property Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Property Information</h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="col-span-2">
                  <Label htmlFor="property_address">Street Address *</Label>
                  <Input
                    id="property_address"
                    value={formData.property_address}
                    onChange={(e) => updateFormData('property_address', e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="property_city">City *</Label>
                  <Input
                    id="property_city"
                    value={formData.property_city}
                    onChange={(e) => updateFormData('property_city', e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="property_state">State *</Label>
                  <Input
                    id="property_state"
                    value={formData.property_state}
                    onChange={(e) => updateFormData('property_state', e.target.value)}
                    maxLength={2}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="property_zip">ZIP Code *</Label>
                  <Input
                    id="property_zip"
                    value={formData.property_zip}
                    onChange={(e) => updateFormData('property_zip', e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="property_owner">Property Owner *</Label>
                  <Input
                    id="property_owner"
                    value={formData.property_owner}
                    onChange={(e) => updateFormData('property_owner', e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="property_type">Property Type *</Label>
                  <select
                    id="property_type"
                    value={formData.property_type}
                    onChange={(e) => updateFormData('property_type', e.target.value)}
                    className="mt-1 input"
                    required
                  >
                    <option value="RESIDENTIAL">Residential</option>
                    <option value="COMMERCIAL">Commercial</option>
                    <option value="INDUSTRIAL">Industrial</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Project Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Project Information</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="project_type">Project Type *</Label>
                  <select
                    id="project_type"
                    value={formData.project_type}
                    onChange={(e) => updateFormData('project_type', e.target.value)}
                    className="mt-1 input"
                    required
                  >
                    <option value="NEW">New Construction</option>
                    <option value="ALTERATION">Alteration</option>
                    <option value="ADDITION">Addition</option>
                    <option value="REPAIR">Repair</option>
                  </select>
                </div>
                <div>
                  <Label htmlFor="estimated_cost">Estimated Cost *</Label>
                  <Input
                    id="estimated_cost"
                    type="number"
                    value={formData.estimated_cost}
                    onChange={(e) => updateFormData('estimated_cost', parseFloat(e.target.value))}
                    required
                  />
                </div>
                <div className="col-span-2">
                  <Label htmlFor="project_description">Project Description *</Label>
                  <textarea
                    id="project_description"
                    className="mt-1 input"
                    rows={3}
                    value={formData.project_description}
                    onChange={(e) => updateFormData('project_description', e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="square_footage">Square Footage</Label>
                  <Input
                    id="square_footage"
                    type="number"
                    value={formData.square_footage || ''}
                    onChange={(e) => updateFormData('square_footage', e.target.value ? parseInt(e.target.value) : undefined)}
                  />
                </div>
              </div>
            </div>

            {/* Electrical System Details */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Electrical System Details</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="service_size">Service Size (Amps) *</Label>
                  <Input
                    id="service_size"
                    type="number"
                    value={formData.service_size}
                    onChange={(e) => updateFormData('service_size', parseInt(e.target.value))}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="voltage_system">Voltage System *</Label>
                  <Input
                    id="voltage_system"
                    value={formData.voltage_system}
                    onChange={(e) => updateFormData('voltage_system', e.target.value)}
                    placeholder="e.g., 120/240V 1PH"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="panel_count">Number of Panels</Label>
                  <Input
                    id="panel_count"
                    type="number"
                    value={formData.panel_count}
                    readOnly
                  />
                </div>
                <div>
                  <Label htmlFor="total_circuits">Total Circuits</Label>
                  <Input
                    id="total_circuits"
                    type="number"
                    value={formData.total_circuits}
                    readOnly
                  />
                </div>
                <div>
                  <Label htmlFor="grounding_system">Grounding System *</Label>
                  <Input
                    id="grounding_system"
                    value={formData.grounding_system}
                    onChange={(e) => updateFormData('grounding_system', e.target.value)}
                    placeholder="e.g., Ground Rod, Ufer"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="main_disconnect_type">Main Disconnect Type *</Label>
                  <Input
                    id="main_disconnect_type"
                    value={formData.main_disconnect_type}
                    onChange={(e) => updateFormData('main_disconnect_type', e.target.value)}
                    placeholder="e.g., Main Breaker"
                    required
                  />
                </div>
              </div>
            </div>

            {/* Load Calculation Summary */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Load Calculation Summary</h3>
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <div>
                  <p>Total Connected Load: {formData.total_connected_load.toFixed(0)} VA</p>
                  <p>Total Demand Load: {formData.total_demand_load.toFixed(0)} VA</p>
                  <p>Calculation Method: {formData.load_calculation_method}</p>
                </div>
              </Alert>
            </div>

            {/* Contractor Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Contractor Information</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="contractor_name">Contractor Name *</Label>
                  <Input
                    id="contractor_name"
                    value={formData.contractor_name}
                    onChange={(e) => updateFormData('contractor_name', e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="contractor_license">Contractor License *</Label>
                  <Input
                    id="contractor_license"
                    value={formData.contractor_license}
                    onChange={(e) => updateFormData('contractor_license', e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="contractor_phone">Contractor Phone *</Label>
                  <Input
                    id="contractor_phone"
                    type="tel"
                    value={formData.contractor_phone}
                    onChange={(e) => updateFormData('contractor_phone', e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="contractor_email">Contractor Email *</Label>
                  <Input
                    id="contractor_email"
                    type="email"
                    value={formData.contractor_email}
                    onChange={(e) => updateFormData('contractor_email', e.target.value)}
                    required
                  />
                </div>
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex justify-end gap-4 pt-4 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={loading || !selectedTemplate}
              >
                {loading ? 'Creating...' : 'Create Permit Application'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};