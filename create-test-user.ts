import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function createTestUser() {
  try {
    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (existingUser) {
      console.log('Test user already exists');
      return;
    }

    // Create password hash
    const passwordHash = await bcrypt.hash('password123', 12);

    // Create user
    const user = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password_hash: passwordHash,
        name: 'Test User',
        role: 'admin'
      }
    });

    console.log('Test user created:', user);
  } catch (error) {
    console.error('Error creating user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestUser();
EOF < /dev/null
