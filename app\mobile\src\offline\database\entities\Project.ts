// Temporarily using mock TypeORM - will be replaced with proper database solution
// Note: Decorators removed for React Native compatibility
import { BaseEntity } from './BaseEntity';
import { Panel } from './Panel';
import { Material } from './Material';
import { Calculation } from './Calculation';
import { Photo } from './Photo';

export class Project extends BaseEntity {
  name: string;

  description: string;

  address: string;

  clientName: string;

  clientPhone: string;

  clientEmail: string;

  status: 'active' | 'completed' | 'on-hold' | 'cancelled';

  estimatedBudget: number;

  actualCost: number;

  startDate: string;

  endDate: string;

  notes: string;

  metadata: string; // JSON string for additional data

  panels: Panel[];

  materials: Material[];

  calculations: Calculation[];

  photos: Photo[];
}