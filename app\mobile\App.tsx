import 'reflect-metadata'; // Required for TypeORM
import React, { useEffect } from 'react';
import { StatusBar } from 'react-native';
import { NativeBaseProvider } from 'native-base';
import { NavigationContainer } from '@react-navigation/native';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { SafeAreaProvider } from 'react-native-safe-area-context';

import { store, persistor } from '@store/index';
import { theme } from '@utils/theme';
import { navigationRef } from '@navigation/NavigationService';
import RootNavigator from '@navigation/RootNavigator';
import { SimpleLoadingScreen } from '@components/common/SimpleLoadingScreen';
import { SimpleErrorBoundary } from '@components/common/SimpleErrorBoundary';
import { OfflineNotice } from '@components/common/OfflineNotice';
import { OfflineIndicator } from '@components/offline/OfflineIndicator';
import { setupAxiosInterceptors } from '@services/api';
import { getDatabaseConnection } from '@offline/database/connection';
import { BackgroundSync } from '@offline/sync/BackgroundSync';
import { SyncEngine } from '@offline/sync/SyncEngine';

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 2,
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
    },
  },
});

const App: React.FC = () => {
  useEffect(() => {
    setupAxiosInterceptors();
    initializeOfflineCapabilities();
  }, []);

  const initializeOfflineCapabilities = async () => {
    try {
      // Initialize database connection
      await getDatabaseConnection();
      console.log('Database initialized successfully');

      // Initialize background sync
      const backgroundSync = BackgroundSync.getInstance();
      await backgroundSync.configure();
      console.log('Background sync configured');

      // Initialize sync engine
      const syncEngine = SyncEngine.getInstance();
      console.log('Sync engine initialized');

      // Perform initial sync if online
      const { NetworkMonitor } = await import('@offline/sync/NetworkMonitor');
      const networkMonitor = new NetworkMonitor();
      if (networkMonitor.getIsConnected()) {
        syncEngine.sync({ automatic: true });
      }
    } catch (error) {
      console.error('Failed to initialize offline capabilities:', error);
    }
  };

  return (
    <SimpleErrorBoundary>
      <GestureHandlerRootView style={{ flex: 1 }}>
        <Provider store={store}>
          <PersistGate loading={<SimpleLoadingScreen />} persistor={persistor}>
            <NativeBaseProvider theme={theme}>
              <QueryClientProvider client={queryClient}>
                <SafeAreaProvider>
                  <NavigationContainer ref={navigationRef}>
                    <StatusBar
                      barStyle="light-content"
                      backgroundColor="#1a202c"
                    />
                    <OfflineIndicator position="top" />
                    <OfflineNotice />
                    <RootNavigator />
                  </NavigationContainer>
                </SafeAreaProvider>
              </QueryClientProvider>
            </NativeBaseProvider>
          </PersistGate>
        </Provider>
      </GestureHandlerRootView>
    </SimpleErrorBoundary>
  );
};

export default App;