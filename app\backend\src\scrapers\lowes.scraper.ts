import { Page } from 'playwright';
import { BaseScraper, SearchResult } from './base.scraper';
import { logger } from '../utils/logger';

export class LowesScraper extends BaseScraper {
  protected searchUrl(query: string): string {
    const encoded = encodeURIComponent(query);
    return `https://www.lowes.com/search?searchTerm=${encoded}`;
  }

  protected async waitForResults(page: Page) {
    try {
      // Wait for product grid to load
      await page.waitForSelector('[data-testid="product-card"]', {
        timeout: 10000,
        state: 'visible'
      });
    } catch (error) {
      // Try alternative selectors
      await page.waitForSelector('.product-card, [class*="product-item"], .product-grid', {
        timeout: 10000,
        state: 'visible'
      });
    }
  }

  protected async extractResults(page: Page): Promise<SearchResult[]> {
    const results: SearchResult[] = [];

    try {
      // Take screenshot for debugging
      await this.takeScreenshot(page, 'lowes-search');

      // Extract product information
      const products = await page.evaluate(() => {
        const items: any[] = [];
        
        // Try multiple selectors for products
        const productElements = document.querySelectorAll(
          '[data-testid="product-card"], .product-card, [class*="product-item"]'
        );

        productElements.forEach((element) => {
          try {
            // Title
            const titleElement = element.querySelector(
              '[data-testid="product-title"], ' +
              '[class*="product-title"], ' +
              '.product-description h3, ' +
              '.product-card__title a, ' +
              'h3 a'
            );
            const title = titleElement?.textContent?.trim() || '';
            const url = titleElement?.getAttribute('href') || '';

            // Price
            const priceElement = element.querySelector(
              '[data-testid="item-price"], ' +
              '[data-testid="product-price"], ' +
              '.art-pr-price, ' +
              '[class*="price-format"], ' +
              '.item-price'
            );
            const priceText = priceElement?.textContent?.trim() || '';

            // Model/Item number
            const modelElement = element.querySelector(
              '[data-testid="product-model"], ' +
              '[data-testid="item-number"], ' +
              '.product-model, ' +
              '[class*="model-number"]'
            );
            const sku = modelElement?.textContent?.trim().replace(/[^\d]/g, '') || '';

            // Image
            const imageElement = element.querySelector('img');
            const imageUrl = imageElement?.getAttribute('src') || '';

            // Brand/Manufacturer
            const brandElement = element.querySelector(
              '[data-testid="product-brand"], ' +
              '.product-brand, ' +
              '[class*="brand-name"]'
            );
            const manufacturer = brandElement?.textContent?.trim() || '';

            // Rating
            const ratingElement = element.querySelector(
              '[data-testid="ratings"], ' +
              '.ratings, ' +
              '[class*="star-rating"]'
            );
            const ratingText = ratingElement?.getAttribute('aria-label') || '';
            const ratingMatch = ratingText.match(/(\d+\.?\d*)/);
            const rating = ratingMatch ? parseFloat(ratingMatch[1]) : undefined;

            // Review count
            const reviewElement = element.querySelector(
              '[data-testid="reviews-count"], ' +
              '.reviews-count, ' +
              '[class*="review-count"]'
            );
            const reviewText = reviewElement?.textContent?.trim() || '';
            const reviewMatch = reviewText.match(/\d+/);
            const reviewCount = reviewMatch ? parseInt(reviewMatch[0]) : undefined;

            // Availability
            const availabilityElement = element.querySelector(
              '[data-testid="availability"], ' +
              '.fulfillment-availability, ' +
              '[class*="availability"]'
            );
            const availability = availabilityElement?.textContent?.trim() || '';

            if (title && priceText) {
              items.push({
                title,
                priceText,
                url: url.startsWith('http') ? url : `https://www.lowes.com${url}`,
                sku,
                imageUrl,
                manufacturer,
                availability,
                rating,
                reviewCount
              });
            }
          } catch (err) {
            console.error('Error extracting product:', err);
          }
        });

        return items;
      });

      // Process extracted data
      for (const product of products) {
        const result: SearchResult = {
          title: product.title,
          price: this.parsePrice(product.priceText),
          url: product.url,
          sku: product.sku,
          imageUrl: product.imageUrl,
          manufacturer: product.manufacturer,
          availability: product.availability,
          rating: product.rating,
          reviewCount: product.reviewCount,
          unit: this.extractUnit(product.title, product.priceText),
          category: 'Electrical',
          confidence: 0.9
        };

        results.push(result);
      }

      logger.info(`Extracted ${results.length} results from Lowe's`);
    } catch (error) {
      logger.error('Error extracting Lowe\'s results:', error);
      await this.takeScreenshot(page, 'lowes-error');
    }

    return results;
  }

  protected async extractProductDetails(page: Page, url: string): Promise<SearchResult> {
    try {
      // Wait for product details to load
      await page.waitForSelector('h1[itemprop="name"], h1[class*="product-title"], h1', {
        timeout: 10000
      });

      const details = await page.evaluate(() => {
        // Title
        const titleElement = document.querySelector(
          'h1[itemprop="name"], ' +
          'h1[class*="product-title"], ' +
          '[data-testid="product-title"], ' +
          'h1'
        );
        const title = titleElement?.textContent?.trim() || '';

        // Price
        const priceElement = document.querySelector(
          '[data-testid="item-price"], ' +
          '.art-pr-price, ' +
          '[class*="main-price"], ' +
          '.item-price-dollar'
        );
        const priceText = priceElement?.textContent?.trim() || '';

        // Model/Item number
        const modelElement = document.querySelector(
          '[data-testid="item-number"], ' +
          '#productDetails .product-model, ' +
          '[class*="model-number"]'
        );
        const sku = modelElement?.textContent?.trim().replace(/[^\d]/g, '') || '';

        // Description
        const descElement = document.querySelector(
          '[data-testid="product-description"], ' +
          '.product-description, ' +
          '[class*="description-content"]'
        );
        const description = descElement?.textContent?.trim() || '';

        // Image
        const imageElement = document.querySelector(
          '[data-testid="product-image"] img, ' +
          '.main-image img, ' +
          '[class*="product-image"] img'
        );
        const imageUrl = imageElement?.getAttribute('src') || '';

        // Brand
        const brandElement = document.querySelector(
          '[itemprop="brand"], ' +
          '[data-testid="product-brand"], ' +
          '.product-brand'
        );
        const manufacturer = brandElement?.textContent?.trim() || '';

        // Availability
        const availElement = document.querySelector(
          '[data-testid="fulfillment"], ' +
          '.fulfillment-container, ' +
          '[class*="availability-message"]'
        );
        const availability = availElement?.textContent?.trim() || '';

        // Specifications
        const specs: Record<string, string> = {};
        const specRows = document.querySelectorAll(
          '.product-specifications tr, ' +
          '[data-testid="specifications-table"] tr, ' +
          '.specs-table__row'
        );
        specRows.forEach(row => {
          const cells = row.querySelectorAll('td');
          if (cells.length >= 2) {
            const label = cells[0]?.textContent?.trim();
            const value = cells[1]?.textContent?.trim();
            if (label && value) {
              specs[label] = value;
            }
          }
        });

        // Rating
        const ratingElement = document.querySelector(
          '[data-testid="ratings"] [itemprop="ratingValue"], ' +
          '.ratings .stars'
        );
        const ratingText = ratingElement?.getAttribute('aria-label') || 
                          ratingElement?.textContent || '';
        const ratingMatch = ratingText.match(/(\d+\.?\d*)/);
        const rating = ratingMatch ? parseFloat(ratingMatch[1]) : undefined;

        return {
          title,
          priceText,
          sku,
          description,
          imageUrl,
          manufacturer,
          availability,
          specifications: specs,
          rating
        };
      });

      return {
        title: details.title,
        price: this.parsePrice(details.priceText),
        url,
        sku: details.sku,
        description: details.description,
        imageUrl: details.imageUrl,
        manufacturer: details.manufacturer,
        availability: details.availability,
        rating: details.rating,
        unit: this.extractUnit(details.title, details.priceText),
        category: 'Electrical',
        confidence: 0.95
      };

    } catch (error) {
      logger.error('Error extracting Lowe\'s product details:', error);
      throw error;
    }
  }

  private extractUnit(title: string, priceText: string): string {
    // Check for common units in title or price
    const text = `${title} ${priceText}`.toLowerCase();
    
    if (text.includes('/ft') || text.includes('per foot')) return 'FT';
    if (text.includes('/in') || text.includes('per inch')) return 'IN';
    if (text.includes('/yd') || text.includes('per yard')) return 'YD';
    if (text.includes('/box')) return 'BOX';
    if (text.includes('/case')) return 'CASE';
    if (text.includes('/roll')) return 'ROLL';
    if (text.includes('/pkg') || text.includes('/pack')) return 'PKG';
    if (text.includes('/pair')) return 'PAIR';
    if (text.includes('/set')) return 'SET';
    if (text.includes('/bag')) return 'BAG';
    
    // Check for quantity indicators
    const qtyMatch = text.match(/\((\d+)[- ]?(pack|pk|count|ct|pc)\)/i);
    if (qtyMatch) return 'PKG';
    
    // Default to each
    return 'EA';
  }
}