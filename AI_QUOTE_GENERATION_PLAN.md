# AI-Powered Quote Generation System Implementation Plan

## Executive Summary

This document outlines the comprehensive implementation plan for an advanced quote generation system with AI-powered features and real-time material price scraping for our electrical contracting application. The system is designed to rival industry leaders like ServiceTitan and Housecall Pro while providing unique capabilities tailored for electrical contractors.

## Key Features Overview

### 1. AI-Powered Quote Generation
- Natural language input processing (text and images)
- Smart material recognition from descriptions/photos
- Interactive clarification questions
- Multi-model AI fallback system
- Automated quote section generation

### 2. Real-Time Material Price Scraping
- Multi-source price aggregation (Home Depot, Lowe's, distributors)
- Intelligent caching with Redis
- User selection interface for ambiguous matches
- Background price update processing
- Historical price tracking and analytics

### 3. Advanced Quote Management
- Version control with revision history
- Complete status workflow (DRAFT → SENT → APPROVED/REJECTED → EXPIRED)
- Professional PDF generation with branding
- Direct email integration
- Customer portal for online viewing/approval
- Digital signature capture

### 4. AI-Enhanced Features
- Cost prediction based on historical data
- Bid optimization with market analysis
- Material alternative suggestions
- NEC compliance validation
- Real-time profit margin analysis

## Technical Architecture

### Backend Stack
- **Framework**: Node.js/Express with TypeScript
- **Database**: MongoDB with Prisma ORM
- **Caching**: Redis for hot data
- **Queue**: BullMQ for background jobs
- **AI Integration**: Google Gemini API (primary), Deepseek (fallback)
- **Web Scraping**: Playwright, Puppeteer, Cheerio
- **PDF Generation**: PDFKit or Puppeteer
- **Email**: SendGrid or AWS SES

### Frontend Stack
- **Framework**: React 18 with TypeScript
- **State Management**: Zustand + TanStack Query
- **UI Components**: Shadcn/ui + custom components
- **Forms**: React Hook Form with Zod validation
- **File Upload**: react-dropzone
- **Rich Text Editor**: TipTap or Slate.js
- **Charts**: Recharts for analytics
- **PDF Viewer**: react-pdf

## Database Schema

### Quote Model
```javascript
{
  // Basic Information
  quoteNumber: { type: String, unique: true, required: true },
  name: { type: String, required: true },
  customer: { type: ObjectId, ref: 'Customer', required: true },
  project: { type: ObjectId, ref: 'Project', required: true },
  company: { type: ObjectId, ref: 'Company', required: true },
  
  // Version Control
  version: { type: Number, default: 1 },
  parentQuote: { type: ObjectId, ref: 'Quote' },
  revisionHistory: [{
    version: Number,
    createdAt: Date,
    createdBy: ObjectId,
    changes: String
  }],
  
  // Quote Content
  projectOverview: { type: String },
  scopeOfWork: { type: String },
  materialsIncluded: { type: String },
  exclusions: { type: String },
  termsAndConditions: { type: String },
  
  // Line Items
  items: [{
    // Basic Item Info
    name: String,
    description: String,
    category: String,
    quantity: Number,
    unit: String,
    
    // Pricing
    unitPrice: Number,
    totalPrice: Number,
    cost: Number,
    margin: Number,
    
    // Material Lookup
    materialId: String,
    sku: String,
    manufacturer: String,
    imageUrl: String,
    sourceUrl: String,
    
    // AI/Lookup Status
    lookupStatus: {
      type: String,
      enum: ['pending', 'searching', 'options_available', 'confirmed', 'failed', 'manual'],
      default: 'pending'
    },
    lookupResults: [{
      source: String,
      price: Number,
      url: String,
      description: String,
      imageUrl: String,
      confidence: Number,
      timestamp: Date
    }],
    priceHistory: [{
      price: Number,
      source: String,
      timestamp: Date
    }],
    
    // Additional Fields
    notes: String,
    isOptional: Boolean,
    groupId: String
  }],
  
  // AI Generation Fields
  aiGenerationData: {
    inputType: { type: String, enum: ['text', 'image', 'mixed'] },
    originalInput: mongoose.Schema.Types.Mixed,
    clarificationQuestions: [String],
    userAnswers: mongoose.Schema.Types.Mixed,
    generationStatus: {
      type: String,
      enum: ['idle', 'pending_questions', 'generating', 'complete', 'error'],
      default: 'idle'
    },
    modelUsed: String,
    processingTime: Number,
    error: String
  },
  
  // Pricing Summary
  subtotal: { type: Number, required: true },
  taxRate: { type: Number, default: 0 },
  taxAmount: { type: Number, default: 0 },
  discount: { type: Number, default: 0 },
  discountType: { type: String, enum: ['percentage', 'fixed'], default: 'percentage' },
  total: { type: Number, required: true },
  
  // Status and Workflow
  status: {
    type: String,
    enum: ['DRAFT', 'SENT', 'VIEWED', 'APPROVED', 'REJECTED', 'EXPIRED', 'CONVERTED'],
    default: 'DRAFT'
  },
  sentAt: Date,
  viewedAt: Date,
  approvedAt: Date,
  rejectedAt: Date,
  expiresAt: Date,
  
  // Conversion Tracking
  convertedToJob: { type: ObjectId, ref: 'Job' },
  convertedToInvoice: { type: ObjectId, ref: 'Invoice' },
  
  // Customer Interaction
  customerNotes: String,
  internalNotes: String,
  customerSignature: {
    name: String,
    signedAt: Date,
    ipAddress: String,
    signatureData: String
  },
  
  // Metadata
  createdBy: { type: ObjectId, ref: 'User', required: true },
  updatedBy: { type: ObjectId, ref: 'User' },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
}
```

### MaterialPriceHistory Model
```javascript
{
  materialId: { type: String, required: true, index: true },
  sku: { type: String, index: true },
  name: { type: String, required: true },
  description: String,
  manufacturer: String,
  
  priceData: {
    price: { type: Number, required: true },
    unit: String,
    currency: { type: String, default: 'USD' },
    source: { type: String, required: true },
    url: String,
    confidence: Number
  },
  
  metadata: {
    category: String,
    subcategory: String,
    specifications: mongoose.Schema.Types.Mixed,
    imageUrl: String
  },
  
  scrapedAt: { type: Date, default: Date.now },
  validUntil: Date,
  
  company: { type: ObjectId, ref: 'Company' },
  createdAt: { type: Date, default: Date.now }
}
```

### PriceScrapingLog Model
```javascript
{
  requestId: { type: String, unique: true, required: true },
  quoteId: { type: ObjectId, ref: 'Quote' },
  itemId: String,
  
  searchQuery: { type: String, required: true },
  searchParams: mongoose.Schema.Types.Mixed,
  
  source: {
    type: String,
    enum: ['HOME_DEPOT', 'LOWES', 'GRAYBAR', 'CED', 'GOOGLE_SHOPPING', 'MANUAL'],
    required: true
  },
  
  status: {
    type: String,
    enum: ['PENDING', 'PROCESSING', 'SUCCESS', 'PARTIAL', 'FAILED', 'RATE_LIMITED', 'BLOCKED'],
    default: 'PENDING'
  },
  
  results: [{
    title: String,
    price: Number,
    url: String,
    sku: String,
    description: String,
    imageUrl: String,
    availability: String,
    confidence: Number
  }],
  
  error: {
    code: String,
    message: String,
    details: mongoose.Schema.Types.Mixed
  },
  
  performance: {
    startTime: Date,
    endTime: Date,
    duration: Number,
    retryCount: Number
  },
  
  userId: { type: ObjectId, ref: 'User' },
  createdAt: { type: Date, default: Date.now }
}
```

## Implementation Phases

### Phase 1: Backend Infrastructure (Days 1-3)

#### Day 1: Database Models and Core Setup
- Create Quote, MaterialPriceHistory, and PriceScrapingLog models
- Set up model relationships and indexes
- Implement model validation and hooks
- Create migration scripts

#### Day 2: API Routes and Controllers
- Implement CRUD operations for quotes
- Create AI generation endpoints
- Add price refresh endpoints
- Implement PDF generation route
- Set up email sending endpoints

#### Day 3: Service Layer Architecture
- Create QuoteService for business logic
- Implement AIQuoteGenerationService
- Build MaterialPriceLookupService
- Set up PDFGenerationService
- Create EmailService with templates

### Phase 2: AI Integration (Days 4-5)

#### Day 4: AI Service Setup
- Configure Gemini API integration
- Implement multi-model fallback logic
- Create prompt templates for electrical context
- Set up rate limiting and retry mechanisms

#### Day 5: Quote Generation Pipeline
- Build input processing for text/images
- Implement context enrichment
- Create structured output parsers
- Add validation and error handling

### Phase 3: Price Scraping System (Days 6-7)

#### Day 6: Scraper Infrastructure
- Create base scraper class
- Implement Home Depot scraper
- Build Lowe's scraper
- Add anti-detection measures

#### Day 7: Price Lookup Flow
- Implement caching layer with Redis
- Create parallel source querying
- Build user selection interface
- Set up price storage system

### Phase 4: Frontend Implementation (Days 8-10)

#### Day 8: Quote Creation UI
- Build multi-step form component
- Implement file upload functionality
- Create real-time preview
- Add material search autocomplete

#### Day 9: AI Assistant Interface
- Create chat-like UI component
- Implement question/answer flow
- Add progress indicators
- Build inline editing capabilities

#### Day 10: Management Dashboard
- Create quote list with filters
- Implement status tracking
- Add analytics widgets
- Build quick action tools

### Phase 5: Testing & Optimization (Days 11-12)

#### Day 11: Testing
- Write unit tests for services
- Create integration tests
- Implement E2E test suite
- Perform load testing

#### Day 12: Optimization & Documentation
- Optimize database queries
- Improve caching strategies
- Create API documentation
- Write user guides

## API Endpoints

### Quote Management
```
POST   /api/quotes                    - Create new quote
GET    /api/quotes                    - List quotes with filters
GET    /api/quotes/:id                - Get quote details
PUT    /api/quotes/:id                - Update quote
DELETE /api/quotes/:id                - Delete quote
POST   /api/quotes/:id/duplicate      - Duplicate quote
POST   /api/quotes/:id/convert        - Convert to job/invoice
```

### AI Generation
```
POST   /api/quotes/generate-ai        - Generate quote with AI
POST   /api/quotes/:id/ai-questions   - Answer clarification questions
POST   /api/quotes/ai-suggest         - Get AI suggestions
```

### Material Pricing
```
GET    /api/quotes/:id/prices         - Get all item prices
POST   /api/quotes/:id/prices/refresh - Refresh material prices
POST   /api/quotes/:id/items/:itemId/price - Update single item price
GET    /api/materials/search          - Search materials
GET    /api/materials/price-history   - Get price history
```

### Quote Actions
```
POST   /api/quotes/:id/send           - Send quote via email
GET    /api/quotes/:id/pdf            - Generate PDF
POST   /api/quotes/:id/approve        - Customer approval
POST   /api/quotes/:id/reject         - Customer rejection
GET    /api/quotes/public/:token      - Public quote view
```

## Environment Configuration

```bash
# AI Configuration
GEMINI_API_KEY=your_gemini_api_key
AI_MODEL_PRIMARY=gemini-2.5-pro-exp-03-25
AI_MODEL_FALLBACK_1=gemini-2.0-flash
AI_MODEL_FALLBACK_2=gemini-1.5-flash
AI_PRIMARY_RATE_LIMIT=10
AI_FALLBACK_RATE_LIMIT=120

# Scraping Configuration
SCRAPER_USER_AGENT_ROTATION=true
SCRAPER_MAX_RETRIES=3
SCRAPER_TIMEOUT_MS=30000
SCRAPER_DELAY_MS=1000

# Redis Configuration
REDIS_URL=redis://localhost:6379
CACHE_TTL_SECONDS=86400
PRICE_CACHE_TTL=3600

# Email Configuration
SENDGRID_API_KEY=your_sendgrid_key
EMAIL_FROM=<EMAIL>
EMAIL_REPLY_TO=<EMAIL>

# PDF Configuration
PDF_LOGO_URL=https://yourcompany.com/logo.png
PDF_FOOTER_TEXT=Licensed Electrical Contractor #12345

# Queue Configuration
BULL_REDIS_URL=redis://localhost:6379
QUEUE_CONCURRENCY=5
QUEUE_MAX_RETRIES=3
```

## Security Considerations

### API Security
- JWT authentication for all endpoints
- Rate limiting per user/IP
- Input validation with Zod schemas
- SQL injection prevention via Prisma
- XSS protection for user inputs

### AI Security
- Prompt injection prevention
- Content filtering for inappropriate requests
- API key encryption at rest
- Request/response logging for audit

### Scraping Security
- Respect robots.txt
- Implement request delays
- Use rotating proxies if needed
- Handle CAPTCHAs gracefully

### Data Security
- Encrypt sensitive quote data
- Secure file upload handling
- CORS configuration for customer portal
- SSL/TLS for all communications

## Monitoring and Analytics

### Performance Metrics
- AI response times
- Scraper success rates
- Cache hit/miss ratios
- API endpoint latencies

### Business Metrics
- Quote creation rate
- Conversion rate (quote to job)
- Average quote value
- Material price accuracy

### Error Tracking
- Failed AI generations
- Scraping failures by source
- API errors by endpoint
- User-reported issues

## Deployment Strategy

### Development Environment
- Local MongoDB instance
- Local Redis server
- Mock external APIs
- Test data seeders

### Staging Environment
- Replicate production setup
- Use sandbox API keys
- Limited scraping rates
- Test with real users

### Production Environment
- MongoDB Atlas cluster
- Redis Cloud instance
- Production API keys
- Full monitoring suite

## Future Enhancements

### Phase 2 Features
- Mobile app integration
- Voice input for quotes
- AR measurement tools
- Supplier API integrations

### Phase 3 Features
- Machine learning for pricing
- Automated follow-ups
- Customer behavior analytics
- Multi-language support

### Phase 4 Features
- Blockchain for contracts
- IoT sensor integration
- Predictive maintenance quotes
- Virtual consultation tools

## Success Metrics

### Technical KPIs
- 99.9% uptime
- <2s page load times
- <5s AI response time
- >90% scraping success rate

### Business KPIs
- 50% reduction in quote creation time
- 30% increase in quote accuracy
- 25% improvement in win rate
- 40% faster quote turnaround

## Conclusion

This comprehensive quote generation system will position our electrical contracting application as a market leader, offering advanced AI capabilities and real-time pricing that surpass current industry solutions. The modular architecture ensures scalability and maintainability as we continue to add features based on user feedback and market demands.