{"extends": "@react-native/typescript-config/tsconfig.json", "compilerOptions": {"target": "esnext", "module": "es2020", "lib": ["es2017"], "jsx": "react-native", "strict": true, "allowJs": true, "skipLibCheck": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "isolatedModules": true, "moduleResolution": "bundler", "resolveJsonModule": true, "noEmit": true, "forceConsistentCasingInFileNames": true, "baseUrl": "./", "paths": {"@/*": ["src/*"], "@components/*": ["src/components/*"], "@screens/*": ["src/screens/*"], "@navigation/*": ["src/navigation/*"], "@services/*": ["src/services/*"], "@store/*": ["src/store/*"], "@utils/*": ["src/utils/*"], "@types/*": ["src/types/*"], "@hooks/*": ["src/hooks/*"], "@constants/*": ["src/constants/*"], "@offline/*": ["src/offline/*"]}}, "include": ["src/**/*", "index.js", "jest.config.js", "global.d.ts"], "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"]}