diff --git a/node_modules/react-native-encrypted-storage/android/.gradle/6.6.1/fileChanges/last-build.bin b/node_modules/react-native-encrypted-storage/android/.gradle/6.6.1/fileChanges/last-build.bin
new file mode 100644
index 0000000..f76dd23
Binary files /dev/null and b/node_modules/react-native-encrypted-storage/android/.gradle/6.6.1/fileChanges/last-build.bin differ
diff --git a/node_modules/react-native-encrypted-storage/android/.gradle/6.6.1/fileHashes/fileHashes.lock b/node_modules/react-native-encrypted-storage/android/.gradle/6.6.1/fileHashes/fileHashes.lock
new file mode 100644
index 0000000..68a4e55
Binary files /dev/null and b/node_modules/react-native-encrypted-storage/android/.gradle/6.6.1/fileHashes/fileHashes.lock differ
diff --git a/node_modules/react-native-encrypted-storage/android/.gradle/6.6.1/gc.properties b/node_modules/react-native-encrypted-storage/android/.gradle/6.6.1/gc.properties
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-encrypted-storage/android/.gradle/checksums/checksums.lock b/node_modules/react-native-encrypted-storage/android/.gradle/checksums/checksums.lock
new file mode 100644
index 0000000..0eed409
Binary files /dev/null and b/node_modules/react-native-encrypted-storage/android/.gradle/checksums/checksums.lock differ
diff --git a/node_modules/react-native-encrypted-storage/android/.gradle/configuration-cache/gc.properties b/node_modules/react-native-encrypted-storage/android/.gradle/configuration-cache/gc.properties
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-encrypted-storage/android/.gradle/nb-cache/trust/22D541804564E3686435527A0CED7E937633CF081502BD3D1F34A47D7E040C8C b/node_modules/react-native-encrypted-storage/android/.gradle/nb-cache/trust/22D541804564E3686435527A0CED7E937633CF081502BD3D1F34A47D7E040C8C
new file mode 100644
index 0000000..b102a1c
--- /dev/null
+++ b/node_modules/react-native-encrypted-storage/android/.gradle/nb-cache/trust/22D541804564E3686435527A0CED7E937633CF081502BD3D1F34A47D7E040C8C
@@ -0,0 +1 @@
+2F4AC843AD98C3E170AAB7A298786DB3D279C46EB5AD69C0B3537CDEDCB15DF0
diff --git a/node_modules/react-native-encrypted-storage/android/.gradle/nb-cache/trust/91F8D82D1E89C7B88E267D14969E83D7427B27A6CB5CD1A3B286551305087721 b/node_modules/react-native-encrypted-storage/android/.gradle/nb-cache/trust/91F8D82D1E89C7B88E267D14969E83D7427B27A6CB5CD1A3B286551305087721
new file mode 100644
index 0000000..b5ad82f
--- /dev/null
+++ b/node_modules/react-native-encrypted-storage/android/.gradle/nb-cache/trust/91F8D82D1E89C7B88E267D14969E83D7427B27A6CB5CD1A3B286551305087721
@@ -0,0 +1 @@
+3E574A37F20CBBA6525DD7E4F9749FC3B39066D7EC9228FBA846B0B2714C1986
diff --git a/node_modules/react-native-encrypted-storage/android/.gradle/vcs-1/gc.properties b/node_modules/react-native-encrypted-storage/android/.gradle/vcs-1/gc.properties
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-encrypted-storage/android/build.gradle b/node_modules/react-native-encrypted-storage/android/build.gradle
index b343dc8..14a8962 100644
--- a/node_modules/react-native-encrypted-storage/android/build.gradle
+++ b/node_modules/react-native-encrypted-storage/android/build.gradle
@@ -1,14 +1,3 @@
-buildscript {
-  repositories {
-    mavenCentral()
-    google()
-  }
-
-  dependencies {
-    classpath 'com.android.tools.build:gradle:4.1.1'
-  }
-}
-
 apply plugin: 'com.android.library'
 
 def getExtOrDefault(name) {
@@ -20,6 +9,11 @@ def getExtOrIntegerDefault(name) {
 }
 
 android {
+  def agpVersion = com.android.Version.ANDROID_GRADLE_PLUGIN_VERSION
+  if (agpVersion.tokenize('.')[0].toInteger() >= 7) {
+    namespace "com.emeraldsanto.encryptedstorage"
+  }
+  
   compileSdkVersion getExtOrIntegerDefault('compileSdkVersion')
   buildToolsVersion getExtOrDefault('buildToolsVersion')
 
