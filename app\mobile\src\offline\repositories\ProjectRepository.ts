import { BaseRepository } from './BaseRepository';
import { Project } from '../database/entities';
// Temporarily using mock TypeORM - will be replaced with proper database solution
import { FindManyOptions } from '../database/mockTypeorm';

export class ProjectRepository extends BaseRepository<Project> {
  constructor() {
    super('Project');
  }

  async findByStatus(status: 'active' | 'completed' | 'on-hold' | 'cancelled'): Promise<Project[]> {
    await this.ensureRepository();
    return this.repository.find({
      where: {
        status,
        isDeleted: false,
      },
      relations: ['panels', 'materials', 'calculations', 'photos'],
      order: {
        updatedAt: 'DESC',
      },
    });
  }

  async findWithFullDetails(id: string): Promise<Project | null> {
    await this.ensureRepository();
    return this.repository.findOne({
      where: {
        id,
        isDeleted: false,
      },
      relations: ['panels', 'panels.circuits', 'materials', 'calculations', 'photos'],
    });
  }

  async searchProjects(searchTerm: string): Promise<Project[]> {
    return this.search(searchTerm, ['name', 'address', 'clientName', 'description']);
  }

  async getProjectStats(projectId: string): Promise<{
    totalPanels: number;
    totalCircuits: number;
    totalMaterials: number;
    totalCost: number;
    completionPercentage: number;
  }> {
    await this.ensureRepository();
    
    const project = await this.findWithFullDetails(projectId);
    if (!project) {
      throw new Error('Project not found');
    }

    const totalCircuits = project.panels.reduce((sum, panel) => 
      sum + (panel.circuits?.length || 0), 0
    );

    const totalCost = project.materials.reduce((sum, material) => 
      sum + material.totalPrice, 0
    );

    // Calculate completion based on material status
    const installedMaterials = project.materials.filter(m => m.status === 'installed').length;
    const completionPercentage = project.materials.length > 0
      ? (installedMaterials / project.materials.length) * 100
      : 0;

    return {
      totalPanels: project.panels.length,
      totalCircuits,
      totalMaterials: project.materials.length,
      totalCost,
      completionPercentage,
    };
  }

  async getActiveProjects(): Promise<Project[]> {
    return this.findByStatus('active');
  }

  async duplicateProject(projectId: string, newName: string): Promise<Project> {
    await this.ensureRepository();
    
    const original = await this.findWithFullDetails(projectId);
    if (!original) {
      throw new Error('Project not found');
    }

    // Create new project
    const newProject = await this.create({
      name: newName,
      description: original.description,
      address: original.address,
      clientName: original.clientName,
      clientPhone: original.clientPhone,
      clientEmail: original.clientEmail,
      status: 'active',
      estimatedBudget: original.estimatedBudget,
      notes: `Duplicated from project: ${original.name}`,
      metadata: original.metadata,
    });

    // TODO: Duplicate related entities (panels, materials, etc.)

    return newProject;
  }

  async getProjectsByDateRange(startDate: Date, endDate: Date): Promise<Project[]> {
    await this.ensureRepository();
    
    const qb = this.repository.createQueryBuilder('project');
    
    return qb
      .where('project.isDeleted = :isDeleted', { isDeleted: false })
      .andWhere('project.startDate >= :startDate', { startDate: startDate.toISOString() })
      .andWhere('project.startDate <= :endDate', { endDate: endDate.toISOString() })
      .orderBy('project.startDate', 'ASC')
      .getMany();
  }

  async updateProjectBudget(projectId: string, actualCost: number): Promise<void> {
    await this.ensureRepository();
    
    const project = await this.findById(projectId);
    if (!project) {
      throw new Error('Project not found');
    }

    await this.update(projectId, {
      actualCost,
    });
  }
}