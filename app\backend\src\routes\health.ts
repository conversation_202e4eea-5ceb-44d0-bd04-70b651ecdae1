import { Router } from 'express';
import { HealthCheckService } from '../services/health-check.service';
import { dbConnectionManager } from '../database/connection-manager';
import { asyncErrorHandler } from '../middleware/enhanced-error-handler';
import { redisManager } from '../services/redis-manager';

const router = Router();

// Initialize health check service
let healthCheckService: HealthCheckService;

// Initialize health check service when database is ready
async function initializeHealthCheck() {
  try {
    const prisma = await dbConnectionManager.getClient();
    
    // Get Redis instance from Redis manager
    const redis = redisManager.getClient() || undefined;

    healthCheckService = new HealthCheckService(prisma, redis);
    
    // Start periodic health checks
    healthCheckService.startPeriodicChecks(30000); // Every 30 seconds
  } catch (error) {
    console.error('Failed to initialize health check service:', error);
  }
}

// Initialize on startup
initializeHealthCheck();

/**
 * Health check endpoint
 * Returns detailed health status of all services
 */
router.get('/health', asyncErrorHandler(async (req, res) => {
  if (!healthCheckService) {
    res.status(503).json({
      status: 'unhealthy',
      message: 'Health check service not initialized',
    });
    return;
  }

  await healthCheckService.handleHealthCheck(req, res);
}));

/**
 * Liveness probe endpoint (for Kubernetes)
 * Simple check to verify the service is alive
 */
router.get('/health/live', (req, res) => {
  if (!healthCheckService) {
    res.status(503).json({ status: 'not_alive' });
    return;
  }

  healthCheckService.handleLiveness(req, res);
});

/**
 * Readiness probe endpoint (for Kubernetes)
 * Checks if the service is ready to handle requests
 */
router.get('/health/ready', asyncErrorHandler(async (req, res) => {
  if (!healthCheckService) {
    res.status(503).json({ status: 'not_ready' });
    return;
  }

  await healthCheckService.handleReadiness(req, res);
}));

/**
 * Metrics endpoint
 * Returns performance and operational metrics
 */
router.get('/health/metrics', asyncErrorHandler(async (req, res) => {
  const metrics = {
    timestamp: new Date().toISOString(),
    process: {
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      cpu: process.cpuUsage(),
      pid: process.pid,
      version: process.version,
    },
    database: dbConnectionManager.getStatus(),
    redis: redisManager.getHealth(),
    lastHealthCheck: healthCheckService?.getLastResult(),
  };

  res.json(metrics);
}));

export default router;