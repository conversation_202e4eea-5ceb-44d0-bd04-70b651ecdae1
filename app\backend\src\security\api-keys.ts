import { Request, Response, NextFunction } from 'express';
import { prisma } from '../database/prisma';
import { AppError } from '../middleware/errorHandler';
import { generateApiKey, hashToken, secureCompare } from './crypto';
import { createAuditLog } from './audit';

export interface ApiKeyRequest extends Request {
  apiKey?: {
    id: string;
    name: string;
    scopes: string[];
  };
}

// API Key model extension (add to Prisma schema)
export const API_KEY_SCOPES = {
  READ_PROJECTS: 'read:projects',
  WRITE_PROJECTS: 'write:projects',
  READ_CALCULATIONS: 'read:calculations',
  WRITE_CALCULATIONS: 'write:calculations',
  READ_PANELS: 'read:panels',
  WRITE_PANELS: 'write:panels',
  READ_INSPECTIONS: 'read:inspections',
  WRITE_INSPECTIONS: 'write:inspections',
  WEBHOOKS: 'webhooks',
  ADMIN: 'admin'
} as const;

export type ApiKeyScope = typeof API_KEY_SCOPES[keyof typeof API_KEY_SCOPES];

// Create new API key
export async function createApiKey(
  userId: string,
  name: string,
  scopes: ApiKeyScope[],
  expiresAt?: Date
): Promise<{ key: string; id: string }> {
  const { key, hash } = generateApiKey('ec');
  
  // Store in database (you'll need to add ApiKey model to Prisma)
  const apiKey = await prisma.$executeRaw`
    INSERT INTO api_keys (id, user_id, name, key_hash, scopes, expires_at, created_at, last_used_at)
    VALUES (${crypto.randomUUID()}, ${userId}, ${name}, ${hash}, ${JSON.stringify(scopes)}, ${expiresAt}, ${new Date()}, NULL)
    RETURNING id
  `;
  
  await createAuditLog({
    action: 'API_KEY_CREATED',
    userId,
    resourceType: 'api_key',
    resourceId: apiKey.toString(),
    details: { name, scopes }
  });
  
  return { key, id: apiKey.toString() };
}

// Middleware for API key authentication
export function apiKeyAuth(requiredScopes?: ApiKeyScope[]) {
  return async (req: ApiKeyRequest, res: Response, next: NextFunction) => {
    const apiKey = extractApiKey(req);
    
    if (!apiKey) {
      return next(new AppError(401, 'API key required', true, 'API_KEY_REQUIRED'));
    }
    
    try {
      // Hash the provided key
      const keyHash = hashToken(apiKey);
      
      // Look up in database
      const keyRecord = await prisma.$queryRaw<any>`
        SELECT id, user_id, name, scopes, expires_at, is_active
        FROM api_keys
        WHERE key_hash = ${keyHash}
      `;
      
      if (!keyRecord || keyRecord.length === 0) {
        return next(new AppError(401, 'Invalid API key', true, 'INVALID_API_KEY'));
      }
      
      const key = keyRecord[0];
      
      // Check if active
      if (!key.is_active) {
        return next(new AppError(401, 'API key disabled', true, 'API_KEY_DISABLED'));
      }
      
      // Check expiration
      if (key.expires_at && new Date(key.expires_at) < new Date()) {
        return next(new AppError(401, 'API key expired', true, 'API_KEY_EXPIRED'));
      }
      
      // Check scopes
      const keyScopes = JSON.parse(key.scopes) as ApiKeyScope[];
      if (requiredScopes && requiredScopes.length > 0) {
        const hasRequiredScopes = requiredScopes.every(scope => 
          keyScopes.includes(scope) || keyScopes.includes(API_KEY_SCOPES.ADMIN)
        );
        
        if (!hasRequiredScopes) {
          return next(new AppError(403, 'Insufficient API key permissions', true, 'INSUFFICIENT_PERMISSIONS'));
        }
      }
      
      // Update last used timestamp
      await prisma.$executeRaw`
        UPDATE api_keys 
        SET last_used_at = ${new Date()}, usage_count = usage_count + 1
        WHERE id = ${key.id}
      `;
      
      // Attach to request
      req.apiKey = {
        id: key.id,
        name: key.name,
        scopes: keyScopes
      };
      
      next();
    } catch (error) {
      next(new AppError(500, 'API key verification failed', true, 'API_KEY_ERROR'));
    }
  };
}

// Extract API key from request
function extractApiKey(req: Request): string | null {
  // Check Authorization header
  const authHeader = req.headers.authorization;
  if (authHeader && authHeader.startsWith('ApiKey ')) {
    return authHeader.substring(7);
  }
  
  // Check X-API-Key header
  const apiKeyHeader = req.headers['x-api-key'];
  if (apiKeyHeader && typeof apiKeyHeader === 'string') {
    return apiKeyHeader;
  }
  
  // Check query parameter (less secure, use only for specific endpoints)
  if (req.query.api_key && typeof req.query.api_key === 'string') {
    return req.query.api_key;
  }
  
  return null;
}

// Revoke API key
export async function revokeApiKey(userId: string, keyId: string): Promise<void> {
  await prisma.$executeRaw`
    UPDATE api_keys
    SET is_active = false, revoked_at = ${new Date()}
    WHERE id = ${keyId} AND user_id = ${userId}
  `;
  
  await createAuditLog({
    action: 'API_KEY_REVOKED',
    userId,
    resourceType: 'api_key',
    resourceId: keyId
  });
}

// List user's API keys
export async function listApiKeys(userId: string): Promise<any[]> {
  return prisma.$queryRaw`
    SELECT id, name, scopes, created_at, last_used_at, expires_at, is_active
    FROM api_keys
    WHERE user_id = ${userId}
    ORDER BY created_at DESC
  `;
}