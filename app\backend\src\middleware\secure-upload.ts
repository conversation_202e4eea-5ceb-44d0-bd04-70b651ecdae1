import multer from 'multer';
import path from 'path';
import crypto from 'crypto';
import { Request, Response, NextFunction } from 'express';
import { AppError } from './errorHandler';
import { validateFilePath } from '../security/validators';
import { createAuditLog, AUDIT_ACTIONS } from '../security/audit';
import { config } from '../config';
import { logger } from '../utils/logger';

// Secure file storage configuration
const storage = multer.diskStorage({
  destination: async (_req, file, cb) => {
    // Different directories for different file types
    const type = getFileType(file.mimetype);
    const uploadDir = path.join(config.upload.dir, type);
    
    // Ensure directory exists
    const fs = require('fs').promises;
    await fs.mkdir(uploadDir, { recursive: true });
    
    cb(null, uploadDir);
  },
  filename: (_req, file, cb) => {
    // Generate secure filename
    const uniqueSuffix = crypto.randomBytes(16).toString('hex');
    const ext = path.extname(file.originalname).toLowerCase();
    const safeBasename = path.basename(file.originalname, ext)
      .replace(/[^a-zA-Z0-9-_]/g, '')
      .substring(0, 50);
    
    cb(null, `${safeBasename}-${uniqueSuffix}${ext}`);
  }
});

// File type categorization
function getFileType(mimetype: string): string {
  if (mimetype.startsWith('image/')) return 'images';
  if (mimetype === 'application/pdf') return 'documents';
  if (mimetype.includes('spreadsheet') || mimetype === 'text/csv') return 'spreadsheets';
  return 'misc';
}

// File filter with security checks
const fileFilter = (_req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  // Check file extension
  const ext = path.extname(file.originalname).toLowerCase();
  const allowedExtensions = [
    '.jpg', '.jpeg', '.png', '.gif', '.pdf',
    '.doc', '.docx', '.xls', '.xlsx', '.csv'
  ];
  
  if (!allowedExtensions.includes(ext)) {
    return cb(new Error('Invalid file extension'));
  }
  
  // Check MIME type
  const allowedMimeTypes = [
    'image/jpeg', 'image/png', 'image/gif',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/csv'
  ];
  
  if (!allowedMimeTypes.includes(file.mimetype)) {
    return cb(new Error('Invalid file type'));
  }
  
  // Check for path traversal
  if (!validateFilePath(file.originalname)) {
    return cb(new Error('Invalid file name'));
  }
  
  // Check for double extensions
  const parts = file.originalname.split('.');
  if (parts.length > 2) {
    // Allow only specific double extensions
    const validDoubleExtensions = ['.tar.gz', '.tar.bz2'];
    const lastTwo = `.${parts[parts.length - 2]}.${parts[parts.length - 1]}`;
    if (!validDoubleExtensions.includes(lastTwo)) {
      return cb(new Error('Multiple file extensions not allowed'));
    }
  }
  
  cb(null, true);
};

// Create multer instance
export const secureUpload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: config.upload.maxFileSize, // 10MB default
    files: 5, // Max 5 files per request
    fieldSize: 1024 * 1024, // 1MB max field size
    fields: 20, // Max 20 fields
    parts: 100, // Max 100 parts (fields + files)
  }
});

// Virus scanning middleware (placeholder - integrate with actual AV service)
export async function scanFile(filePath: string): Promise<boolean> {
  // In production, integrate with ClamAV or similar
  // For now, perform basic checks
  const fs = require('fs').promises;
  const stats = await fs.stat(filePath);
  
  // Check for suspicious file sizes
  if (stats.size === 0) {
    throw new Error('Empty file detected');
  }
  
  // Check file signature (magic numbers)
  const fd = await fs.open(filePath, 'r');
  const buffer = Buffer.alloc(8);
  await fd.read(buffer, 0, 8, 0);
  await fd.close();
  
  const signatures = {
    'ffd8ff': 'jpeg',
    '89504e47': 'png',
    '47494638': 'gif',
    '25504446': 'pdf',
    '504b0304': 'zip/docx/xlsx',
    'd0cf11e0': 'doc/xls'
  };
  
  const hexSignature = buffer.toString('hex', 0, 4);
  const expectedType = signatures[hexSignature as keyof typeof signatures];
  
  if (!expectedType) {
    throw new Error('Unknown file signature');
  }
  
  return true;
}

// Post-upload security middleware
export function postUploadSecurity() {
  return async (req: Request & { user?: any }, _res: Response, next: NextFunction) => {
    if (!req.file && !req.files) {
      return next();
    }
    
    const files = req.file ? [req.file] : Object.values(req.files || {}).flat();
    
    try {
      for (const file of files as Express.Multer.File[]) {
        // Scan file for viruses
        try {
          await scanFile(file.path);
        } catch (error) {
          // Delete infected/suspicious file
          const fs = require('fs').promises;
          await fs.unlink(file.path);
          
          // Log security event
          await createAuditLog({
            action: AUDIT_ACTIONS.SUSPICIOUS_ACTIVITY,
            userId: req.user?.userId,
            resourceType: 'file_upload',
            details: {
              filename: file.originalname,
              reason: error instanceof Error ? error.message : 'Unknown error',
              mimetype: file.mimetype,
              size: file.size
            },
            ipAddress: req.ip
          });
          
          throw new AppError(400, 'File failed security scan', true, 'FILE_SECURITY_FAILED');
        }
        
        // Log successful upload
        await createAuditLog({
          action: 'FILE_UPLOADED',
          userId: req.user?.userId,
          resourceType: 'file',
          resourceId: file.filename,
          details: {
            originalName: file.originalname,
            mimetype: file.mimetype,
            size: file.size,
            destination: file.destination
          },
          ipAddress: req.ip
        });
      }
      
      next();
    } catch (error) {
      next(error);
    }
  };
}

// Secure file download middleware
export function secureDownload() {
  return async (req: Request & { user?: any }, res: Response, next: NextFunction) => {
    const { fileId } = req.params;
    
    try {
      // Validate file access permissions
      // This should check if user has permission to download the file
      
      // Log file access
      await createAuditLog({
        action: 'FILE_DOWNLOADED',
        userId: req.user?.userId,
        resourceType: 'file',
        resourceId: fileId,
        ipAddress: req.ip
      });
      
      // Set security headers
      res.setHeader('X-Content-Type-Options', 'nosniff');
      res.setHeader('X-Frame-Options', 'DENY');
      res.setHeader('Content-Security-Policy', "default-src 'none'");
      res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, private');
      
      next();
    } catch (error) {
      next(error);
    }
  };
}

// Clean up old uploads
export async function cleanupOldUploads(): Promise<void> {
  const fs = require('fs').promises;
  const uploadDir = config.upload.dir;
  const maxAge = 30 * 24 * 60 * 60 * 1000; // 30 days
  
  async function cleanDirectory(dir: string) {
    const files = await fs.readdir(dir, { withFileTypes: true });
    
    for (const file of files) {
      const filePath = path.join(dir, file.name);
      
      if (file.isDirectory()) {
        await cleanDirectory(filePath);
      } else {
        const stats = await fs.stat(filePath);
        const age = Date.now() - stats.mtime.getTime();
        
        if (age > maxAge) {
          await fs.unlink(filePath);
          logger.info('Deleted old upload', {
            filePath,
            age: Math.floor(age / (24 * 60 * 60 * 1000)) + ' days'
          });
        }
      }
    }
  }
  
  try {
    await cleanDirectory(uploadDir);
  } catch (error) {
    logger.error('Error cleaning up uploads', {
      error: error instanceof Error ? error.message : error
    });
  }
}

// Schedule cleanup
setInterval(cleanupOldUploads, 24 * 60 * 60 * 1000); // Run daily