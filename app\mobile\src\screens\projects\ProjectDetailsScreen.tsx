import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
} from 'react-native';
import { RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

type RootStackParamList = {
  ProjectDetails: { projectId: string };
};

type ProjectDetailsScreenRouteProp = RouteProp<RootStackParamList, 'ProjectDetails'>;
type ProjectDetailsScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  'ProjectDetails'
>;

interface Props {
  route: ProjectDetailsScreenRouteProp;
  navigation: ProjectDetailsScreenNavigationProp;
}

const ProjectDetailsScreen: React.FC<Props> = ({ route, navigation }) => {
  const { projectId } = route.params || { projectId: 'unknown' };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentInsetAdjustmentBehavior="automatic">
        <View style={styles.content}>
          <Text style={styles.title}>Project Details</Text>
          <Text style={styles.subtitle}>Project ID: {projectId}</Text>
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Project Information</Text>
            <Text style={styles.placeholder}>
              Project details will be displayed here
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 20,
  },
  section: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
    color: '#333',
  },
  placeholder: {
    fontSize: 14,
    color: '#999',
    fontStyle: 'italic',
  },
});

export default ProjectDetailsScreen;