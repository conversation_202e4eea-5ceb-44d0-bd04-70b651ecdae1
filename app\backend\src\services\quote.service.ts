import { prisma } from '../database/prisma';
import { AppError } from '../utils/errors';
import { logger } from '../utils/logger';
import { generateQuoteNumber } from '../utils/generators';
import { MaterialPriceLookupService } from './material-price-lookup.service';
import { PdfGenerationService } from './pdf-generation.service';
import { EmailService } from './email.service';
import { redis } from '../services/redis-manager';
import { v4 as uuidv4 } from 'uuid';
import crypto from 'crypto';

interface QuoteFilters {
  status?: string;
  customerId?: string;
  projectId?: string;
  search?: string;
  startDate?: string;
  endDate?: string;
  companyId: string;
}

interface PaginationOptions {
  page: number;
  limit: number;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

interface QuoteItem {
  name: string;
  description?: string;
  category?: string;
  quantity: number;
  unit: string;
  unitPrice?: number;
  totalPrice?: number;
  cost?: number;
  margin?: number;
  materialId?: string;
  sku?: string;
  manufacturer?: string;
  imageUrl?: string;
  sourceUrl?: string;
  lookupStatus?: string;
  lookupResults?: any[];
  priceHistory?: any[];
  notes?: string;
  isOptional?: boolean;
  groupId?: string;
}

export class QuoteService {
  private materialPriceLookup: MaterialPriceLookupService;
  private pdfService: PdfGenerationService;
  private emailService: EmailService;

  constructor() {
    this.materialPriceLookup = new MaterialPriceLookupService();
    this.pdfService = new PdfGenerationService();
    this.emailService = new EmailService();
  }

  async listQuotes(filters: QuoteFilters, pagination: PaginationOptions) {
    const where: any = {
      company_id: filters.companyId
    };

    if (filters.status) {
      where.status = filters.status;
    }

    if (filters.customerId) {
      where.customer_id = filters.customerId;
    }

    if (filters.projectId) {
      where.project_id = filters.projectId;
    }

    if (filters.search) {
      where.OR = [
        { name: { contains: filters.search, mode: 'insensitive' } },
        { quote_number: { contains: filters.search, mode: 'insensitive' } },
        { project_overview: { contains: filters.search, mode: 'insensitive' } }
      ];
    }

    if (filters.startDate || filters.endDate) {
      where.created_at = {};
      if (filters.startDate) {
        where.created_at.gte = new Date(filters.startDate);
      }
      if (filters.endDate) {
        where.created_at.lte = new Date(filters.endDate);
      }
    }

    const skip = (pagination.page - 1) * pagination.limit;

    const [quotes, total] = await Promise.all([
      prisma.quote.findMany({
        where,
        skip,
        take: pagination.limit,
        orderBy: {
          [pagination.sortBy]: pagination.sortOrder
        },
        include: {
          customer: true,
          project: true,
          created_by: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      }),
      prisma.quote.count({ where })
    ]);

    // Parse items JSON for each quote
    const quotesWithParsedItems = quotes.map(quote => ({
      ...quote,
      items: JSON.parse(quote.items || '[]'),
      ai_generation_data: quote.ai_generation_data ? JSON.parse(quote.ai_generation_data) : null,
      customer_signature: quote.customer_signature ? JSON.parse(quote.customer_signature) : null
    }));

    return {
      quotes: quotesWithParsedItems,
      pagination: {
        page: pagination.page,
        limit: pagination.limit,
        total,
        totalPages: Math.ceil(total / pagination.limit)
      }
    };
  }

  async getQuoteById(id: string, companyId: string) {
    const quote = await prisma.quote.findFirst({
      where: {
        id,
        company_id: companyId
      },
      include: {
        customer: true,
        project: true,
        company: true,
        created_by: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        updated_by: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        parent_quote: true,
        child_quotes: true,
        pricing_logs: {
          orderBy: {
            created_at: 'desc'
          },
          take: 10
        }
      }
    });

    if (!quote) {
      throw new AppError('Quote not found', 404);
    }

    return {
      ...quote,
      items: JSON.parse(quote.items || '[]'),
      ai_generation_data: quote.ai_generation_data ? JSON.parse(quote.ai_generation_data) : null,
      customer_signature: quote.customer_signature ? JSON.parse(quote.customer_signature) : null
    };
  }

  async createQuote(data: any) {
    const quoteNumber = await generateQuoteNumber();

    // Calculate totals from items
    const items = data.items || [];
    const subtotal = this.calculateSubtotal(items);
    const taxAmount = subtotal * (data.taxRate || 0);
    const discountAmount = this.calculateDiscount(subtotal, data.discount || 0, data.discountType || 'PERCENTAGE');
    const total = subtotal + taxAmount - discountAmount;

    const quote = await prisma.quote.create({
      data: {
        quote_number: quoteNumber,
        name: data.name,
        customer_id: data.customerId,
        project_id: data.projectId,
        company_id: data.companyId,
        project_overview: data.projectOverview,
        scope_of_work: data.scopeOfWork,
        materials_included: data.materialsIncluded,
        exclusions: data.exclusions,
        terms_and_conditions: data.termsAndConditions,
        items: JSON.stringify(items),
        subtotal,
        tax_rate: data.taxRate || 0,
        tax_amount: taxAmount,
        discount: data.discount || 0,
        discount_type: data.discountType || 'PERCENTAGE',
        total,
        expires_at: data.expiresAt ? new Date(data.expiresAt) : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days default
        customer_notes: data.customerNotes,
        internal_notes: data.internalNotes,
        created_by_id: data.createdById
      },
      include: {
        customer: data.customerId ? true : false,
        project: data.projectId ? true : false,
        created_by: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    // Initiate price lookups for items without prices
    const itemsNeedingPrices = items.filter((item: QuoteItem) => 
      !item.unitPrice && item.lookupStatus !== 'manual'
    );
    
    if (itemsNeedingPrices.length > 0) {
      await this.materialPriceLookup.initiateBulkLookup(
        quote.id,
        itemsNeedingPrices,
        data.companyId
      );
    }

    logger.info(`Quote created: ${quote.quote_number}`, { 
      quoteId: quote.id, 
      userId: data.createdById 
    });

    return {
      ...quote,
      items: JSON.parse(quote.items)
    };
  }

  async updateQuote(id: string, data: any, companyId: string) {
    const existingQuote = await this.getQuoteById(id, companyId);

    if (existingQuote.status !== 'DRAFT' && !data.forceUpdate) {
      throw new AppError('Cannot update quote that is not in DRAFT status', 400);
    }

    // Recalculate totals if items changed
    let updateData: any = { ...data };
    if (data.items) {
      const subtotal = this.calculateSubtotal(data.items);
      const taxAmount = subtotal * (data.taxRate || existingQuote.tax_rate);
      const discountAmount = this.calculateDiscount(
        subtotal, 
        data.discount || existingQuote.discount, 
        data.discountType || existingQuote.discount_type
      );
      const total = subtotal + taxAmount - discountAmount;

      updateData = {
        ...updateData,
        items: JSON.stringify(data.items),
        subtotal,
        tax_amount: taxAmount,
        total
      };
    }

    const updatedQuote = await prisma.quote.update({
      where: { id },
      data: {
        ...updateData,
        updated_by_id: data.updatedById,
        updated_at: new Date()
      },
      include: {
        customer: true,
        project: true,
        created_by: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    return {
      ...updatedQuote,
      items: JSON.parse(updatedQuote.items)
    };
  }

  async deleteQuote(id: string, companyId: string) {
    const quote = await this.getQuoteById(id, companyId);

    if (quote.status !== 'DRAFT') {
      throw new AppError('Cannot delete quote that is not in DRAFT status', 400);
    }

    // Hard delete since Quote model doesn't have deleted_at field
    await prisma.quote.delete({
      where: { id }
    });

    logger.info(`Quote deleted: ${quote.quote_number}`, { quoteId: id });
  }

  async duplicateQuote(id: string, name: string, userId: string, companyId: string) {
    const originalQuote = await this.getQuoteById(id, companyId);
    const quoteNumber = await generateQuoteNumber();

    const newQuote = await prisma.quote.create({
      data: {
        quote_number: quoteNumber,
        name: name || `${originalQuote.name} (Copy)`,
        customer_id: originalQuote.customer_id,
        project_id: originalQuote.project_id,
        company_id: companyId,
        project_overview: originalQuote.project_overview,
        scope_of_work: originalQuote.scope_of_work,
        materials_included: originalQuote.materials_included,
        exclusions: originalQuote.exclusions,
        terms_and_conditions: originalQuote.terms_and_conditions,
        items: originalQuote.items,
        subtotal: originalQuote.subtotal,
        tax_rate: originalQuote.tax_rate,
        tax_amount: originalQuote.tax_amount,
        discount: originalQuote.discount,
        discount_type: originalQuote.discount_type,
        total: originalQuote.total,
        expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        created_by_id: userId,
        parent_quote_id: originalQuote.parent_quote_id || id,
        version: (originalQuote.version || 1) + 1
      }
    });

    logger.info(`Quote duplicated: ${originalQuote.quote_number} -> ${newQuote.quote_number}`, {
      originalId: id,
      newId: newQuote.id,
      userId
    });

    return newQuote;
  }

  async convertToJob(quoteId: string, jobData: any, companyId: string) {
    const quote = await this.getQuoteById(quoteId, companyId);

    if (quote.status !== 'APPROVED') {
      throw new AppError('Quote must be approved before converting to job', 400);
    }

    const job = await prisma.job.create({
      data: {
        project_id: quote.project_id,
        quote_id: quoteId,
        name: jobData.name,
        description: `Job created from quote ${quote.quote_number}`,
        scheduled_start: new Date(jobData.scheduledStart),
        scheduled_end: new Date(jobData.scheduledEnd),
        assigned_to: jobData.assignedTo,
        status: 'SCHEDULED'
      }
    });

    // Update quote status
    await prisma.quote.update({
      where: { id: quoteId },
      data: {
        status: 'CONVERTED',
        converted_to_job_id: job.id
      }
    });

    logger.info(`Quote converted to job: ${quote.quote_number}`, {
      quoteId,
      jobId: job.id
    });

    return job;
  }

  async convertToEstimate(quoteId: string, userId: string, companyId: string) {
    const quote = await this.getQuoteById(quoteId, companyId);
    
    // Parse items and convert to estimate format
    const items = JSON.parse(quote.items || '[]');
    
    const estimate = await prisma.estimate.create({
      data: {
        project_id: quote.project_id,
        status: 'DRAFT',
        valid_until: quote.expires_at || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        subtotal: quote.subtotal,
        tax_total: quote.tax_amount,
        total_amount: quote.total,
        notes: quote.customer_notes,
        terms: quote.terms_and_conditions,
        created_by: userId
      }
    });

    // Create material items from quote items
    for (const item of items) {
      if (item.category === 'material' || !item.category) {
        await prisma.materialItem.create({
          data: {
            estimate_id: estimate.id,
            catalog_number: item.sku || item.materialId || 'CUSTOM',
            description: item.description || item.name,
            category: item.category || 'GENERAL',
            unit: item.unit,
            quantity: item.quantity,
            unit_cost: item.cost || item.unitPrice || 0,
            tax_rate: quote.tax_rate,
            extended_cost: item.totalPrice || (item.quantity * (item.unitPrice || 0)),
            total_amount: item.totalPrice || (item.quantity * (item.unitPrice || 0))
          }
        });
      }
    }

    // Update quote
    await prisma.quote.update({
      where: { id: quoteId },
      data: {
        converted_to_estimate_id: estimate.id
      }
    });

    logger.info(`Quote converted to estimate: ${quote.quote_number}`, {
      quoteId,
      estimateId: estimate.id,
      userId
    });

    return estimate;
  }

  async sendQuote(quoteId: string, emailData: any, companyId: string) {
    const quote = await this.getQuoteById(quoteId, companyId);
    
    // Generate PDF
    const pdfBuffer = await this.pdfService.generateQuotePDF(quote);
    
    // Generate public access token
    const token = crypto.randomBytes(32).toString('hex');
    const publicUrl = `${process.env.FRONTEND_URL}/quotes/public/${token}`;
    
    // Store token in Redis with 30 day expiry
    if (redis) {
      await redis.setex(`quote:public:${token}`, 30 * 24 * 60 * 60, quoteId);
    }
    
    // Send email
    await this.emailService.sendQuoteEmail({
      to: emailData.to,
      cc: emailData.cc,
      subject: emailData.subject || `Quote ${quote.quote_number} - ${quote.name}`,
      message: emailData.message,
      publicUrl,
      pdfBuffer,
      quote
    });
    
    // Update quote status
    await prisma.quote.update({
      where: { id: quoteId },
      data: {
        status: 'SENT',
        sent_at: new Date()
      }
    });
    
    logger.info(`Quote sent: ${quote.quote_number}`, {
      quoteId,
      to: emailData.to
    });
    
    return {
      success: true,
      publicUrl
    };
  }

  async generatePDF(quoteId: string, companyId: string) {
    const quote = await this.getQuoteById(quoteId, companyId);
    return this.pdfService.generateQuotePDF(quote);
  }

  async approveQuote(quoteId: string, token: string, signature: any) {
    // Verify token
    const storedQuoteId = redis ? await redis.get(`quote:public:${token}`) : null;
    if (!storedQuoteId || storedQuoteId !== quoteId) {
      throw new AppError('Invalid or expired token', 401);
    }

    const quote = await prisma.quote.findUnique({
      where: { id: quoteId }
    });

    if (!quote) {
      throw new AppError('Quote not found', 404);
    }

    if (quote.status !== 'SENT' && quote.status !== 'VIEWED') {
      throw new AppError('Quote cannot be approved in current status', 400);
    }

    await prisma.quote.update({
      where: { id: quoteId },
      data: {
        status: 'APPROVED',
        approved_at: new Date(),
        customer_signature: JSON.stringify({
          ...signature,
          signedAt: new Date(),
          ipAddress: signature.ipAddress
        })
      }
    });

    logger.info(`Quote approved: ${quote.quote_number}`, { quoteId });

    return { success: true };
  }

  async rejectQuote(quoteId: string, token: string, reason: string) {
    // Verify token
    const storedQuoteId = redis ? await redis.get(`quote:public:${token}`) : null;
    if (!storedQuoteId || storedQuoteId !== quoteId) {
      throw new AppError('Invalid or expired token', 401);
    }

    const quote = await prisma.quote.findUnique({
      where: { id: quoteId }
    });

    if (!quote) {
      throw new AppError('Quote not found', 404);
    }

    if (quote.status !== 'SENT' && quote.status !== 'VIEWED') {
      throw new AppError('Quote cannot be rejected in current status', 400);
    }

    await prisma.quote.update({
      where: { id: quoteId },
      data: {
        status: 'REJECTED',
        rejected_at: new Date(),
        customer_notes: reason
      }
    });

    logger.info(`Quote rejected: ${quote.quote_number}`, { quoteId, reason });

    return { success: true };
  }

  async getPublicQuote(token: string) {
    const quoteId = redis ? await redis.get(`quote:public:${token}`) : null;
    if (!quoteId) {
      throw new AppError('Invalid or expired token', 401);
    }

    const quote = await prisma.quote.findUnique({
      where: { id: quoteId },
      include: {
        customer: true,
        project: true,
        company: true
      }
    });

    if (!quote) {
      throw new AppError('Quote not found', 404);
    }

    // Mark as viewed if first time
    if (quote.status === 'SENT') {
      await prisma.quote.update({
        where: { id: quoteId },
        data: {
          status: 'VIEWED',
          viewed_at: new Date()
        }
      });
    }

    // Return sanitized quote data
    return {
      id: quote.id,
      quote_number: quote.quote_number,
      name: quote.name,
      customer: {
        name: quote.customer.name,
        email: quote.customer.email,
        phone: quote.customer.phone
      },
      project: {
        name: quote.project.name,
        address: quote.project.address,
        city: quote.project.city,
        state: quote.project.state,
        zip: quote.project.zip
      },
      company: {
        name: quote.company.name,
        email: quote.company.email,
        phone: quote.company.phone,
        license: quote.company.license,
        logo_url: quote.company.logo_url
      },
      project_overview: quote.project_overview,
      scope_of_work: quote.scope_of_work,
      materials_included: quote.materials_included,
      exclusions: quote.exclusions,
      terms_and_conditions: quote.terms_and_conditions,
      items: JSON.parse(quote.items || '[]'),
      subtotal: quote.subtotal,
      tax_rate: quote.tax_rate,
      tax_amount: quote.tax_amount,
      discount: quote.discount,
      discount_type: quote.discount_type,
      total: quote.total,
      status: quote.status,
      expires_at: quote.expires_at,
      created_at: quote.created_at
    };
  }

  async getPriceStatus(quoteId: string, companyId: string) {
    const quote = await this.getQuoteById(quoteId, companyId);
    const items = JSON.parse(quote.items || '[]');

    const summary = {
      totalItems: items.length,
      itemsWithPrice: 0,
      itemsPending: 0,
      itemsFailed: 0,
      itemsManual: 0,
      lastUpdated: null as Date | null
    };

    items.forEach((item: QuoteItem) => {
      if (item.unitPrice && item.unitPrice > 0) {
        summary.itemsWithPrice++;
      } else if (item.lookupStatus === 'searching' || item.lookupStatus === 'pending') {
        summary.itemsPending++;
      } else if (item.lookupStatus === 'failed') {
        summary.itemsFailed++;
      } else if (item.lookupStatus === 'manual') {
        summary.itemsManual++;
      }
    });

    // Get last price update from pricing logs
    const lastLog = await prisma.priceScrapingLog.findFirst({
      where: { quote_id: quoteId },
      orderBy: { created_at: 'desc' }
    });

    if (lastLog) {
      summary.lastUpdated = lastLog.created_at;
    }

    return {
      summary,
      items: items.map((item: QuoteItem, index: number) => ({
        id: index.toString(),
        name: item.name,
        quantity: item.quantity,
        unit: item.unit,
        unitPrice: item.unitPrice,
        totalPrice: item.totalPrice,
        lookupStatus: item.lookupStatus || 'pending',
        lastUpdated: item.priceHistory?.[0]?.timestamp
      }))
    };
  }

  async refreshPrices(quoteId: string, force: boolean, companyId: string) {
    const quote = await this.getQuoteById(quoteId, companyId);
    const items = JSON.parse(quote.items || '[]');

    const itemsToRefresh = items.filter((item: QuoteItem) => 
      force || !item.unitPrice || item.lookupStatus === 'failed'
    );

    if (itemsToRefresh.length === 0) {
      return {
        message: 'No items need price refresh',
        itemsQueued: 0
      };
    }

    await this.materialPriceLookup.initiateBulkLookup(
      quoteId,
      itemsToRefresh,
      companyId
    );

    return {
      message: 'Price refresh initiated',
      itemsQueued: itemsToRefresh.length
    };
  }

  async updateItemPrice(quoteId: string, itemId: string, selectedOption: any, companyId: string) {
    const quote = await this.getQuoteById(quoteId, companyId);
    const items = JSON.parse(quote.items || '[]');

    const itemIndex = parseInt(itemId);
    if (itemIndex < 0 || itemIndex >= items.length) {
      throw new AppError('Invalid item ID', 400);
    }

    // Update item with selected price
    items[itemIndex] = {
      ...items[itemIndex],
      unitPrice: selectedOption.price,
      totalPrice: selectedOption.price * items[itemIndex].quantity,
      sku: selectedOption.sku,
      sourceUrl: selectedOption.url,
      imageUrl: selectedOption.imageUrl,
      lookupStatus: 'confirmed',
      priceHistory: [
        ...(items[itemIndex].priceHistory || []),
        {
          price: selectedOption.price,
          source: selectedOption.source,
          timestamp: new Date()
        }
      ]
    };

    // Recalculate totals
    const subtotal = this.calculateSubtotal(items);
    const taxAmount = subtotal * quote.tax_rate;
    const discountAmount = this.calculateDiscount(subtotal, quote.discount, quote.discount_type);
    const total = subtotal + taxAmount - discountAmount;

    // Update quote
    await prisma.quote.update({
      where: { id: quoteId },
      data: {
        items: JSON.stringify(items),
        subtotal,
        tax_amount: taxAmount,
        total
      }
    });

    // Log price update
    await prisma.priceScrapingLog.create({
      data: {
        request_id: uuidv4(),
        quote_id: quoteId,
        item_id: itemId,
        search_query: items[itemIndex].name,
        source: selectedOption.source,
        status: 'SUCCESS',
        results: JSON.stringify([selectedOption]),
        created_at: new Date()
      }
    });

    return {
      success: true,
      item: items[itemIndex],
      totals: {
        subtotal,
        taxAmount,
        discount: discountAmount,
        total
      }
    };
  }

  async searchMaterials(query: string, options: any) {
    return this.materialPriceLookup.searchMaterials(query, options);
  }

  async getPriceHistory(params: any) {
    const where: any = {
      company_id: params.companyId
    };

    if (params.materialId) {
      where.material_id = params.materialId;
    }

    if (params.sku) {
      where.sku = params.sku;
    }

    const sinceDate = new Date();
    sinceDate.setDate(sinceDate.getDate() - params.days);
    where.scraped_at = { gte: sinceDate };

    const history = await prisma.quoteMaterialPriceHistory.findMany({
      where,
      orderBy: { scraped_at: 'asc' },
      select: {
        price: true,
        source: true,
        scraped_at: true,
        unit: true
      }
    });

    // Group by source
    const bySource: Record<string, any[]> = {};
    history.forEach(record => {
      if (!bySource[record.source]) {
        bySource[record.source] = [];
      }
      bySource[record.source].push({
        price: record.price,
        date: record.scraped_at,
        unit: record.unit
      });
    });

    return {
      history: bySource,
      summary: {
        averagePrice: history.length > 0 
          ? history.reduce((sum, r) => sum + r.price, 0) / history.length 
          : 0,
        minPrice: history.length > 0 
          ? Math.min(...history.map(r => r.price)) 
          : 0,
        maxPrice: history.length > 0 
          ? Math.max(...history.map(r => r.price)) 
          : 0,
        priceChange: history.length > 1 
          ? ((history[history.length - 1].price - history[0].price) / history[0].price) * 100 
          : 0
      }
    };
  }

  private calculateSubtotal(items: QuoteItem[]): number {
    return items.reduce((sum, item) => {
      const itemTotal = item.totalPrice || (item.quantity * (item.unitPrice || 0));
      return sum + itemTotal;
    }, 0);
  }

  private calculateDiscount(subtotal: number, discount: number, discountType: string): number {
    if (discountType === 'PERCENTAGE') {
      return subtotal * (discount / 100);
    } else {
      return discount;
    }
  }
}