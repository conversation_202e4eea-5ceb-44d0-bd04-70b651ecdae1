import { Page } from 'playwright';
import { BaseScraper, SearchResult } from './base.scraper';
import { logger } from '../utils/logger';

export class HomeDepotScraper extends BaseScraper {
  protected searchUrl(query: string): string {
    const encoded = encodeURIComponent(query);
    return `https://www.homedepot.com/s/${encoded}`;
  }

  protected async waitForResults(page: Page) {
    try {
      // Wait for product grid to load
      await page.waitForSelector('[data-testid="product-pod"]', {
        timeout: 10000,
        state: 'visible'
      });
    } catch (error) {
      // Try alternative selectors
      await page.waitForSelector('.product-pod, [class*="product-item"]', {
        timeout: 10000,
        state: 'visible'
      });
    }
  }

  protected async extractResults(page: Page): Promise<SearchResult[]> {
    const results: SearchResult[] = [];

    try {
      // Take screenshot for debugging
      await this.takeScreenshot(page, 'home-depot-search');

      // Extract product information
      const products = await page.evaluate(() => {
        const items: any[] = [];
        
        // Try multiple selectors for products
        const productElements = document.querySelectorAll(
          '[data-testid="product-pod"], .product-pod, [class*="product-item"]'
        );

        productElements.forEach((element) => {
          try {
            // Title
            const titleElement = element.querySelector(
              '[data-testid="product-title"] a, ' +
              '[class*="product-title"] a, ' +
              '.product-pod__title a, ' +
              'h3 a, h2 a'
            );
            const title = titleElement?.textContent?.trim() || '';
            const url = titleElement?.getAttribute('href') || '';

            // Price
            const priceElement = element.querySelector(
              '[data-testid="product-price"] span, ' +
              '[class*="price-format__main-price"], ' +
              '.price-format__large--main-price span, ' +
              '[class*="price__dollars"]'
            );
            const priceText = priceElement?.textContent?.trim() || '';

            // SKU/Model
            const skuElement = element.querySelector(
              '[data-testid="product-sku"], ' +
              '[data-testid="product-model"], ' +
              '[class*="product-identifier"]'
            );
            const sku = skuElement?.textContent?.trim().replace(/[^\d]/g, '') || '';

            // Image
            const imageElement = element.querySelector('img');
            const imageUrl = imageElement?.getAttribute('src') || '';

            // Manufacturer/Brand
            const brandElement = element.querySelector(
              '[data-testid="product-brand"], ' +
              '[class*="product-brand"], ' +
              '.product-pod__brand'
            );
            const manufacturer = brandElement?.textContent?.trim() || '';

            // Availability
            const availabilityElement = element.querySelector(
              '[data-testid="product-availability"], ' +
              '[class*="availability"], ' +
              '.product-pod__availability'
            );
            const availability = availabilityElement?.textContent?.trim() || '';

            // Rating
            const ratingElement = element.querySelector(
              '[data-testid="product-rating"], ' +
              '[class*="rating"], ' +
              '.star-rating'
            );
            const ratingText = ratingElement?.getAttribute('aria-label') || '';
            const ratingMatch = ratingText.match(/(\d+\.?\d*)/);
            const rating = ratingMatch ? parseFloat(ratingMatch[1]) : undefined;

            if (title && priceText) {
              items.push({
                title,
                priceText,
                url: url.startsWith('http') ? url : `https://www.homedepot.com${url}`,
                sku,
                imageUrl,
                manufacturer,
                availability,
                rating
              });
            }
          } catch (err) {
            console.error('Error extracting product:', err);
          }
        });

        return items;
      });

      // Process extracted data
      for (const product of products) {
        const result: SearchResult = {
          title: product.title,
          price: this.parsePrice(product.priceText),
          url: product.url,
          sku: product.sku,
          imageUrl: product.imageUrl,
          manufacturer: product.manufacturer,
          availability: product.availability,
          rating: product.rating,
          unit: this.extractUnit(product.title, product.priceText),
          category: 'Electrical',
          confidence: 0.9
        };

        results.push(result);
      }

      logger.info(`Extracted ${results.length} results from Home Depot`);
    } catch (error) {
      logger.error('Error extracting Home Depot results:', error);
      await this.takeScreenshot(page, 'home-depot-error');
    }

    return results;
  }

  protected async extractProductDetails(page: Page, url: string): Promise<SearchResult> {
    try {
      // Wait for product details to load
      await page.waitForSelector('[data-testid="product-title"], h1', {
        timeout: 10000
      });

      const details = await page.evaluate(() => {
        // Title
        const titleElement = document.querySelector(
          '[data-testid="product-title"], ' +
          'h1[class*="product-title"], ' +
          'h1'
        );
        const title = titleElement?.textContent?.trim() || '';

        // Price
        const priceElement = document.querySelector(
          '[data-testid="product-price"], ' +
          '.price-format__main-price, ' +
          '[class*="price-detailed__main-price"]'
        );
        const priceText = priceElement?.textContent?.trim() || '';

        // SKU
        const skuElement = document.querySelector(
          '[data-testid="product-info-model"], ' +
          '[data-testid="product-info-internet"], ' +
          '.product-info-bar__detail:contains("Model"), ' +
          '.product-info-bar__detail:contains("Internet #")'
        );
        const sku = skuElement?.textContent?.trim().replace(/[^\d]/g, '') || '';

        // Description
        const descElement = document.querySelector(
          '[data-testid="product-description"], ' +
          '.product-description__content, ' +
          '#product-overview'
        );
        const description = descElement?.textContent?.trim() || '';

        // Image
        const imageElement = document.querySelector(
          '[data-testid="product-image"] img, ' +
          '.mediagallery__mainimage img'
        );
        const imageUrl = imageElement?.getAttribute('src') || '';

        // Manufacturer
        const brandElement = document.querySelector(
          '[data-testid="product-brand"], ' +
          '.product-details__brand'
        );
        const manufacturer = brandElement?.textContent?.trim() || '';

        // Availability
        const availElement = document.querySelector(
          '[data-testid="product-availability"], ' +
          '.fulfillment__message'
        );
        const availability = availElement?.textContent?.trim() || '';

        // Specifications
        const specs: Record<string, string> = {};
        const specRows = document.querySelectorAll('.specs-table__row, [class*="specification-row"]');
        specRows.forEach(row => {
          const label = row.querySelector('.specs-table__cell:first-child')?.textContent?.trim();
          const value = row.querySelector('.specs-table__cell:last-child')?.textContent?.trim();
          if (label && value) {
            specs[label] = value;
          }
        });

        return {
          title,
          priceText,
          sku,
          description,
          imageUrl,
          manufacturer,
          availability,
          specifications: specs
        };
      });

      return {
        title: details.title,
        price: this.parsePrice(details.priceText),
        url,
        sku: details.sku,
        description: details.description,
        imageUrl: details.imageUrl,
        manufacturer: details.manufacturer,
        availability: details.availability,
        unit: this.extractUnit(details.title, details.priceText),
        category: 'Electrical',
        confidence: 0.95
      };

    } catch (error) {
      logger.error('Error extracting Home Depot product details:', error);
      throw error;
    }
  }

  private extractUnit(title: string, priceText: string): string {
    // Check for common units in title or price
    const text = `${title} ${priceText}`.toLowerCase();
    
    if (text.includes('/ft') || text.includes('per foot')) return 'FT';
    if (text.includes('/in') || text.includes('per inch')) return 'IN';
    if (text.includes('/yd') || text.includes('per yard')) return 'YD';
    if (text.includes('/box')) return 'BOX';
    if (text.includes('/case')) return 'CASE';
    if (text.includes('/roll')) return 'ROLL';
    if (text.includes('/pkg') || text.includes('/pack')) return 'PKG';
    if (text.includes('/pair')) return 'PAIR';
    if (text.includes('/set')) return 'SET';
    
    // Check for quantity indicators
    const qtyMatch = text.match(/\((\d+)[- ]?(pack|pk|count|ct|pc)\)/i);
    if (qtyMatch) return 'PKG';
    
    // Default to each
    return 'EA';
  }
}