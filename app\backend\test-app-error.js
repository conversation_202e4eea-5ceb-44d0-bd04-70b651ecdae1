// Test script to verify AppError import works correctly
const { AppError } = require('./dist/utils/errors');

console.log('Testing AppError import...');
console.log('AppError type:', typeof AppError);
console.log('AppError constructor:', AppError);

try {
  throw new AppError('Test error', 400);
} catch (error) {
  console.log('Error caught successfully:');
  console.log('- Name:', error.name);
  console.log('- Message:', error.message);
  console.log('- Status Code:', error.statusCode);
  console.log('- Is instance of AppError:', error instanceof AppError);
  console.log('- Is instance of Error:', error instanceof Error);
}

console.log('Test completed successfully!');