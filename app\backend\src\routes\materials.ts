import { Router } from 'express';
import { z } from 'zod';
import { prisma, redis } from '../index';
import { authenticate, AuthRequest } from '../middleware/auth';
import { materialPriceQueue } from '../index';
import { emitPriceUpdate } from '../socket';
import { io } from '../index';
import { CacheService } from '../services/cache.service';

const router: Router = Router();

// Apply authentication to all routes
router.use(authenticate);

// Search materials schema
const searchMaterialsSchema = z.object({
  query: z.string().optional(),
  category: z.enum(['WIRE', 'CONDUIT', 'DEVICES', 'PANELS', 'FIXTURES', 'MISC']).optional(),
  supplier: z.string().optional(),
  page: z.string().transform(Number).default('1'),
  limit: z.string().transform(Number).default('50')
});

// Get materials from price history
router.get('/search', async (req: AuthRequest, res, next) => {
  try {
    const params = searchMaterialsSchema.parse(req.query);
    const skip = (params.page - 1) * params.limit;
    
    // Build where clause
    const where = {
      ...(params.query && {
        OR: [
          { catalog_number: { contains: params.query, mode: 'insensitive' as const } },
          { description: { contains: params.query, mode: 'insensitive' as const } }
        ]
      }),
      ...(params.supplier && { supplier: params.supplier })
    };
    
    // Get unique materials with latest prices - SQLite compatible
    const whereClause = params.query ? {
      OR: [
        { catalog_number: { contains: params.query } },
        { supplier: { contains: params.query } }
      ]
    } : {};
    
    // Get latest price for each catalog number
    // Note: SQLite doesn't support distinct on specific columns, so we'll get all and filter
    const allMaterials = await prisma.materialPriceHistory.findMany({
      where: whereClause,
      orderBy: [
        { catalog_number: 'asc' },
        { effective_date: 'desc' }
      ]
    });
    
    // Filter to get only the latest price for each catalog number
    const seenCatalogNumbers = new Set<string>();
    const materials = allMaterials.filter(material => {
      if (seenCatalogNumbers.has(material.catalog_number)) {
        return false;
      }
      seenCatalogNumbers.add(material.catalog_number);
      return true;
    }).slice(skip, skip + params.limit);
    
    // Get material details from seed data (in real app, this would be a materials table)
    const materialDetails = await getMaterialDetails(materials as any[]);
    
    res.json({
      data: materialDetails,
      pagination: {
        page: params.page,
        limit: params.limit
      }
    });
  } catch (error) {
    next(error);
  }
});

// Get price history for a material
router.get('/:catalogNumber/history', async (req: AuthRequest, res, next) => {
  try {
    const history = await prisma.materialPriceHistory.findMany({
      where: { catalog_number: req.params.catalogNumber },
      orderBy: { effective_date: 'desc' },
      take: 100
    });
    
    res.json(history);
  } catch (error) {
    next(error);
  }
});

// Get current price for materials
router.post('/prices', async (req: AuthRequest, res, next) => {
  try {
    const schema = z.object({
      catalog_numbers: z.array(z.string()).max(100)
    });
    
    const { catalog_numbers } = schema.parse(req.body);
    
    // Check cache first using the optimized batch method
    const cachedPrices = await CacheService.getCachedMaterialPrices(catalog_numbers);
    const uncachedNumbers = catalog_numbers.filter(num => !cachedPrices[num]);
    
    // Get uncached prices from database
    let dbPrices: any[] = [];
    if (uncachedNumbers.length > 0) {
      const allPrices = await prisma.materialPriceHistory.findMany({
        where: {
          catalog_number: { in: uncachedNumbers }
        },
        orderBy: [
          { catalog_number: 'asc' },
          { effective_date: 'desc' }
        ]
      });
      
      // Filter to get only the latest price for each catalog number
      const seenNumbers = new Set<string>();
      dbPrices = allPrices.filter(price => {
        if (seenNumbers.has(price.catalog_number)) {
          return false;
        }
        seenNumbers.add(price.catalog_number);
        return true;
      });
    }
    
    // Cache the results using the optimized batch method
    if (dbPrices.length > 0) {
      await CacheService.cacheMaterialPrices(
        dbPrices.map(price => ({
          catalogNumber: price.catalog_number,
          data: price
        }))
      );
    }
    
    // Combine results
    const allPrices = { ...cachedPrices };
    dbPrices.forEach(price => {
      allPrices[price.catalog_number] = price;
    });
    
    res.json(allPrices);
  } catch (error) {
    next(error);
  }
});

// Request price update (queues background job)
router.post('/update-prices', async (req: AuthRequest, res, next) => {
  try {
    const schema = z.object({
      catalog_numbers: z.array(z.string()).max(50),
      suppliers: z.array(z.string()).optional()
    });
    
    const data = schema.parse(req.body);
    
    // Queue price update job (if Redis available)
    if (materialPriceQueue) {
      await materialPriceQueue.add('update-prices', {
        catalog_numbers: data.catalog_numbers,
        suppliers: data.suppliers || ['Graybar', 'WESCO', 'Platt'],
        requestedBy: req.user?.userId
      }, {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000
        }
      });
    } else {
      return res.status(503).json({
        error: 'Price update service temporarily unavailable'
      });
    }
    
    res.json({
      message: 'Price update queued',
      jobCount: data.catalog_numbers.length
    });
  } catch (error) {
    next(error);
  }
});

// Get suppliers
router.get('/suppliers', async (_req: AuthRequest, res, next) => {
  try {
    const suppliers = await prisma.materialPriceHistory.findMany({
      select: { supplier: true },
      distinct: ['supplier'],
      orderBy: { supplier: 'asc' }
    });
    
    res.json(suppliers.map(s => s.supplier));
  } catch (error) {
    next(error);
  }
});

// Helper function to get material details
async function getMaterialDetails(priceData: any[]): Promise<any[]> {
  // In a real app, this would query a materials table
  // For now, we'll enhance with category based on catalog number patterns
  return priceData.map(item => {
    let category = 'MISC';
    let description = item.catalog_number;
    
    // Determine category from catalog number
    if (item.catalog_number.includes('THHN') || item.catalog_number.includes('NM')) {
      category = 'WIRE';
      if (item.catalog_number.includes('THHN')) {
        const match = item.catalog_number.match(/THHN-(\d+)/);
        if (match) {
          description = `${match[1]} AWG THHN Wire`;
        }
      }
    } else if (item.catalog_number.includes('EMT') || item.catalog_number.includes('PVC')) {
      category = 'CONDUIT';
      const match = item.catalog_number.match(/EMT-(.+?)-/);
      if (match) {
        description = `${match[1]}" EMT Conduit`;
      }
    } else if (item.catalog_number.includes('REC') || item.catalog_number.includes('SW')) {
      category = 'DEVICES';
      if (item.catalog_number.includes('GFCI')) {
        description = 'GFCI Receptacle';
      } else if (item.catalog_number.includes('REC')) {
        description = 'Receptacle';
      } else {
        description = 'Switch';
      }
    } else if (item.catalog_number.includes('PANEL') || item.catalog_number.includes('BRK')) {
      category = 'PANELS';
      if (item.catalog_number.includes('PANEL')) {
        description = 'Load Center Panel';
      } else {
        description = 'Circuit Breaker';
      }
    } else if (item.catalog_number.includes('FIX')) {
      category = 'FIXTURES';
      description = 'Light Fixture';
    }
    
    return {
      ...item,
      category,
      description
    };
  });
}

// Subscribe to real-time price updates via WebSocket
router.post('/subscribe', async (req: AuthRequest, res, next) => {
  try {
    const schema = z.object({
      catalog_numbers: z.array(z.string()).max(100)
    });
    
    const { catalog_numbers } = schema.parse(req.body);
    
    // Store subscription in Redis
    const userId = req.user!.userId;
    await redis.setex(
      `price-sub:${userId}`,
      3600, // 1 hour TTL
      JSON.stringify(catalog_numbers)
    );
    
    res.json({
      message: 'Subscribed to price updates',
      catalog_numbers
    });
  } catch (error) {
    next(error);
  }
});

export { router as materialsRouter };