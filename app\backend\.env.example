# Database
DATABASE_URL=file:./dev.db

# Environment
NODE_ENV=development

# JWT Secrets (generate secure random strings for production)
JWT_SECRET=your_jwt_secret_here_min_32_chars
JWT_REFRESH_SECRET=your_jwt_refresh_secret_here_min_32_chars
SESSION_SECRET=your_session_secret_here

# Redis configuration (optional in development)
# Leave REDIS_HOST empty to run without Redis
# If your Redis requires authentication, set REDIS_PASSWORD
REDIS_HOST=
REDIS_PORT=6379
REDIS_PASSWORD=

# Encryption key for sensitive data (min 32 chars)
ENCRYPTION_KEY=your_encryption_key_here_min_32_chars

# Rate limiting (development defaults - more lenient)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# External APIs (optional)
GEMINI_API_KEY=
CLAUDE_API_KEY=

# File upload settings
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=10485760

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# CORS (comma-separated origins)
CORS_ORIGINS=http://localhost:3000

# Security (optional)
API_SIGNING_SECRET=
USER_SIGNING_SECRET=

# Agent System (optional)
AGENT_REDIS_CHANNEL=electrical:agents
AGENT_MEMORY_TTL=86400
AGENT_MESSAGE_RETENTION=604800

# Neo4j for Memory Agent (optional)
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=electrical123

# ChromaDB for Memory Agent (optional)
CHROMADB_URL=http://localhost:8000

# InfluxDB for metrics (optional)
INFLUXDB_URL=http://localhost:8086
INFLUXDB_TOKEN=
INFLUXDB_ORG=electrical_contracting
INFLUXDB_BUCKET=metrics