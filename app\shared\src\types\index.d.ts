import { z } from 'zod';
export declare const CustomerSchema: any;
export type Customer = z.infer<typeof CustomerSchema>;
export declare const ProjectSchema: any;
export type Project = z.infer<typeof ProjectSchema>;
export declare const MaterialItemSchema: any;
export type MaterialItem = z.infer<typeof MaterialItemSchema>;
export declare const LaborItemSchema: any;
export type LaborItem = z.infer<typeof LaborItemSchema>;
export declare const EstimateSchema: any;
export type Estimate = z.infer<typeof EstimateSchema>;
export declare const LoadCalculationSchema: any;
export type LoadCalculation = z.infer<typeof LoadCalculationSchema>;
export declare const VoltageDropSchema: any;
export type VoltageDrop = z.infer<typeof VoltageDropSchema>;
export declare const MaterialPriceHistorySchema: any;
export type MaterialPriceHistory = z.infer<typeof MaterialPriceHistorySchema>;
export declare const AgentMessageSchema: any;
export type AgentMessage = z.infer<typeof AgentMessageSchema>;
export declare const CalculationLogSchema: any;
export type CalculationLog = z.infer<typeof CalculationLogSchema>;
export declare const PanelSchema: any;
export type Panel = z.infer<typeof PanelSchema>;
export declare const CircuitSchema: any;
export type Circuit = z.infer<typeof CircuitSchema>;
export declare const PanelLoadCalculationSchema: any;
export type PanelLoadCalculation = z.infer<typeof PanelLoadCalculationSchema>;
export declare const UserSchema: any;
export type User = z.infer<typeof UserSchema>;
//# sourceMappingURL=index.d.ts.map