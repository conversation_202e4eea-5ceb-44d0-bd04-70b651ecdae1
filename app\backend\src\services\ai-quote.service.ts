import { GoogleGenerativeAI } from '@google/generative-ai';
import { prisma } from '../database/prisma';
import { AppError } from '../utils/errors';
import { logger } from '../utils/logger';
import { QuoteService } from './quote.service';
import { MaterialPriceLookupService } from './material-price-lookup.service';
import { z } from 'zod';

// AI Response Schemas
const aiQuoteResponseSchema = z.object({
  projectOverview: z.string().optional(),
  scopeOfWork: z.string().optional(),
  materialsIncluded: z.string().optional(),
  exclusions: z.string().optional(),
  items: z.array(z.object({
    name: z.string(),
    description: z.string().optional(),
    category: z.enum(['material', 'labor', 'equipment', 'other']).default('material'),
    quantity: z.number().positive(),
    unit: z.string(),
    estimatedPrice: z.number().optional(),
    notes: z.string().optional(),
    specifications: z.object({
      wireSize: z.string().optional(),
      wireType: z.string().optional(),
      conduitSize: z.string().optional(),
      conduitType: z.string().optional(),
      voltageRating: z.number().optional(),
      amperageRating: z.number().optional(),
      manufacturer: z.string().optional()
    }).optional()
  })).optional(),
  clarificationQuestions: z.array(z.string()).optional(),
  suggestions: z.array(z.string()).optional(),
  necReferences: z.array(z.string()).optional()
});

interface GenerateQuoteParams {
  inputType: 'text' | 'image' | 'mixed';
  inputData: string;
  customerId?: string;
  projectId?: string;
  includeImages?: boolean;
  userId: string;
  companyId: string;
}

export class AIQuoteService {
  private genAI: GoogleGenerativeAI;
  private quoteService: QuoteService;
  private materialPriceLookup: MaterialPriceLookupService;
  private models = {
    primary: 'gemini-2.0-flash-exp',
    fallback1: 'gemini-1.5-flash',
    fallback2: 'gemini-1.5-pro'
  };

  constructor() {
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      logger.warn('GEMINI_API_KEY is not configured - AI quote generation will not be available');
      this.genAI = null as any;
    } else {
      this.genAI = new GoogleGenerativeAI(apiKey);
    }
    
    this.quoteService = new QuoteService();
    this.materialPriceLookup = new MaterialPriceLookupService();
  }

  async generateQuote(params: GenerateQuoteParams) {
    if (!this.genAI) {
      // Debug: Check if AppError is available
      if (typeof AppError === 'undefined') {
        logger.error('AppError is undefined at runtime');
        throw new Error('AI quote generation is not available. Please configure GEMINI_API_KEY.');
      }
      throw new AppError('AI quote generation is not available. Please configure GEMINI_API_KEY.', 503);
    }
    
    const startTime = Date.now();
    let modelUsed = this.models.primary;
    
    try {
      // Get context if customerId or projectId provided
      const context = await this.getContext(params.customerId, params.projectId);
      
      // Prepare system prompt
      const systemPrompt = this.buildSystemPrompt(params.inputType, context);
      
      // Call AI with fallback logic
      let aiResponse;
      try {
        aiResponse = await this.callAI(this.models.primary, systemPrompt, params.inputData, params.inputType);
        modelUsed = this.models.primary;
      } catch (primaryError) {
        logger.warn('Primary AI model failed, trying fallback', { error: primaryError });
        
        try {
          aiResponse = await this.callAI(this.models.fallback1, systemPrompt, params.inputData, params.inputType);
          modelUsed = this.models.fallback1;
        } catch (fallbackError) {
          logger.warn('First fallback AI model failed, trying second fallback', { error: fallbackError });
          aiResponse = await this.callAI(this.models.fallback2, systemPrompt, params.inputData, params.inputType);
          modelUsed = this.models.fallback2;
        }
      }
      
      // Parse and validate response
      const parsedResponse = this.parseAIResponse(aiResponse);
      const validatedResponse = aiQuoteResponseSchema.parse(parsedResponse);
      
      // Check if clarification needed
      if (validatedResponse.clarificationQuestions && validatedResponse.clarificationQuestions.length > 0) {
        // Create a draft quote with questions
        const draftQuote = await this.createDraftQuote({
          ...params,
          aiData: {
            status: 'pending_questions',
            questions: validatedResponse.clarificationQuestions,
            originalInput: params.inputData,
            modelUsed,
            processingTime: Date.now() - startTime
          }
        });
        
        return {
          status: 'pending_questions',
          quoteId: draftQuote.id,
          questions: validatedResponse.clarificationQuestions,
          suggestions: validatedResponse.suggestions
        };
      }
      
      // Create complete quote
      const quote = await this.createCompleteQuote({
        ...params,
        ...validatedResponse,
        context,
        aiData: {
          status: 'complete',
          modelUsed,
          processingTime: Date.now() - startTime,
          necReferences: validatedResponse.necReferences
        }
      });
      
      // Initiate price lookups for materials
      if (validatedResponse.items && validatedResponse.items.length > 0) {
        await this.materialPriceLookup.initiateBulkLookup(
          quote.id,
          validatedResponse.items.filter(item => item.category === 'material'),
          params.companyId
        );
      }
      
      return {
        status: 'complete',
        quote,
        suggestions: validatedResponse.suggestions
      };
      
    } catch (error) {
      logger.error('AI quote generation failed', { error, params });
      // Debug: Check if AppError is available
      if (typeof AppError === 'undefined') {
        logger.error('AppError is undefined at runtime in catch block');
        throw new Error('Failed to generate quote with AI');
      }
      throw new AppError('Failed to generate quote with AI', 500);
    }
  }

  async answerQuestions(quoteId: string, answers: Record<string, string>, companyId: string) {
    const quote = await this.quoteService.getQuoteById(quoteId, companyId);
    
    if (!quote.ai_generation_data || quote.ai_generation_data.status !== 'pending_questions') {
      throw new AppError('Quote is not waiting for answers', 400);
    }
    
    const startTime = Date.now();
    
    // Build follow-up prompt with answers
    const followUpPrompt = this.buildFollowUpPrompt(
      quote.ai_generation_data.originalInput,
      quote.ai_generation_data.questions,
      answers
    );
    
    // Call AI with answers
    const aiResponse = await this.callAI(
      quote.ai_generation_data.modelUsed || this.models.primary,
      followUpPrompt,
      '',
      'text'
    );
    
    // Parse and validate response
    const parsedResponse = this.parseAIResponse(aiResponse);
    const validatedResponse = aiQuoteResponseSchema.parse(parsedResponse);
    
    // Update quote with generated content
    const updatedQuote = await this.updateQuoteWithAIContent(
      quoteId,
      validatedResponse,
      {
        status: 'complete',
        answers,
        processingTime: Date.now() - startTime
      },
      companyId
    );
    
    // Initiate price lookups
    if (validatedResponse.items && validatedResponse.items.length > 0) {
      await this.materialPriceLookup.initiateBulkLookup(
        quoteId,
        validatedResponse.items.filter(item => item.category === 'material'),
        companyId
      );
    }
    
    return {
      status: 'complete',
      quote: updatedQuote,
      suggestions: validatedResponse.suggestions
    };
  }

  async getSuggestions(quoteId: string, type: string, companyId: string) {
    const quote = await this.quoteService.getQuoteById(quoteId, companyId);
    const items = JSON.parse(quote.items || '[]');
    
    let prompt = '';
    switch (type) {
      case 'materials':
        prompt = this.buildMaterialSuggestionsPrompt(quote, items);
        break;
      case 'pricing':
        prompt = this.buildPricingSuggestionsPrompt(quote, items);
        break;
      case 'alternatives':
        prompt = this.buildAlternativesSuggestionsPrompt(quote, items);
        break;
      default:
        throw new AppError('Invalid suggestion type', 400);
    }
    
    const model = await this.genAI.getGenerativeModel({ model: this.models.primary });
    const result = await model.generateContent(prompt);
    const response = result.response;
    const text = response.text();
    
    try {
      const suggestions = JSON.parse(text);
      return { suggestions };
    } catch (error) {
      // If not JSON, return as text suggestions
      return { 
        suggestions: text.split('\n').filter(line => line.trim()).map(line => line.trim())
      };
    }
  }

  private async getContext(customerId?: string, projectId?: string) {
    const context: any = {};
    
    if (customerId) {
      const customer = await prisma.customer.findUnique({
        where: { id: customerId }
      });
      if (customer) {
        context.customer = {
          name: customer.name,
          type: customer.type,
          paymentTerms: customer.payment_terms
        };
      }
    }
    
    if (projectId) {
      const project = await prisma.project.findUnique({
        where: { id: projectId },
        include: {
          panels: true
        }
      });
      if (project) {
        context.project = {
          name: project.name,
          type: project.type,
          voltageSystem: project.voltage_system,
          serviceSize: project.service_size,
          squareFootage: project.square_footage,
          hasGenerator: project.has_generator,
          hasSolar: project.has_solar,
          panelCount: project.panels.length
        };
      }
    }
    
    return context;
  }

  private buildSystemPrompt(inputType: string, context: any): string {
    let prompt = `You are an expert electrical contractor creating a detailed quote for a customer. 
    You must respond with a valid JSON object that matches this exact structure:
    {
      "projectOverview": "string - Brief overview of the project",
      "scopeOfWork": "string - Detailed scope of work",
      "materialsIncluded": "string - List of materials included",
      "exclusions": "string - What is not included",
      "items": [
        {
          "name": "string - Item name",
          "description": "string - Detailed description",
          "category": "material | labor | equipment | other",
          "quantity": number,
          "unit": "string - Unit of measurement (EA, FT, HR, etc)",
          "estimatedPrice": number (optional),
          "notes": "string (optional)",
          "specifications": {
            "wireSize": "string (optional - e.g., #12, #10)",
            "wireType": "string (optional - e.g., THHN, ROMEX)",
            "conduitSize": "string (optional - e.g., 3/4\", 1\")",
            "conduitType": "string (optional - e.g., EMT, PVC)",
            "voltageRating": number (optional),
            "amperageRating": number (optional),
            "manufacturer": "string (optional)"
          }
        }
      ],
      "clarificationQuestions": ["string - Questions if more info needed"],
      "suggestions": ["string - Professional suggestions"],
      "necReferences": ["string - Relevant NEC code references"]
    }
    
    Important guidelines:
    1. For electrical materials, be specific about wire sizes, conduit types, and ratings
    2. Follow NEC 2023 code requirements
    3. Include safety considerations
    4. If the request is vague, ask clarification questions
    5. Provide quantity estimates based on typical installations
    6. For labor items, use hours (HR) as the unit
    7. Include relevant NEC references for code compliance
    `;
    
    if (context.customer) {
      prompt += `\n\nCustomer Information:
      - Name: ${context.customer.name}
      - Type: ${context.customer.type}
      - Payment Terms: ${context.customer.paymentTerms}`;
    }
    
    if (context.project) {
      prompt += `\n\nProject Information:
      - Name: ${context.project.name}
      - Type: ${context.project.type}
      - Voltage System: ${context.project.voltageSystem}
      - Service Size: ${context.project.serviceSize}A
      - Square Footage: ${context.project.squareFootage || 'Not specified'}
      - Has Generator: ${context.project.hasGenerator ? 'Yes' : 'No'}
      - Has Solar: ${context.project.hasSolar ? 'Yes' : 'No'}
      - Panel Count: ${context.project.panelCount}`;
    }
    
    if (inputType === 'image' || inputType === 'mixed') {
      prompt += `\n\nYou will be analyzing images of electrical work or plans. 
      Identify all visible components and estimate quantities based on what you see.`;
    }
    
    return prompt;
  }

  private buildFollowUpPrompt(originalInput: string, questions: string[], answers: Record<string, string>): string {
    let prompt = `Based on the following information, create a complete electrical quote.
    
    Original Request: ${originalInput}
    
    Questions and Answers:\n`;
    
    questions.forEach((question, index) => {
      prompt += `Q: ${question}\nA: ${answers[index.toString()] || 'Not answered'}\n\n`;
    });
    
    prompt += `\n${this.buildSystemPrompt('text', {})}`;
    
    return prompt;
  }

  private buildMaterialSuggestionsPrompt(quote: any, items: any[]): string {
    return `Analyze this electrical quote and suggest additional materials that might be needed:
    
    Project Overview: ${quote.project_overview}
    Scope of Work: ${quote.scope_of_work}
    
    Current Materials:
    ${items.filter(i => i.category === 'material').map(i => `- ${i.quantity} ${i.unit} of ${i.name}`).join('\n')}
    
    Suggest any missing materials, consumables, or accessories that are typically needed for this type of work.
    Consider wire nuts, straps, boxes, covers, breakers, etc.
    Format as a JSON array of suggested items with name, quantity, unit, and reason.`;
  }

  private buildPricingSuggestionsPrompt(quote: any, items: any[]): string {
    return `Analyze this electrical quote pricing and provide suggestions:
    
    Total: $${quote.total}
    Items: ${items.length}
    
    Provide pricing suggestions including:
    1. Is the pricing competitive for the market?
    2. Any items that seem over/under priced?
    3. Suggested profit margins
    4. Volume discount opportunities
    
    Format as JSON with specific recommendations.`;
  }

  private buildAlternativesSuggestionsPrompt(quote: any, items: any[]): string {
    return `Suggest cost-effective alternatives for this electrical quote:
    
    Current Materials:
    ${items.filter(i => i.category === 'material').map(i => `- ${i.name} (${i.specifications ? JSON.stringify(i.specifications) : 'No specs'})`).join('\n')}
    
    Suggest alternatives that:
    1. Meet the same electrical requirements
    2. Comply with NEC code
    3. Could reduce costs
    4. Might improve quality or longevity
    
    Format as JSON with original item, suggested alternative, and reasoning.`;
  }

  private async callAI(modelName: string, systemPrompt: string, userInput: string, inputType: string) {
    const model = await this.genAI.getGenerativeModel({ 
      model: modelName,
      generationConfig: {
        temperature: 0.2,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 8192,
      }
    });
    
    let prompt = systemPrompt;
    if (userInput) {
      prompt += `\n\nUser Input: ${userInput}`;
    }
    
    const result = await model.generateContent(prompt);
    const response = result.response;
    
    if (!response || !response.text()) {
      throw new Error('Empty response from AI model');
    }
    
    return response.text();
  }

  private parseAIResponse(text: string): any {
    // First try to extract JSON from markdown code blocks
    const codeBlockMatch = text.match(/```(?:json)?\s*\n?([\s\S]*?)\n?```/);
    let jsonText = text;
    
    if (codeBlockMatch) {
      jsonText = codeBlockMatch[1];
    } else {
      // Try to extract JSON directly
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        jsonText = jsonMatch[0];
      } else {
        throw new Error('No JSON found in AI response');
      }
    }
    
    try {
      return JSON.parse(jsonText.trim());
    } catch (error) {
      logger.error('Failed to parse AI response', { text, error });
      throw new Error('Invalid JSON in AI response');
    }
  }

  private async createDraftQuote(params: any) {
    const quoteData = {
      name: 'AI Generated Quote (Draft)',
      customerId: params.customerId || null,
      projectId: params.projectId || null,
      companyId: params.companyId,
      createdById: params.userId,
      items: [],
      subtotal: 0,
      total: 0,
      status: 'DRAFT',
      aiGenerationData: JSON.stringify(params.aiData)
    };
    
    return this.quoteService.createQuote(quoteData);
  }

  private async createCompleteQuote(params: any) {
    const items = params.items || [];
    
    // Estimate prices if not provided
    const itemsWithPrices = items.map((item: any) => ({
      ...item,
      unitPrice: item.estimatedPrice || 0,
      totalPrice: (item.estimatedPrice || 0) * item.quantity,
      lookupStatus: item.estimatedPrice ? 'confirmed' : 'pending'
    }));
    
    const quoteData = {
      name: params.context?.project?.name 
        ? `Quote for ${params.context.project.name}` 
        : 'AI Generated Quote',
      customerId: params.customerId || null,
      projectId: params.projectId || null,
      companyId: params.companyId,
      createdById: params.userId,
      projectOverview: params.projectOverview,
      scopeOfWork: params.scopeOfWork,
      materialsIncluded: params.materialsIncluded,
      exclusions: params.exclusions,
      items: itemsWithPrices,
      status: 'DRAFT',
      aiGenerationData: JSON.stringify(params.aiData)
    };
    
    return this.quoteService.createQuote(quoteData);
  }

  private async updateQuoteWithAIContent(quoteId: string, content: any, aiData: any, companyId: string) {
    const items = content.items || [];
    
    // Estimate prices if not provided
    const itemsWithPrices = items.map((item: any) => ({
      ...item,
      unitPrice: item.estimatedPrice || 0,
      totalPrice: (item.estimatedPrice || 0) * item.quantity,
      lookupStatus: item.estimatedPrice ? 'confirmed' : 'pending'
    }));
    
    const updateData = {
      projectOverview: content.projectOverview,
      scopeOfWork: content.scopeOfWork,
      materialsIncluded: content.materialsIncluded,
      exclusions: content.exclusions,
      items: itemsWithPrices,
      aiGenerationData: JSON.stringify(aiData)
    };
    
    return this.quoteService.updateQuote(quoteId, updateData, companyId);
  }
}