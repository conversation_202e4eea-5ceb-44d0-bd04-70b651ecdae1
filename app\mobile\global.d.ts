/// <reference types="react-native" />

declare module '@env' {
  export const API_URL: string;
  export const API_TIMEOUT: string;
  export const API_SIGNING_SECRET: string;
  export const NODE_ENV: string;
  export const ENABLE_OFFLINE_MODE: string;
  export const ENABLE_BARCODE_SCANNER: string;
  export const ENABLE_PUSH_NOTIFICATIONS: string;
  export const ANALYTICS_KEY: string;
  export const SENTRY_DSN: string;
  export const ENABLE_SSL_PINNING: string;
  export const ENABLE_REQUEST_SIGNING: string;
  export const ENABLE_DEVICE_BINDING: string;
}

declare module '*.svg' {
  import React from 'react';
  import { SvgProps } from 'react-native-svg';
  const content: React.FC<SvgProps>;
  export default content;
}

declare module '*.png' {
  const value: any;
  export = value;
}

declare module '*.jpg' {
  const value: any;
  export = value;
}