{"name": "@electrical/frontend", "version": "1.0.0", "description": "Frontend UI for electrical contracting application", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:e2e": "cypress run", "lint": "eslint src --ext .ts,.tsx", "typecheck": "tsc --noEmit"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.2.0", "@tanstack/react-query": "^5.17.19", "@tanstack/react-virtual": "^3.0.1", "axios": "^1.6.5", "clsx": "^2.1.0", "date-fns": "^3.3.1", "date-fns-tz": "^2.0.0", "decimal.js": "^10.4.3", "dexie": "^3.2.4", "react": "^18.2.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-hook-form": "^7.49.3", "react-hot-toast": "^2.4.1", "react-router-dom": "^6.30.1", "react-window": "^1.8.10", "recharts": "^2.10.4", "socket.io-client": "^4.6.1", "tailwind-merge": "^2.2.0", "web-vitals": "^3.5.0", "zod": "^3.22.4", "zustand": "^4.5.0"}, "devDependencies": {"@hookform/resolvers": "^3.3.4", "@tailwindcss/forms": "^0.5.7", "@tanstack/react-query-devtools": "^5.17.19", "@types/puppeteer": "^7.0.4", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "@types/react-window": "^1.8.8", "@typescript-eslint/eslint-plugin": "^6.19.0", "@typescript-eslint/parser": "^6.19.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.17", "cypress": "^13.6.3", "eslint": "^8.56.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "lucide-react": "^0.309.0", "postcss": "^8.4.33", "puppeteer": "^24.12.1", "tailwindcss": "^3.4.1", "tsx": "^4.20.3", "typescript": "^5.3.3", "vite": "^5.0.12", "vite-plugin-pwa": "^0.17.4", "vitest": "^1.2.1"}}