/**
 * Auth debugging utilities to help identify persistence issues
 */

export function debugAuthPersistence() {
  console.group('🔍 Auth Persistence Debug');
  
  // Check localStorage directly
  const rawAuthStorage = localStorage.getItem('auth-storage');
  console.log('Raw localStorage auth-storage:', rawAuthStorage);
  
  if (rawAuthStorage) {
    try {
      const parsed = JSON.parse(rawAuthStorage);
      console.log('Parsed auth storage:', parsed);
      console.log('State:', parsed.state);
      console.log('Version:', parsed.version);
    } catch (e) {
      console.error('Failed to parse auth storage:', e);
    }
  }
  
  // Check if any other code might be clearing localStorage
  const originalSetItem = localStorage.setItem;
  const originalRemoveItem = localStorage.removeItem;
  const originalClear = localStorage.clear;
  
  // Temporarily override to log calls
  localStorage.setItem = function(key: string, value: string) {
    if (key === 'auth-storage') {
      console.trace('localStorage.setItem called for auth-storage:', value);
    }
    return originalSetItem.call(this, key, value);
  };
  
  localStorage.removeItem = function(key: string) {
    if (key === 'auth-storage') {
      console.trace('localStorage.removeItem called for auth-storage');
    }
    return originalRemoveItem.call(this, key);
  };
  
  localStorage.clear = function() {
    console.trace('localStorage.clear called - this will remove auth!');
    return originalClear.call(this);
  };
  
  console.groupEnd();
}

export function monitorAuthChanges() {
  // Monitor storage events
  window.addEventListener('storage', (e) => {
    if (e.key === 'auth-storage') {
      console.log('Storage event for auth-storage:', {
        oldValue: e.oldValue,
        newValue: e.newValue,
        url: e.url,
        storageArea: e.storageArea
      });
    }
  });
  
  // Check auth state periodically
  setInterval(() => {
    const authStorage = localStorage.getItem('auth-storage');
    if (authStorage) {
      try {
        const parsed = JSON.parse(authStorage);
        const hasAuth = !!parsed.state?.accessToken && !!parsed.state?.user;
        console.log(`[Auth Check] hasAuth: ${hasAuth}, token: ${!!parsed.state?.accessToken}, user: ${!!parsed.state?.user}`);
      } catch (e) {
        console.error('[Auth Check] Failed to parse:', e);
      }
    } else {
      console.log('[Auth Check] No auth-storage in localStorage');
    }
  }, 5000);
}