// Temporarily using mock TypeORM - will be replaced with proper database solution
// Note: Decorators removed for React Native compatibility
import { BaseEntity } from './BaseEntity';
import { Project } from './Project';

export class Photo extends BaseEntity {
  filename: string;

  localPath: string;

  remotePath: string;

  thumbnailPath: string;

  size: number;

  mimeType: string;

  width: number;

  height: number;

  caption: string;

  category: string; // 'panel', 'wiring', 'site', 'inspection', etc.

  location: string;

  tags: string; // JSON array of tags

  takenAt: string;

  takenBy: string;

  isCompressed: boolean;

  metadata: string; // JSON string (EXIF data, etc.)

  projectId: string;

  project: Project;
}