// Bypass workspace configuration and create standalone services
const fs = require('fs');
const path = require('path');
const { spawn, exec } = require('child_process');

async function runCommand(command, cwd, timeout = 60000) {
  return new Promise((resolve) => {
    console.log(`\n🔧 Running: ${command}`);
    console.log(`📁 Directory: ${cwd}`);
    
    const process = exec(command, { cwd, timeout }, (error, stdout, stderr) => {
      if (error) {
        console.error(`❌ Error: ${error.message}`);
        resolve({ success: false, error: error.message, stdout, stderr });
      } else {
        console.log(`✅ Success: ${stdout}`);
        if (stderr) console.warn(`⚠️ Warning: ${stderr}`);
        resolve({ success: true, stdout, stderr });
      }
    });
  });
}

async function createStandaloneBackend() {
  console.log('\n🔧 === CREATING STANDALONE BACKEND ===');
  
  const backendDir = path.join(__dirname, 'app', 'backend');
  const originalPackageJson = path.join(backendDir, 'package.json');
  const backupPackageJson = path.join(backendDir, 'package-original.json');
  
  try {
    // Backup original package.json
    if (fs.existsSync(originalPackageJson)) {
      fs.copyFileSync(originalPackageJson, backupPackageJson);
      console.log('✅ Backed up original package.json');
    }
    
    // Create standalone package.json
    const standalonePackage = {
      "name": "electrical-backend-standalone",
      "version": "1.0.0",
      "description": "Standalone backend for electrical contracting application",
      "main": "dist/index.js",
      "scripts": {
        "dev": "tsx watch src/index.ts",
        "build": "tsc",
        "start": "node dist/index.js"
      },
      "dependencies": {
        "@google/genai": "^1.9.0",
        "@google/generative-ai": "^0.24.1",
        "@prisma/client": "^5.8.1",
        "axios": "^1.6.5",
        "bcryptjs": "^2.4.3",
        "compression": "^1.7.4",
        "cookie-parser": "^1.4.6",
        "cors": "^2.8.5",
        "date-fns": "^3.3.1",
        "dotenv": "^16.3.1",
        "express": "^4.18.2",
        "express-rate-limit": "^7.1.5",
        "helmet": "^7.1.0",
        "jsonwebtoken": "^9.0.2",
        "prisma": "^5.8.1",
        "uuid": "^9.0.1",
        "winston": "^3.11.0",
        "zod": "^3.22.4"
      },
      "devDependencies": {
        "@types/bcryptjs": "^2.4.6",
        "@types/compression": "^1.7.5",
        "@types/cookie-parser": "^1.4.6",
        "@types/cors": "^2.8.17",
        "@types/express": "^4.17.21",
        "@types/jsonwebtoken": "^9.0.5",
        "@types/node": "^20.11.5",
        "@types/uuid": "^9.0.7",
        "tsx": "^4.7.0",
        "typescript": "^5.3.3"
      }
    };
    
    fs.writeFileSync(originalPackageJson, JSON.stringify(standalonePackage, null, 2));
    console.log('✅ Created standalone package.json');
    
    // Remove node_modules and install fresh
    await runCommand('rmdir /s /q node_modules 2>nul || echo "No node_modules found"', backendDir);
    
    console.log('Installing dependencies...');
    const installResult = await runCommand('npm install --no-package-lock', backendDir);
    
    return installResult.success;
    
  } catch (error) {
    console.error('❌ Failed to create standalone backend:', error.message);
    return false;
  }
}

async function createStandaloneFrontend() {
  console.log('\n🔧 === CREATING STANDALONE FRONTEND ===');
  
  const frontendDir = path.join(__dirname, 'app', 'frontend');
  const originalPackageJson = path.join(frontendDir, 'package.json');
  const backupPackageJson = path.join(frontendDir, 'package-original.json');
  
  try {
    // Backup original package.json
    if (fs.existsSync(originalPackageJson)) {
      fs.copyFileSync(originalPackageJson, backupPackageJson);
      console.log('✅ Backed up original package.json');
    }
    
    // Create standalone package.json
    const standalonePackage = {
      "name": "electrical-frontend-standalone",
      "version": "1.0.0",
      "description": "Standalone frontend for electrical contracting application",
      "type": "module",
      "scripts": {
        "dev": "vite",
        "build": "tsc && vite build",
        "preview": "vite preview"
      },
      "dependencies": {
        "@headlessui/react": "^1.7.17",
        "@heroicons/react": "^2.0.18",
        "@hookform/resolvers": "^3.3.2",
        "axios": "^1.6.5",
        "date-fns": "^3.3.1",
        "react": "^18.2.0",
        "react-dom": "^18.2.0",
        "react-hook-form": "^7.48.2",
        "react-router-dom": "^6.20.1",
        "zustand": "^4.4.7",
        "zod": "^3.22.4"
      },
      "devDependencies": {
        "@types/react": "^18.2.43",
        "@types/react-dom": "^18.2.17",
        "@typescript-eslint/eslint-plugin": "^6.14.0",
        "@typescript-eslint/parser": "^6.14.0",
        "@vitejs/plugin-react": "^4.2.1",
        "autoprefixer": "^10.4.16",
        "eslint": "^8.55.0",
        "eslint-plugin-react-hooks": "^4.6.0",
        "eslint-plugin-react-refresh": "^0.4.5",
        "postcss": "^8.4.32",
        "tailwindcss": "^3.3.6",
        "typescript": "^5.2.2",
        "vite": "^5.0.8"
      }
    };
    
    fs.writeFileSync(originalPackageJson, JSON.stringify(standalonePackage, null, 2));
    console.log('✅ Created standalone package.json');
    
    // Remove node_modules and install fresh
    await runCommand('rmdir /s /q node_modules 2>nul || echo "No node_modules found"', frontendDir);
    
    console.log('Installing dependencies...');
    const installResult = await runCommand('npm install --no-package-lock', frontendDir);
    
    return installResult.success;
    
  } catch (error) {
    console.error('❌ Failed to create standalone frontend:', error.message);
    return false;
  }
}

async function startServices() {
  console.log('\n🚀 === STARTING SERVICES ===');
  
  const backendDir = path.join(__dirname, 'app', 'backend');
  const frontendDir = path.join(__dirname, 'app', 'frontend');
  
  // Start backend
  console.log('Starting backend...');
  const backendProcess = spawn('npm', ['run', 'dev'], {
    cwd: backendDir,
    stdio: 'pipe',
    shell: true,
    env: { ...process.env, NODE_ENV: 'development' }
  });
  
  backendProcess.stdout.on('data', (data) => {
    console.log(`[Backend] ${data}`);
  });
  
  backendProcess.stderr.on('data', (data) => {
    console.log(`[Backend Error] ${data}`);
  });
  
  // Wait for backend to start
  await new Promise(resolve => setTimeout(resolve, 8000));
  
  // Start frontend
  console.log('Starting frontend...');
  const frontendProcess = spawn('npm', ['run', 'dev'], {
    cwd: frontendDir,
    stdio: 'pipe',
    shell: true,
    env: { ...process.env, NODE_ENV: 'development' }
  });
  
  frontendProcess.stdout.on('data', (data) => {
    console.log(`[Frontend] ${data}`);
  });
  
  frontendProcess.stderr.on('data', (data) => {
    console.log(`[Frontend Error] ${data}`);
  });
  
  // Wait for frontend to start
  await new Promise(resolve => setTimeout(resolve, 10000));
  
  return { backendProcess, frontendProcess };
}

async function testAndRunComprehensive() {
  console.log('\n🧪 === TESTING SERVICES AND RUNNING COMPREHENSIVE TEST ===');
  
  const http = require('http');
  
  // Test backend
  const backendTest = await new Promise((resolve) => {
    const req = http.request({
      hostname: 'localhost',
      port: 3001,
      path: '/api/health',
      method: 'GET',
      timeout: 5000
    }, (res) => {
      console.log(`✅ Backend is running! Status: ${res.statusCode}`);
      resolve(true);
    });
    
    req.on('error', (error) => {
      console.log(`❌ Backend not responding: ${error.message}`);
      resolve(false);
    });
    
    req.end();
  });
  
  // Test frontend
  const frontendTest = await new Promise((resolve) => {
    const req = http.request({
      hostname: 'localhost',
      port: 3000,
      path: '/',
      method: 'GET',
      timeout: 5000
    }, (res) => {
      console.log(`✅ Frontend is running! Status: ${res.statusCode}`);
      resolve(true);
    });
    
    req.on('error', (error) => {
      console.log(`❌ Frontend not responding: ${error.message}`);
      resolve(false);
    });
    
    req.end();
  });
  
  if (backendTest && frontendTest) {
    console.log('\n✅ Both services running! Starting comprehensive test...');
    
    try {
      const { chromium } = require('playwright');
      
      const browser = await chromium.launch({ headless: false });
      const context = await browser.newContext();
      const page = await context.newPage();
      
      // Navigate and test login
      console.log('Testing login...');
      await page.goto('http://localhost:3000/login', { waitUntil: 'domcontentloaded', timeout: 30000 });
      await page.waitForTimeout(3000);
      
      // Fill login form
      await page.fill('input[type="email"]', '<EMAIL>');
      await page.fill('input[type="password"]', 'itsMike818!');
      await page.click('button[type="submit"]');
      
      await page.waitForTimeout(5000);
      
      // Take screenshots
      await page.screenshot({ path: 'login-test.png' });
      console.log('📸 Screenshot saved: login-test.png');
      
      const currentUrl = page.url();
      if (!currentUrl.includes('/login')) {
        console.log('✅ Login successful!');
        
        // Test quotes navigation
        try {
          await page.click('a[href="/quotes"]');
          await page.waitForTimeout(3000);
          await page.screenshot({ path: 'quotes-test.png' });
          console.log('📸 Screenshot saved: quotes-test.png');
          console.log('✅ Quotes navigation successful!');
        } catch (error) {
          console.log('⚠️ Quotes navigation failed:', error.message);
        }
      }
      
      await browser.close();
      console.log('🎉 COMPREHENSIVE TEST COMPLETED!');
      
    } catch (error) {
      console.error('❌ Comprehensive test failed:', error.message);
    }
  }
  
  return { backend: backendTest, frontend: frontendTest };
}

async function restoreOriginalFiles() {
  console.log('\n🔄 === RESTORING ORIGINAL FILES ===');
  
  const backendDir = path.join(__dirname, 'app', 'backend');
  const frontendDir = path.join(__dirname, 'app', 'frontend');
  
  // Restore backend
  const backendBackup = path.join(backendDir, 'package-original.json');
  const backendPackage = path.join(backendDir, 'package.json');
  
  if (fs.existsSync(backendBackup)) {
    fs.copyFileSync(backendBackup, backendPackage);
    fs.unlinkSync(backendBackup);
    console.log('✅ Restored backend package.json');
  }
  
  // Restore frontend
  const frontendBackup = path.join(frontendDir, 'package-original.json');
  const frontendPackage = path.join(frontendDir, 'package.json');
  
  if (fs.existsSync(frontendBackup)) {
    fs.copyFileSync(frontendBackup, frontendPackage);
    fs.unlinkSync(frontendBackup);
    console.log('✅ Restored frontend package.json');
  }
}

async function main() {
  try {
    console.log('🚀 === BYPASS WORKSPACE AND START SERVICES ===');
    
    // Step 1: Create standalone services
    const backendSuccess = await createStandaloneBackend();
    const frontendSuccess = await createStandaloneFrontend();
    
    if (backendSuccess && frontendSuccess) {
      console.log('\n✅ Standalone services created successfully!');
      
      // Step 2: Start services
      const processes = await startServices();
      
      // Step 3: Test and run comprehensive test
      const testResults = await testAndRunComprehensive();
      
      if (testResults.backend && testResults.frontend) {
        console.log('\n🎉 === COMPLETE SUCCESS ===');
        console.log('Frontend: http://localhost:3000');
        console.log('Backend: http://localhost:3001');
        console.log('Login: <EMAIL> / itsMike818!');
      }
      
    } else {
      console.log('\n❌ Failed to create standalone services');
    }
    
  } catch (error) {
    console.error('Main execution error:', error);
  } finally {
    console.log('\nServices will continue running. Press Ctrl+C to stop.');
    console.log('To restore original files, run: node restore-workspace.js');
    
    // Create restore script
    const restoreScript = `
const fs = require('fs');
const path = require('path');

${restoreOriginalFiles.toString()}

restoreOriginalFiles().then(() => {
  console.log('✅ Original workspace files restored');
}).catch(error => {
  console.error('❌ Failed to restore files:', error);
});
`;
    
    fs.writeFileSync(path.join(__dirname, 'restore-workspace.js'), restoreScript);
  }
}

main();
