import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';
import { format } from 'date-fns';

const APP_URL = 'http://localhost:3000';
const API_URL = 'http://localhost:3001';
const SCREENSHOT_DIR = './screenshots';

interface TestResult {
  section: string;
  status: 'pass' | 'fail';
  errors: string[];
  screenshots: string[];
}

class ComprehensiveAppTester {
  private browser: Browser | null = null;
  private page: Page | null = null;
  private results: TestResult[] = [];

  async initialize() {
    this.browser = await puppeteer.launch({
      headless: false,
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
      defaultViewport: { width: 1920, height: 1080 }
    });
    
    this.page = await this.browser.newPage();
    
    // Set up console message handling
    this.page.on('console', msg => {
      if (msg.type() === 'error') {
        console.error('Browser console error:', msg.text());
      }
    });
    
    // Set up error handling
    this.page.on('pageerror', error => {
      console.error('Page error:', error.message);
    });
    
    // Create screenshots directory
    const fs = require('fs');
    if (!fs.existsSync(SCREENSHOT_DIR)) {
      fs.mkdirSync(SCREENSHOT_DIR, { recursive: true });
    }
  }

  async login() {
    console.log('🔐 Logging in...');
    await this.page!.goto(APP_URL);
    
    // Wait for login form
    await this.page!.waitForSelector('input[type="email"]', { timeout: 10000 });
    
    // Fill login credentials
    await this.page!.type('input[type="email"]', '<EMAIL>');
    await this.page!.type('input[type="password"]', 'password123');
    
    // Take screenshot of login page
    await this.takeScreenshot('login-page');
    
    // Submit login form
    await this.page!.click('button[type="submit"]');
    
    // Wait for dashboard to load
    await this.page!.waitForSelector('.dashboard-container', { timeout: 10000 });
    console.log('✅ Login successful');
  }

  async takeScreenshot(name: string) {
    const timestamp = format(new Date(), 'yyyyMMdd-HHmmss');
    const filename = `${SCREENSHOT_DIR}/${name}-${timestamp}.png`;
    await this.page!.screenshot({ path: filename, fullPage: true });
    return filename;
  }

  async testDashboard() {
    const result: TestResult = {
      section: 'Dashboard',
      status: 'pass',
      errors: [],
      screenshots: []
    };

    try {
      console.log('\n📊 Testing Dashboard...');
      
      // Navigate to dashboard
      await this.page!.goto(`${APP_URL}/dashboard`);
      await this.page!.waitForSelector('.dashboard-container', { timeout: 10000 });
      
      // Take screenshot
      result.screenshots.push(await this.takeScreenshot('dashboard-main'));
      
      // Test dashboard widgets
      const widgets = await this.page!.$$('.dashboard-widget');
      console.log(`  Found ${widgets.length} dashboard widgets`);
      
      // Test revenue chart
      const revenueChart = await this.page!.$('.revenue-chart');
      if (revenueChart) {
        console.log('  ✓ Revenue chart found');
      } else {
        result.errors.push('Revenue chart not found');
      }
      
      // Test project summary
      const projectSummary = await this.page!.$('.project-summary');
      if (projectSummary) {
        console.log('  ✓ Project summary found');
      } else {
        result.errors.push('Project summary not found');
      }
      
    } catch (error: any) {
      result.status = 'fail';
      result.errors.push(error.message);
      console.error('  ❌ Dashboard test failed:', error.message);
    }

    this.results.push(result);
  }

  async testProjects() {
    const result: TestResult = {
      section: 'Projects',
      status: 'pass',
      errors: [],
      screenshots: []
    };

    try {
      console.log('\n🏗️ Testing Projects...');
      
      // Navigate to projects
      await this.page!.click('a[href="/projects"]');
      await this.page!.waitForSelector('.projects-container', { timeout: 10000 });
      
      // Take screenshot
      result.screenshots.push(await this.takeScreenshot('projects-list'));
      
      // Test create project button
      const createBtn = await this.page!.$('button:has-text("New Project")');
      if (createBtn) {
        await createBtn.click();
        await this.page!.waitForSelector('.project-form', { timeout: 5000 });
        
        // Take screenshot of form
        result.screenshots.push(await this.takeScreenshot('projects-create-form'));
        
        // Close form
        const closeBtn = await this.page!.$('button:has-text("Cancel")');
        if (closeBtn) await closeBtn.click();
        
        console.log('  ✓ Create project form works');
      } else {
        result.errors.push('Create project button not found');
      }
      
      // Test project list
      const projectItems = await this.page!.$$('.project-item');
      console.log(`  Found ${projectItems.length} projects`);
      
      // Test search functionality
      const searchInput = await this.page!.$('input[placeholder*="Search"]');
      if (searchInput) {
        await searchInput.type('test');
        await this.page!.waitForTimeout(1000);
        console.log('  ✓ Search functionality works');
      }
      
    } catch (error: any) {
      result.status = 'fail';
      result.errors.push(error.message);
      console.error('  ❌ Projects test failed:', error.message);
    }

    this.results.push(result);
  }

  async testEstimates() {
    const result: TestResult = {
      section: 'Estimates',
      status: 'pass',
      errors: [],
      screenshots: []
    };

    try {
      console.log('\n📋 Testing Estimates...');
      
      // Navigate to estimates
      await this.page!.click('a[href="/estimates"]');
      await this.page!.waitForSelector('.estimates-container', { timeout: 10000 });
      
      // Take screenshot
      result.screenshots.push(await this.takeScreenshot('estimates-list'));
      
      // Test filters
      const statusFilter = await this.page!.$('select[value="ALL"]');
      if (statusFilter) {
        await statusFilter.select('DRAFT');
        await this.page!.waitForTimeout(1000);
        console.log('  ✓ Status filter works');
      }
      
      // Test create estimate button
      const createBtn = await this.page!.$('button:has-text("New Estimate")');
      if (createBtn) {
        await createBtn.click();
        await this.page!.waitForSelector('.estimate-form', { timeout: 5000 });
        
        // Take screenshot of form
        result.screenshots.push(await this.takeScreenshot('estimates-create-form'));
        
        // Close form
        const closeBtn = await this.page!.$('button:has-text("Cancel")');
        if (closeBtn) await closeBtn.click();
        
        console.log('  ✓ Create estimate form works');
      } else {
        result.errors.push('Create estimate button not found');
      }
      
    } catch (error: any) {
      result.status = 'fail';
      result.errors.push(error.message);
      console.error('  ❌ Estimates test failed:', error.message);
    }

    this.results.push(result);
  }

  async testCustomers() {
    const result: TestResult = {
      section: 'Customers',
      status: 'pass',
      errors: [],
      screenshots: []
    };

    try {
      console.log('\n👥 Testing Customers...');
      
      // Navigate to customers
      await this.page!.click('a[href="/customers"]');
      await this.page!.waitForSelector('.customers-container', { timeout: 10000 });
      
      // Take screenshot
      result.screenshots.push(await this.takeScreenshot('customers-list'));
      
      // Test search
      const searchInput = await this.page!.$('input[placeholder*="Search"]');
      if (searchInput) {
        await searchInput.type('John');
        await this.page!.waitForTimeout(1000);
        console.log('  ✓ Search functionality works');
      }
      
      // Test create customer button
      const createBtn = await this.page!.$('button:has-text("New Customer")');
      if (createBtn) {
        await createBtn.click();
        await this.page!.waitForSelector('.customer-form', { timeout: 5000 });
        
        // Take screenshot of form
        result.screenshots.push(await this.takeScreenshot('customers-create-form'));
        
        // Test form fields
        const nameInput = await this.page!.$('input[name="name"]');
        const emailInput = await this.page!.$('input[name="email"]');
        const phoneInput = await this.page!.$('input[name="phone"]');
        
        if (nameInput && emailInput && phoneInput) {
          console.log('  ✓ Customer form fields present');
        } else {
          result.errors.push('Missing customer form fields');
        }
        
        // Close form
        const closeBtn = await this.page!.$('button:has-text("Cancel")');
        if (closeBtn) await closeBtn.click();
      }
      
    } catch (error: any) {
      result.status = 'fail';
      result.errors.push(error.message);
      console.error('  ❌ Customers test failed:', error.message);
    }

    this.results.push(result);
  }

  async testCalculations() {
    const result: TestResult = {
      section: 'Calculations',
      status: 'pass',
      errors: [],
      screenshots: []
    };

    try {
      console.log('\n🧮 Testing Calculations...');
      
      // Navigate to calculations
      await this.page!.click('a[href="/calculations"]');
      await this.page!.waitForSelector('.calculations-container', { timeout: 10000 });
      
      // Take screenshot
      result.screenshots.push(await this.takeScreenshot('calculations-main'));
      
      // Test each calculator type
      const calculatorTypes = [
        'Load Calculator',
        'Voltage Drop',
        'Wire Sizing',
        'Conduit Fill',
        'Arc Flash',
        'Short Circuit'
      ];
      
      for (const calcType of calculatorTypes) {
        const calcButton = await this.page!.$(`button:has-text("${calcType}")`);
        if (calcButton) {
          await calcButton.click();
          await this.page!.waitForTimeout(1000);
          
          // Take screenshot
          result.screenshots.push(await this.takeScreenshot(`calculations-${calcType.toLowerCase().replace(' ', '-')}`));
          
          console.log(`  ✓ ${calcType} calculator loads`);
          
          // Go back to main calculations
          const backBtn = await this.page!.$('button:has-text("Back")');
          if (backBtn) await backBtn.click();
        } else {
          result.errors.push(`${calcType} calculator button not found`);
        }
      }
      
    } catch (error: any) {
      result.status = 'fail';
      result.errors.push(error.message);
      console.error('  ❌ Calculations test failed:', error.message);
    }

    this.results.push(result);
  }

  async testReports() {
    const result: TestResult = {
      section: 'Reports',
      status: 'pass',
      errors: [],
      screenshots: []
    };

    try {
      console.log('\n📈 Testing Reports...');
      
      // Navigate to reports
      await this.page!.click('a[href="/reports"]');
      await this.page!.waitForSelector('.reports-container', { timeout: 10000 });
      
      // Take screenshot
      result.screenshots.push(await this.takeScreenshot('reports-main'));
      
      // Test report types
      const reportTypes = await this.page!.$$('.report-type-card');
      console.log(`  Found ${reportTypes.length} report types`);
      
      // Test date range picker
      const dateRangePicker = await this.page!.$('.date-range-picker');
      if (dateRangePicker) {
        console.log('  ✓ Date range picker present');
      } else {
        result.errors.push('Date range picker not found');
      }
      
      // Test export button
      const exportBtn = await this.page!.$('button:has-text("Export")');
      if (exportBtn) {
        console.log('  ✓ Export functionality available');
      }
      
    } catch (error: any) {
      result.status = 'fail';
      result.errors.push(error.message);
      console.error('  ❌ Reports test failed:', error.message);
    }

    this.results.push(result);
  }

  async testSettings() {
    const result: TestResult = {
      section: 'Settings',
      status: 'pass',
      errors: [],
      screenshots: []
    };

    try {
      console.log('\n⚙️ Testing Settings...');
      
      // Navigate to settings
      await this.page!.click('a[href="/settings"]');
      await this.page!.waitForSelector('.settings-container', { timeout: 10000 });
      
      // Take screenshot
      result.screenshots.push(await this.takeScreenshot('settings-main'));
      
      // Test settings tabs
      const settingsTabs = [
        'General',
        'Users',
        'Notifications',
        'Integrations',
        'Security',
        'Billing'
      ];
      
      for (const tab of settingsTabs) {
        const tabButton = await this.page!.$(`button:has-text("${tab}")`);
        if (tabButton) {
          await tabButton.click();
          await this.page!.waitForTimeout(1000);
          
          // Take screenshot
          result.screenshots.push(await this.takeScreenshot(`settings-${tab.toLowerCase()}`));
          
          console.log(`  ✓ ${tab} settings tab loads`);
        } else {
          result.errors.push(`${tab} settings tab not found`);
        }
      }
      
      // Test theme toggle
      const themeToggle = await this.page!.$('.theme-toggle');
      if (themeToggle) {
        await themeToggle.click();
        await this.page!.waitForTimeout(500);
        console.log('  ✓ Theme toggle works');
      }
      
    } catch (error: any) {
      result.status = 'fail';
      result.errors.push(error.message);
      console.error('  ❌ Settings test failed:', error.message);
    }

    this.results.push(result);
  }

  async generateReport() {
    console.log('\n\n📊 TEST RESULTS SUMMARY');
    console.log('========================\n');
    
    let totalPass = 0;
    let totalFail = 0;
    let allErrors: string[] = [];
    
    for (const result of this.results) {
      const status = result.status === 'pass' ? '✅' : '❌';
      console.log(`${status} ${result.section}: ${result.status.toUpperCase()}`);
      
      if (result.errors.length > 0) {
        console.log(`   Errors:`);
        result.errors.forEach(error => {
          console.log(`   - ${error}`);
          allErrors.push(`${result.section}: ${error}`);
        });
      }
      
      if (result.screenshots.length > 0) {
        console.log(`   Screenshots: ${result.screenshots.length} captured`);
      }
      
      if (result.status === 'pass') totalPass++;
      else totalFail++;
    }
    
    console.log('\n\nSUMMARY:');
    console.log(`Total Sections Tested: ${this.results.length}`);
    console.log(`Passed: ${totalPass}`);
    console.log(`Failed: ${totalFail}`);
    console.log(`Success Rate: ${((totalPass / this.results.length) * 100).toFixed(1)}%`);
    
    if (allErrors.length > 0) {
      console.log('\n\n❌ ALL ERRORS TO FIX:');
      allErrors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`);
      });
    }
    
    // Generate HTML report
    const reportHtml = this.generateHtmlReport();
    const fs = require('fs');
    fs.writeFileSync(`${SCREENSHOT_DIR}/test-report.html`, reportHtml);
    console.log(`\n📄 HTML report generated: ${SCREENSHOT_DIR}/test-report.html`);
  }

  private generateHtmlReport(): string {
    const timestamp = format(new Date(), 'yyyy-MM-dd HH:mm:ss');
    
    return `
<!DOCTYPE html>
<html>
<head>
  <title>Electrical App Test Report - ${timestamp}</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
    .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
    h1 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
    .summary { background: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0; }
    .pass { color: #28a745; }
    .fail { color: #dc3545; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; }
    .section h3 { margin-top: 0; }
    .errors { color: #dc3545; margin: 10px 0; }
    .screenshots { margin: 10px 0; }
    .screenshot-link { color: #007bff; text-decoration: none; margin-right: 10px; }
  </style>
</head>
<body>
  <div class="container">
    <h1>Electrical App Comprehensive Test Report</h1>
    <p>Generated: ${timestamp}</p>
    
    <div class="summary">
      <h2>Summary</h2>
      <p>Total Sections: ${this.results.length}</p>
      <p class="pass">Passed: ${this.results.filter(r => r.status === 'pass').length}</p>
      <p class="fail">Failed: ${this.results.filter(r => r.status === 'fail').length}</p>
      <p>Success Rate: ${((this.results.filter(r => r.status === 'pass').length / this.results.length) * 100).toFixed(1)}%</p>
    </div>
    
    <h2>Detailed Results</h2>
    ${this.results.map(result => `
      <div class="section">
        <h3 class="${result.status}">${result.status === 'pass' ? '✅' : '❌'} ${result.section}</h3>
        <p>Status: <strong class="${result.status}">${result.status.toUpperCase()}</strong></p>
        
        ${result.errors.length > 0 ? `
          <div class="errors">
            <strong>Errors:</strong>
            <ul>
              ${result.errors.map(error => `<li>${error}</li>`).join('')}
            </ul>
          </div>
        ` : ''}
        
        ${result.screenshots.length > 0 ? `
          <div class="screenshots">
            <strong>Screenshots:</strong>
            ${result.screenshots.map(screenshot => 
              `<a href="${screenshot}" class="screenshot-link" target="_blank">${screenshot.split('/').pop()}</a>`
            ).join('')}
          </div>
        ` : ''}
      </div>
    `).join('')}
  </div>
</body>
</html>
    `;
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
    }
  }

  async run() {
    try {
      await this.initialize();
      await this.login();
      
      // Run all tests
      await this.testDashboard();
      await this.testProjects();
      await this.testEstimates();
      await this.testCustomers();
      await this.testCalculations();
      await this.testReports();
      await this.testSettings();
      
      // Generate report
      await this.generateReport();
      
    } catch (error) {
      console.error('Fatal error during testing:', error);
    } finally {
      await this.cleanup();
    }
  }
}

// Run the comprehensive test
const tester = new ComprehensiveAppTester();
tester.run().catch(console.error);