package com.electricalcontractor.mobile;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.Build;
import android.util.Log;

/**
 * Helper class to configure React Native debug settings for Metro bundler connection
 * This approach is compatible with React Native 0.73
 */
public class ReactNativeDebugConfig {
    private static final String TAG = "ReactNativeDebugConfig";
    private static final String PREFS_NAME = "ReactNative";
    private static final String DEBUG_SERVER_HOST_KEY = "debug_http_host";
    
    /**
     * Initialize debug configuration for React Native
     * Must be called before React Native initialization
     */
    public static void initialize(Context context) {
        if (!BuildConfig.DEBUG) {
            return;
        }
        
        try {
            // Detect if running on emulator
            boolean isEmulator = isRunningOnEmulator();
            
            // Set the appropriate host
            String host = isEmulator ? "********:8081" : "localhost:8081";
            
            // Store in SharedPreferences where React Native expects it
            SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
            SharedPreferences.Editor editor = prefs.edit();
            editor.putString(DEBUG_SERVER_HOST_KEY, host);
            editor.apply();
            
            Log.i(TAG, "React Native debug server configured for " + 
                       (isEmulator ? "emulator" : "device") + ": " + host);
            
            // Also try to set system property (some React Native versions check this)
            try {
                System.setProperty("metro.host", host.split(":")[0]);
                System.setProperty("metro.port", "8081");
            } catch (Exception e) {
                // System properties might be restricted, that's OK
                Log.d(TAG, "Could not set system properties: " + e.getMessage());
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error initializing debug configuration", e);
        }
    }
    
    /**
     * Detect if the app is running on an emulator
     */
    private static boolean isRunningOnEmulator() {
        return Build.FINGERPRINT.startsWith("generic")
            || Build.FINGERPRINT.startsWith("unknown")
            || Build.MODEL.contains("google_sdk")
            || Build.MODEL.contains("Emulator")
            || Build.MODEL.contains("Android SDK built for x86")
            || Build.MANUFACTURER.contains("Genymotion")
            || Build.MANUFACTURER.contains("Google")
            || (Build.BRAND.startsWith("generic") && Build.DEVICE.startsWith("generic"))
            || "google_sdk".equals(Build.PRODUCT)
            || Build.HARDWARE.contains("goldfish")
            || Build.HARDWARE.contains("ranchu")
            || Build.PRODUCT.contains("sdk_google")
            || Build.PRODUCT.contains("google_sdk")
            || Build.PRODUCT.contains("sdk")
            || Build.PRODUCT.contains("sdk_x86")
            || Build.PRODUCT.contains("vbox86p")
            || Build.PRODUCT.contains("emulator")
            || Build.PRODUCT.contains("simulator");
    }
    
    /**
     * Get the current debug server host from preferences
     */
    public static String getDebugServerHost(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        return prefs.getString(DEBUG_SERVER_HOST_KEY, "localhost:8081");
    }
}