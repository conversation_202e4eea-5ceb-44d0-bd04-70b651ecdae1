import { NavLink } from 'react-router-dom';
import { X, Home, Users, Briefcase, FileText, Calculator, Package, Zap, BarChart3, FileBarChart, Settings, DollarSign } from 'lucide-react';
import { Dialog, Transition } from '@headlessui/react';
import { Fragment } from 'react';
import clsx from 'clsx';

interface SidebarProps {
  open: boolean;
  onClose: () => void;
}

const navigation = [
  { name: 'Dashboard', href: '/', icon: Home },
  { name: 'Customers', href: '/customers', icon: Users },
  { name: 'Projects', href: '/projects', icon: Briefcase },
  { name: 'Quotes', href: '/quotes', icon: DollarSign },
  { name: 'Estimates', href: '/estimates', icon: FileText },
  { name: 'Calculations', href: '/calculations', icon: Calculator },
  { name: 'Materials', href: '/materials', icon: Package },
  { name: 'Analytics', href: '/analytics', icon: Bar<PERSON>hart3 },
  { name: 'Reports', href: '/reports', icon: File<PERSON>ar<PERSON><PERSON> },
  { name: 'Settings', href: '/settings', icon: Settings },
];

export function Sidebar({ open, onClose }: SidebarProps) {
  const sidebarContent = (
    <>
      <div className="flex items-center justify-between px-4 pt-5 pb-4">
        <div className="flex items-center">
          <Zap className="h-8 w-8 text-electrical-500" />
          <span className="ml-2 text-xl font-bold text-gray-900 dark:text-white">
            ElectricalPro
          </span>
        </div>
        <button
          type="button"
          className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700"
          onClick={onClose}
        >
          <X className="h-5 w-5" />
        </button>
      </div>
      
      <nav className="mt-5 px-2 space-y-1">
        {navigation.map((item) => (
          <NavLink
            key={item.name}
            to={item.href}
            className={({ isActive }) =>
              clsx(
                isActive
                  ? 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white'
                  : 'text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white',
                'group flex items-center px-2 py-2 text-sm font-medium rounded-md'
              )
            }
            onClick={onClose}
          >
            <item.icon
              className={clsx(
                'mr-3 flex-shrink-0 h-5 w-5'
              )}
            />
            {item.name}
          </NavLink>
        ))}
      </nav>
      
      <div className="absolute bottom-0 left-0 right-0 p-4">
        <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-4">
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Need Help?
          </h3>
          <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
            Contact support or check the documentation for assistance.
          </p>
          <button className="mt-2 text-xs font-medium text-primary-600 dark:text-primary-400 hover:text-primary-500">
            Get Support →
          </button>
        </div>
      </div>
    </>
  );

  return (
    <>
      {/* Mobile sidebar */}
      <Transition.Root show={open} as={Fragment}>
        <Dialog as="div" className="relative z-50 lg:hidden" onClose={onClose}>
          <Transition.Child
            as={Fragment}
            enter="transition-opacity ease-linear duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="transition-opacity ease-linear duration-300"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-900/80" />
          </Transition.Child>

          <div className="fixed inset-0 flex">
            <Transition.Child
              as={Fragment}
              enter="transition ease-in-out duration-300 transform"
              enterFrom="-translate-x-full"
              enterTo="translate-x-0"
              leave="transition ease-in-out duration-300 transform"
              leaveFrom="translate-x-0"
              leaveTo="-translate-x-full"
            >
              <Dialog.Panel className="relative mr-16 flex w-full max-w-xs flex-1">
                <div className="flex grow flex-col bg-white dark:bg-gray-800 overflow-y-auto">
                  {sidebarContent}
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </Dialog>
      </Transition.Root>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-64 lg:flex-col">
        <div className="flex grow flex-col bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 overflow-y-auto">
          {sidebarContent}
        </div>
      </div>
    </>
  );
}