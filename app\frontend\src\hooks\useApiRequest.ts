import { useState, useCallback, useRef } from 'react';
import { AxiosError } from 'axios';
import { errorService } from '../services/errorService';
import { ErrorHandler } from '../utils/error-handler';
import { requestThrottler } from '../utils/request-throttler';
import { useToast } from './useToast';

interface UseApiRequestOptions {
  showErrorToast?: boolean;
  retryOnError?: boolean;
  maxRetries?: number;
  onSuccess?: (data: any) => void;
  onError?: (error: Error) => void;
}

interface UseApiRequestResult<T> {
  data: T | null;
  loading: boolean;
  error: Error | null;
  execute: (...args: any[]) => Promise<T | null>;
  reset: () => void;
}

export function useApiRequest<T>(
  requestFn: (...args: any[]) => Promise<T>,
  options: UseApiRequestOptions = {}
): UseApiRequestResult<T> {
  const {
    showErrorToast = true,
    retryOnError = true,
    maxRetries = 3,
    onSuccess,
    onError,
  } = options;

  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const { showToast } = useToast();
  const abortControllerRef = useRef<AbortController | null>(null);

  const execute = useCallback(async (...args: any[]): Promise<T | null> => {
    // Cancel any pending request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController();
    
    setLoading(true);
    setError(null);

    try {
      // Execute request with retry logic
      const result = await executeWithRetry(
        () => requestFn(...args),
        {
          maxRetries: retryOnError ? maxRetries : 0,
          signal: abortControllerRef.current.signal,
        }
      );

      setData(result);
      
      if (onSuccess) {
        onSuccess(result);
      }

      return result;
    } catch (err) {
      const error = err as Error;
      
      // Don't set error state if request was cancelled
      if (error.name === 'AbortError') {
        return null;
      }

      setError(error);
      
      if (showErrorToast) {
        const apiError = ErrorHandler.handleApiError(error, {
          showNotification: false,
        });
        showToast({
          title: 'Error',
          description: apiError.message,
          variant: 'error',
        });
      }

      if (onError) {
        onError(error);
      }

      return null;
    } finally {
      setLoading(false);
      abortControllerRef.current = null;
    }
  }, [requestFn, retryOnError, maxRetries, showErrorToast, onSuccess, onError, showToast]);

  const reset = useCallback(() => {
    setData(null);
    setError(null);
    setLoading(false);
    
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
  }, []);

  return {
    data,
    loading,
    error,
    execute,
    reset,
  };
}

/**
 * Execute request with retry logic
 */
async function executeWithRetry<T>(
  requestFn: () => Promise<T>,
  options: {
    maxRetries: number;
    signal?: AbortSignal;
  }
): Promise<T> {
  const { maxRetries, signal } = options;
  let lastError: Error | null = null;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      // Check if request was cancelled
      if (signal?.aborted) {
        throw new Error('Request cancelled');
      }

      // Execute through throttler
      const result = await requestThrottler.throttle(requestFn);
      
      // Success - adjust throttler to allow more requests
      if (attempt > 0) {
        requestThrottler.adjustThrottle('increase');
      }
      
      return result;
    } catch (error) {
      lastError = error as Error;
      
      // Don't retry if request was cancelled
      if (signal?.aborted || lastError.name === 'AbortError') {
        throw lastError;
      }

      // Check if error is retriable
      if (!ErrorHandler.isRetriableError(error) || attempt === maxRetries) {
        throw lastError;
      }

      // Calculate retry delay
      const isRateLimit = error instanceof AxiosError && error.response?.status === 429;
      const baseDelay = isRateLimit ? 2000 : 1000;
      const delay = Math.min(
        baseDelay * Math.pow(2, attempt) + Math.random() * 1000,
        30000
      );

      errorService.logWarning('Request failed, retrying', {
        attempt: attempt + 1,
        maxAttempts: maxRetries + 1,
        delay,
        error: lastError.message,
      });

      // Wait before retry
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError || new Error('Request failed');
}

/**
 * Hook for paginated API requests
 */
export function usePaginatedApiRequest<T>(
  requestFn: (page: number, pageSize: number) => Promise<{ data: T[]; total: number }>,
  pageSize = 20
) {
  const [items, setItems] = useState<T[]>([]);
  const [totalItems, setTotalItems] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  const { data, loading, error, execute } = useApiRequest(requestFn, {
    onSuccess: (result) => {
      if (result) {
        setItems(prev => currentPage === 1 ? result.data : [...prev, ...result.data]);
        setTotalItems(result.total);
        setHasMore(items.length + result.data.length < result.total);
      }
    },
  });

  const loadMore = useCallback(async () => {
    if (!loading && hasMore) {
      await execute(currentPage, pageSize);
      setCurrentPage(prev => prev + 1);
    }
  }, [currentPage, pageSize, loading, hasMore, execute]);

  const refresh = useCallback(async () => {
    setItems([]);
    setCurrentPage(1);
    setHasMore(true);
    await execute(1, pageSize);
  }, [pageSize, execute]);

  return {
    items,
    totalItems,
    loading,
    error,
    hasMore,
    loadMore,
    refresh,
  };
}