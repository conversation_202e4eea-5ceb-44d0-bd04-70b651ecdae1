import { GoogleGenAI } from '@google/genai';

interface ValidationResult {
  status: 'PASS' | 'FAIL' | 'WARNING';
  errors: ValidationError[];
  warnings: string[];
  suggestions: string[];
  necViolations: NECViolation[];
  safetyScore: number; // 0-100
}

interface ValidationError {
  field: string;
  message: string;
  severity: 'HIGH' | 'MEDIUM' | 'LOW';
  suggestedValue?: any;
}

interface NECViolation {
  article: string;
  section: string;
  description: string;
  correction: string;
}

interface CorrectionSuggestion {
  field: string;
  currentValue: any;
  suggestedValue: any;
  reason: string;
  necReference?: string;
}

export class CalculationValidatorService {
  private genAI: GoogleGenAI;
  private model: any;

  constructor() {
    if (!process.env.GEMINI_API_KEY) {
      throw new Error('GEMINI_API_KEY environment variable is required');
    }
    
    this.genAI = new GoogleGenAI({ apiKey: process.env.GEMINI_API_KEY });
    this.model = this.genAI.models;
  }

  async validateCalculation(
    type: string, 
    input: any, 
    output: any
  ): Promise<ValidationResult> {
    try {
      const validationPrompt = `
      You are an expert electrical engineer and NEC code inspector. 
      Validate this electrical calculation for accuracy and code compliance.
      
      Calculation Type: ${type}
      Input Parameters: ${JSON.stringify(input, null, 2)}
      Calculation Output: ${JSON.stringify(output, null, 2)}
      
      Perform these checks:
      1. Mathematical accuracy - verify all calculations are correct
      2. NEC 2023 compliance - check against current code requirements
      3. Safety margins - ensure adequate safety factors are applied
      4. Common mistakes - identify typical errors for this calculation type
      5. Edge cases - check for unusual conditions that might cause issues
      
      For ${type} calculations, specifically verify:
      ${this.getTypeSpecificChecks(type)}
      
      Return a detailed JSON validation report with:
      {
        "status": "PASS|FAIL|WARNING",
        "errors": [
          {
            "field": "field_name",
            "message": "description of error",
            "severity": "HIGH|MEDIUM|LOW",
            "suggestedValue": corrected_value
          }
        ],
        "warnings": ["list of warnings"],
        "suggestions": ["improvement suggestions"],
        "necViolations": [
          {
            "article": "NEC article number",
            "section": "specific section",
            "description": "violation description",
            "correction": "how to fix"
          }
        ],
        "safetyScore": 0-100
      }
      `;

      const response = await this.model.generateContent({
        model: 'gemini-2.0-flash-001',
        contents: validationPrompt,
        generationConfig: {
          responseFormat: 'json',
          temperature: 0.1 // Low temperature for consistent validation
        }
      });

      const validation = JSON.parse(response.text);

      // Log issues for tracking
      if (validation.status !== 'PASS') {
        await this.logValidationIssue(type, input, output, validation);
      }

      return validation;
    } catch (error) {
      console.error('Validation error:', error);
      return {
        status: 'WARNING',
        errors: [],
        warnings: ['Validation service temporarily unavailable'],
        suggestions: [],
        necViolations: [],
        safetyScore: 75
      };
    }
  }

  async suggestCorrections(
    error: any, 
    context: any
  ): Promise<CorrectionSuggestion[]> {
    const correctionPrompt = `
    As an electrical engineering expert, analyze this calculation error and suggest corrections.
    
    Error Details: ${JSON.stringify(error, null, 2)}
    Calculation Context: ${JSON.stringify(context, null, 2)}
    
    Consider:
    1. NEC 2023 requirements
    2. Industry best practices
    3. Safety factors
    4. Common field conditions
    
    Provide specific, actionable corrections with:
    {
      "corrections": [
        {
          "field": "parameter name",
          "currentValue": current_value,
          "suggestedValue": corrected_value,
          "reason": "explanation",
          "necReference": "Article.Section if applicable"
        }
      ]
    }
    `;

    try {
      const response = await this.model.generateContent({
        model: 'gemini-2.0-flash-001',
        contents: correctionPrompt,
        generationConfig: {
          responseFormat: 'json'
        }
      });

      const result = JSON.parse(response.text);
      return result.corrections;
    } catch (error) {
      console.error('Correction suggestion error:', error);
      return [];
    }
  }

  async validateSafety(
    calculationType: string,
    result: any
  ): Promise<{
    safe: boolean;
    concerns: string[];
    recommendations: string[];
  }> {
    const safetyPrompt = `
    Evaluate the safety implications of this electrical calculation result.
    
    Type: ${calculationType}
    Result: ${JSON.stringify(result, null, 2)}
    
    Check for:
    1. Adequate safety margins
    2. Potential hazards
    3. Equipment stress levels
    4. Environmental factors
    5. Future expansion considerations
    
    Return:
    {
      "safe": true/false,
      "concerns": ["list of safety concerns"],
      "recommendations": ["safety improvements"]
    }
    `;

    try {
      const response = await this.model.generateContent({
        model: 'gemini-2.0-flash-001',
        contents: safetyPrompt,
        generationConfig: {
          responseFormat: 'json'
        }
      });

      return JSON.parse(response.text);
    } catch (error) {
      console.error('Safety validation error:', error);
      return {
        safe: true,
        concerns: [],
        recommendations: ['Manual safety review recommended']
      };
    }
  }

  private getTypeSpecificChecks(type: string): string {
    const checks: Record<string, string> = {
      LOAD_CALCULATION: `
        - Demand factors per NEC Table 220.42
        - Service conductor sizing per 230.42
        - Neutral sizing per 220.61
        - Future load provisions per 220.14
      `,
      WIRE_SIZE: `
        - Ampacity tables 310.16 through 310.21
        - Temperature corrections per 310.15(B)(1)
        - Bundling derating per 310.15(C)(1)
        - Terminal temperature ratings per 110.14(C)
      `,
      VOLTAGE_DROP: `
        - 3% branch circuit limit (recommended)
        - 5% total feeder + branch limit
        - Conductor resistance values
        - Power factor considerations
      `,
      CONDUIT_FILL: `
        - Chapter 9 Table 1 fill percentages
        - Jam ratio for 3 conductors
        - Wire area calculations per Table 5
        - Conduit area per Table 4
      `,
      SHORT_CIRCUIT: `
        - Equipment interrupting ratings
        - Series rating compliance
        - Bus bracing adequacy
        - Selective coordination
      `,
      ARC_FLASH: `
        - IEEE 1584-2018 methodology
        - Working distance standards
        - PPE categories per NFPA 70E
        - Boundary calculations
      `
    };

    return checks[type] || 'General electrical safety and code compliance';
  }

  private async logValidationIssue(
    type: string,
    input: any,
    output: any,
    validation: ValidationResult
  ): Promise<void> {
    // Log to database for analysis and improvement
    try {
      const { prisma } = await import('../../database/prisma');
      
      await prisma.calculationLog.create({
        data: {
          calculation_type: type,
          input_data: JSON.stringify(input),
          output_data: JSON.stringify(output),
          validation_result: JSON.stringify(validation),
          validation_status: validation.status,
          created_at: new Date()
        }
      });
    } catch (error) {
      console.error('Failed to log validation issue:', error);
    }
  }

  // Real-time validation during data entry
  async validateInput(
    calculationType: string,
    field: string,
    value: any,
    context: any
  ): Promise<{
    valid: boolean;
    message?: string;
    suggestion?: any;
  }> {
    const fieldValidationPrompt = `
    Validate this input field for an electrical calculation.
    
    Calculation Type: ${calculationType}
    Field: ${field}
    Value: ${value}
    Context: ${JSON.stringify(context)}
    
    Check if the value is:
    1. Within reasonable ranges
    2. Compliant with NEC requirements
    3. Consistent with other parameters
    
    Return:
    {
      "valid": true/false,
      "message": "explanation if invalid",
      "suggestion": suggested_value if applicable
    }
    `;

    try {
      const response = await this.model.generateContent({
        model: 'gemini-2.0-flash-001',
        contents: fieldValidationPrompt,
        generationConfig: {
          responseFormat: 'json'
        }
      });

      return JSON.parse(response.text);
    } catch (error) {
      // Fail open - don't block user input if validation fails
      return { valid: true };
    }
  }
}