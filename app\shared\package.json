{"name": "@electrical/shared", "version": "1.0.0", "description": "Shared types and utilities for electrical contracting application", "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint 'src/**/*.ts' --ignore-pattern '*.d.ts'", "typecheck": "tsc --noEmit"}, "dependencies": {"decimal.js": "^10.6.0", "zod": "^3.22.4"}, "devDependencies": {"@types/decimal.js": "^7.4.3", "@types/node": "^20.11.5", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.57.1", "typescript": "^5.3.3"}}