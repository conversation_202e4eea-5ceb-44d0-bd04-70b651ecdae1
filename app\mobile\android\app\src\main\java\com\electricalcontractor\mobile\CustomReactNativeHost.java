package com.electricalcontractor.mobile;

import android.app.Application;
import com.facebook.react.PackageList;
import com.facebook.react.ReactPackage;
import com.facebook.react.defaults.DefaultReactNativeHost;
import java.util.List;
import android.os.Build;
import android.util.Log;

/**
 * Custom ReactNativeHost that properly configures debug server for React Native 0.73
 */
public class CustomReactNativeHost extends DefaultReactNativeHost {
    private static final String TAG = "CustomReactNativeHost";
    
    public CustomReactNativeHost(Application application) {
        super(application);
    }

    @Override
    public boolean getUseDeveloperSupport() {
        return BuildConfig.DEBUG;
    }

    @Override
    protected List<ReactPackage> getPackages() {
        @SuppressWarnings("UnnecessaryLocalVariable")
        List<ReactPackage> packages = new PackageList(this).getPackages();
        // Packages that cannot be autolinked yet can be added manually here
        return packages;
    }

    @Override
    protected String getJSMainModuleName() {
        return "index";
    }

    @Override
    protected boolean isNewArchEnabled() {
        return BuildConfig.IS_NEW_ARCHITECTURE_ENABLED;
    }

    @Override
    protected Boolean isHermesEnabled() {
        return BuildConfig.IS_HERMES_ENABLED;
    }
    
    @Override
    protected String getJSBundleFile() {
        // In debug mode, we want to load from the dev server
        if (BuildConfig.DEBUG) {
            return null; // This tells React Native to load from dev server
        }
        return super.getJSBundleFile();
    }
    
    /**
     * Get the debug server URL based on whether we're running on emulator or device
     */
    private String getDebugServerUrl() {
        boolean isEmulator = Build.FINGERPRINT.startsWith("generic")
                || Build.FINGERPRINT.startsWith("unknown")
                || Build.MODEL.contains("google_sdk")
                || Build.MODEL.contains("Emulator")
                || Build.MODEL.contains("Android SDK built for x86")
                || Build.MANUFACTURER.contains("Genymotion")
                || (Build.BRAND.startsWith("generic") && Build.DEVICE.startsWith("generic"))
                || "google_sdk".equals(Build.PRODUCT);
                
        String host = isEmulator ? "********" : "localhost";
        String url = host + ":8081";
        
        Log.d(TAG, "Debug server URL: " + url);
        return url;
    }
}