import { sqliteConnection, buildWhereClause } from './sqliteConnection';
import {
  Project,
  Panel,
  Circuit,
  Material,
  Calculation,
  Photo,
  SyncQueue,
} from './entities';
import { Repository, FindManyOptions, FindOneOptions, DeepPartial } from './mockTypeorm';

// Map entity classes to table names
const ENTITY_TABLE_MAP: Record<string, string> = {
  Project: 'projects',
  Panel: 'panels',
  Circuit: 'circuits',
  Material: 'materials',
  Calculation: 'calculations',
  Photo: 'photos',
  SyncQueue: 'sync_queue',
};

// SQLite-based Repository implementation
class SQLiteRepository<T> implements Repository<T> {
  private tableName: string;

  constructor(entityClass: any) {
    this.tableName = ENTITY_TABLE_MAP[entityClass.name] || entityClass.name.toLowerCase() + 's';
  }

  async find(options?: FindManyOptions<T>): Promise<T[]> {
    const { sql: whereClause, params: whereParams } = buildWhereClause(options?.where);
    
    let query = `SELECT * FROM ${this.tableName} ${whereClause}`;
    const params = [...whereParams];

    // Add ORDER BY
    if (options?.order) {
      const orderClauses = Object.entries(options.order)
        .map(([field, direction]) => `${field} ${direction}`)
        .join(', ');
      query += ` ORDER BY ${orderClauses}`;
    }

    // Add LIMIT and OFFSET
    if (options?.take) {
      query += ` LIMIT ${options.take}`;
    }
    if (options?.skip) {
      query += ` OFFSET ${options.skip}`;
    }

    const rows = await sqliteConnection.executeQuery<T>(query, params);
    
    // Parse JSON fields
    return rows.map(row => this.parseRow(row));
  }

  async findOne(options?: FindOneOptions<T>): Promise<T | null> {
    const results = await this.find({ ...options, take: 1 } as FindManyOptions<T>);
    return results[0] || null;
  }

  async save(entity: T | T[]): Promise<T | T[]> {
    if (Array.isArray(entity)) {
      const results: T[] = [];
      for (const item of entity) {
        results.push(await this.saveOne(item));
      }
      return results;
    }
    return this.saveOne(entity);
  }

  private async saveOne(entity: T): Promise<T> {
    const data = entity as any;
    const id = data.id;

    if (!id) {
      throw new Error('Entity must have an id');
    }

    // Check if entity exists
    const existing = await this.findOne({ where: { id } } as FindOneOptions<T>);

    if (existing) {
      // Update
      const fields = Object.keys(data).filter(key => key !== 'id');
      const setClause = fields.map(field => `${field} = ?`).join(', ');
      const values = fields.map(field => this.serializeValue(data[field]));
      
      await sqliteConnection.executeUpdate(
        `UPDATE ${this.tableName} SET ${setClause} WHERE id = ?`,
        [...values, id]
      );
    } else {
      // Insert
      const fields = Object.keys(data);
      const placeholders = fields.map(() => '?').join(', ');
      const values = fields.map(field => this.serializeValue(data[field]));
      
      await sqliteConnection.executeUpdate(
        `INSERT INTO ${this.tableName} (${fields.join(', ')}) VALUES (${placeholders})`,
        values
      );
    }

    return entity;
  }

  async remove(entity: T | T[]): Promise<T | T[]> {
    if (Array.isArray(entity)) {
      for (const item of entity) {
        await this.removeOne(item);
      }
      return entity;
    }
    return this.removeOne(entity);
  }

  private async removeOne(entity: T): Promise<T> {
    const data = entity as any;
    if (data.id) {
      await sqliteConnection.executeUpdate(
        `DELETE FROM ${this.tableName} WHERE id = ?`,
        [data.id]
      );
    }
    return entity;
  }

  create(data: DeepPartial<T>): T {
    // Simply return the data as-is, actual persistence happens in save()
    return data as T;
  }

  async update(criteria: any, partialEntity: any): Promise<any> {
    const { sql: whereClause, params: whereParams } = buildWhereClause(criteria);
    
    const fields = Object.keys(partialEntity);
    const setClause = fields.map(field => `${field} = ?`).join(', ');
    const values = fields.map(field => this.serializeValue(partialEntity[field]));
    
    const affected = await sqliteConnection.executeUpdate(
      `UPDATE ${this.tableName} SET ${setClause} ${whereClause}`,
      [...values, ...whereParams]
    );
    
    return { affected };
  }

  async delete(criteria: any): Promise<any> {
    const { sql: whereClause, params } = buildWhereClause(criteria);
    
    const affected = await sqliteConnection.executeUpdate(
      `DELETE FROM ${this.tableName} ${whereClause}`,
      params
    );
    
    return { affected };
  }

  async count(options?: FindManyOptions<T>): Promise<number> {
    const { sql: whereClause, params } = buildWhereClause(options?.where);
    
    const result = await sqliteConnection.executeQuery<{ count: number }>(
      `SELECT COUNT(*) as count FROM ${this.tableName} ${whereClause}`,
      params
    );
    
    return result[0]?.count || 0;
  }

  createQueryBuilder(alias?: string) {
    const qb = new SQLiteQueryBuilder(this.tableName, alias || this.tableName);
    return qb;
  }

  private parseRow(row: any): T {
    const parsed = { ...row };
    
    // Parse JSON fields
    ['metadata', 'parameters', 'results', 'conflictData'].forEach(field => {
      if (parsed[field] && typeof parsed[field] === 'string') {
        try {
          parsed[field] = JSON.parse(parsed[field]);
        } catch (e) {
          // Keep as string if parsing fails
        }
      }
    });
    
    // Convert boolean fields
    ['isDeleted'].forEach(field => {
      if (field in parsed) {
        parsed[field] = Boolean(parsed[field]);
      }
    });
    
    return parsed as T;
  }

  private serializeValue(value: any): any {
    if (value === undefined) {
      return null;
    }
    if (typeof value === 'object' && value !== null && !(value instanceof Date)) {
      return JSON.stringify(value);
    }
    if (value instanceof Date) {
      return value.toISOString();
    }
    if (typeof value === 'boolean') {
      return value ? 1 : 0;
    }
    return value;
  }
}

// Simple query builder for SQLite
class SQLiteQueryBuilder {
  private tableName: string;
  private alias: string;
  private conditions: string[] = [];
  private params: any[] = [];
  private orderByClause: string = '';
  private limitValue?: number;
  private selectFields: string[] = ['*'];
  private joinClauses: string[] = [];
  private groupByClause: string = '';

  constructor(tableName: string, alias: string) {
    this.tableName = tableName;
    this.alias = alias;
  }

  where(condition: string, parameters?: any): this {
    this.conditions.push(condition);
    if (parameters) {
      Object.values(parameters).forEach(value => this.params.push(value));
    }
    return this;
  }

  andWhere(condition: string, parameters?: any): this {
    return this.where(condition, parameters);
  }

  orderBy(sort: string, order?: 'ASC' | 'DESC'): this {
    this.orderByClause = `ORDER BY ${sort} ${order || 'ASC'}`;
    return this;
  }

  addOrderBy(sort: string, order?: 'ASC' | 'DESC'): this {
    if (this.orderByClause) {
      this.orderByClause += `, ${sort} ${order || 'ASC'}`;
    } else {
      this.orderBy(sort, order);
    }
    return this;
  }

  leftJoinAndSelect(property: string, alias: string): this {
    // Simplified join - would need more sophisticated implementation for real joins
    this.joinClauses.push(`LEFT JOIN ${property} ${alias}`);
    return this;
  }

  limit(limit: number): this {
    this.limitValue = limit;
    return this;
  }

  select(selection: string | string[], alias?: string): this {
    if (Array.isArray(selection)) {
      this.selectFields = selection;
    } else {
      this.selectFields = [selection];
    }
    return this;
  }

  addSelect(selection: string | string[], alias?: string): this {
    if (Array.isArray(selection)) {
      this.selectFields.push(...selection);
    } else {
      this.selectFields.push(selection);
    }
    return this;
  }

  groupBy(group: string): this {
    this.groupByClause = `GROUP BY ${group}`;
    return this;
  }

  private buildQuery(): string {
    let query = `SELECT ${this.selectFields.join(', ')} FROM ${this.tableName} ${this.alias}`;
    
    if (this.joinClauses.length > 0) {
      query += ' ' + this.joinClauses.join(' ');
    }
    
    if (this.conditions.length > 0) {
      query += ' WHERE ' + this.conditions.join(' AND ');
    }
    
    if (this.groupByClause) {
      query += ' ' + this.groupByClause;
    }
    
    if (this.orderByClause) {
      query += ' ' + this.orderByClause;
    }
    
    if (this.limitValue) {
      query += ` LIMIT ${this.limitValue}`;
    }
    
    return query;
  }

  async getRawMany(): Promise<any[]> {
    const query = this.buildQuery();
    return sqliteConnection.executeQuery(query, this.params);
  }

  async getMany(): Promise<any[]> {
    return this.getRawMany();
  }

  async getOne(): Promise<any | null> {
    this.limit(1);
    const results = await this.getMany();
    return results[0] || null;
  }

  async getCount(): Promise<number> {
    const countQuery = `SELECT COUNT(*) as count FROM ${this.tableName} ${this.alias}`;
    let query = countQuery;
    
    if (this.conditions.length > 0) {
      query += ' WHERE ' + this.conditions.join(' AND ');
    }
    
    const result = await sqliteConnection.executeQuery<{ count: number }>(query, this.params);
    return result[0]?.count || 0;
  }
}

// DataSource implementation using SQLite
class SQLiteDataSource {
  isInitialized = false;

  async initialize() {
    await sqliteConnection.initialize();
    this.isInitialized = true;
    console.log('[SQLite] Database initialized');
  }

  async destroy() {
    await sqliteConnection.close();
    this.isInitialized = false;
    console.log('[SQLite] Database connection closed');
  }

  async dropDatabase() {
    await sqliteConnection.clearDatabase();
    console.log('[SQLite] Database dropped');
  }

  async synchronize() {
    // Tables are created automatically in sqliteConnection.initialize()
    console.log('[SQLite] Database synchronized');
  }

  getRepository<T>(entity: any): Repository<T> {
    return new SQLiteRepository<T>(entity);
  }
}

// Type assertion to match TypeORM's DataSource interface
type DataSource = SQLiteDataSource & {
  [key: string]: any;
};

let dataSource: DataSource | null = null;

export const getDatabaseConnection = async (): Promise<DataSource> => {
  if (dataSource?.isInitialized) {
    return dataSource;
  }

  // Create SQLite database connection
  dataSource = new SQLiteDataSource() as DataSource;
  await dataSource.initialize();
  
  return dataSource;
};

export const closeDatabaseConnection = async () => {
  if (dataSource?.isInitialized) {
    await dataSource.destroy();
    dataSource = null;
  }
};

export const clearDatabase = async () => {
  const ds = await getDatabaseConnection();
  await ds.dropDatabase();
  await ds.synchronize();
};