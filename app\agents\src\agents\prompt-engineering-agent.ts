import { BaseAgent, BaseAgentConfig, AgentCapability } from '../base/base-agent';
import { z } from 'zod';

// Prompt optimization schemas
const optimizePromptSchema = z.object({
  prompt: z.string(),
  model: z.enum(['claude', 'gpt', 'gemini']).default('claude'),
  context: z.object({
    projectType: z.string().optional(),
    voltage: z.string().optional(),
    codes: z.array(z.string()).optional(),
    safetyCritical: z.boolean().default(true),
  }).optional(),
  optimizationGoals: z.array(z.enum(['token-efficiency', 'accuracy', 'safety', 'clarity'])).optional(),
});

const generatePromptSchema = z.object({
  task: z.string(),
  taskType: z.enum(['calculation', 'code-generation', 'analysis', 'estimation', 'validation']),
  requirements: z.array(z.string()).optional(),
  constraints: z.array(z.string()).optional(),
  targetModel: z.enum(['claude', 'gpt', 'gemini']).default('claude'),
});

const validateSafetySchema = z.object({
  prompt: z.string(),
  taskType: z.string(),
  riskLevel: z.enum(['low', 'medium', 'high', 'critical']).default('high'),
});

const manageContextSchema = z.object({
  fullContext: z.string(),
  maxTokens: z.number().default(200000),
  priority: z.array(z.string()).optional(),
  taskType: z.string(),
});

const generateExamplesSchema = z.object({
  taskType: z.enum(['voltage-drop', 'load-calculation', 'wire-sizing', 'motor-control', 'arc-flash']),
  count: z.number().min(1).max(10).default(3),
  complexity: z.enum(['basic', 'intermediate', 'advanced']).default('intermediate'),
});

const analyzeTokensSchema = z.object({
  prompt: z.string(),
  model: z.enum(['claude', 'gpt', 'gemini']).default('claude'),
  includeOptimizations: z.boolean().default(true),
});

// Safety keywords for electrical domain
const REQUIRED_SAFETY_KEYWORDS = [
  'NEC', 'safety', 'verify', 'validate', 'check limits',
  'error handling', 'bounds checking', 'decimal precision'
];

// Model-specific token costs (approximate)
const TOKEN_COSTS = {
  claude: { input: 0.008, output: 0.024 },
  gpt: { input: 0.01, output: 0.03 },
  gemini: { input: 0.0075, output: 0.03 },
};

export class PromptEngineeringAgent extends BaseAgent {
  private promptTemplates: Map<string, string>;
  private necReferences: Map<string, string>;
  private exampleCache: Map<string, any[]>;

  constructor(config: Omit<BaseAgentConfig, 'capabilities'>) {
    const capabilities: AgentCapability[] = [
      {
        name: 'optimize-prompt',
        description: 'Optimize existing prompts for efficiency and accuracy',
        inputSchema: optimizePromptSchema,
      },
      {
        name: 'generate-prompt',
        description: 'Generate new prompts for electrical tasks',
        inputSchema: generatePromptSchema,
      },
      {
        name: 'validate-safety',
        description: 'Validate prompts include necessary safety checks',
        inputSchema: validateSafetySchema,
      },
      {
        name: 'manage-context',
        description: 'Manage long context situations efficiently',
        inputSchema: manageContextSchema,
      },
      {
        name: 'generate-examples',
        description: 'Generate electrical domain examples for few-shot learning',
        inputSchema: generateExamplesSchema,
      },
      {
        name: 'analyze-tokens',
        description: 'Analyze and optimize token usage',
        inputSchema: analyzeTokensSchema,
      },
    ];

    super({
      ...config,
      capabilities,
    });

    // Initialize Maps in constructor to avoid transpilation issues
    this.promptTemplates = new Map();
    this.necReferences = new Map();
    this.exampleCache = new Map();
  }

  protected async onInitialize(): Promise<void> {
    // Initialize prompt templates
    this.initializeTemplates();
    
    // Initialize NEC references
    this.initializeNECReferences();

    // Load cached examples
    await this.loadExampleCache();

    await this.log('Prompt Engineering agent initialized', {
      level: 'info',
      templates: this.promptTemplates.size,
      necReferences: this.necReferences.size,
    });
  }

  protected async processTask(taskType: string, data: any): Promise<any> {
    switch (taskType) {
      case 'optimize-prompt':
        return this.optimizePrompt(data);
      case 'generate-prompt':
        return this.generatePrompt(data);
      case 'validate-safety':
        return this.validateSafety(data);
      case 'manage-context':
        return this.manageContext(data);
      case 'generate-examples':
        return this.generateExamples(data);
      case 'analyze-tokens':
        return this.analyzeTokens(data);
      default:
        throw new Error(`Unknown task type: ${taskType}`);
    }
  }

  // Optimize existing prompts
  private async optimizePrompt(data: z.infer<typeof optimizePromptSchema>): Promise<any> {
    const { prompt, model, context, optimizationGoals = ['token-efficiency', 'accuracy'] } = data;

    let optimized = prompt;
    const improvements = [];
    let tokenReduction = 0;

    // Add electrical context if provided
    if (context) {
      optimized = this.injectElectricalContext(optimized, context);
      improvements.push('Added electrical context for domain specificity');
    }

    // Add NEC references
    const necRefs = this.extractRelevantNECReferences(prompt);
    if (necRefs.length > 0 && !prompt.includes('NEC')) {
      optimized = this.addNECReferences(optimized, necRefs);
      improvements.push(`Added ${necRefs.length} relevant NEC references`);
    }

    // Add precision requirements
    if (!prompt.toLowerCase().includes('decimal') && !prompt.toLowerCase().includes('precision')) {
      optimized = this.addPrecisionRequirements(optimized);
      improvements.push('Added decimal precision requirements');
    }

    // Add safety validations
    const safetyResult = await this.validateSafety({ prompt: optimized, taskType: 'general', riskLevel: 'high' });
    if (!safetyResult.isSafe) {
      optimized = this.addSafetyValidations(optimized, safetyResult.missingSafetyElements);
      improvements.push('Added required safety validations');
    }

    // Optimize for token efficiency
    if (optimizationGoals.includes('token-efficiency')) {
      const tokenOptResult = this.optimizeForTokens(optimized, model);
      optimized = tokenOptResult.optimized;
      tokenReduction = tokenOptResult.reduction;
      improvements.push(`Reduced tokens by ${tokenReduction}%`);
    }

    // Add model-specific optimizations
    optimized = this.applyModelSpecificOptimizations(optimized, model);
    improvements.push(`Applied ${model}-specific optimizations`);

    // Calculate metrics
    const originalTokens = this.estimateTokens(prompt);
    const optimizedTokens = this.estimateTokens(optimized);

    // Store optimization pattern
    await this.storeKnowledge(
      {
        originalLength: prompt.length,
        optimizedLength: optimized.length,
        improvements: improvements.length,
        model,
        context,
      },
      ['optimization', 'prompt', model],
      0.7
    );

    return {
      original: prompt,
      optimized,
      improvements,
      metrics: {
        originalTokens,
        optimizedTokens,
        tokenReduction: Math.round(((originalTokens - optimizedTokens) / originalTokens) * 100),
        estimatedCostSavings: this.calculateCostSavings(originalTokens, optimizedTokens, model),
        safetyScore: safetyResult.safetyScore || 1.0,
      },
    };
  }

  // Generate new prompts
  private async generatePrompt(data: z.infer<typeof generatePromptSchema>): Promise<any> {
    const { task, taskType, requirements = [], constraints = [], targetModel } = data;

    let template = this.promptTemplates.get(`${taskType}-${targetModel}`) || 
                   this.promptTemplates.get(taskType) || 
                   this.promptTemplates.get('general');

    if (!template) {
      template = this.createBaseTemplate(taskType);
    }

    // Fill in the template
    let prompt = template
      .replace('{{TASK}}', task)
      .replace('{{TASK_TYPE}}', taskType);

    // Add requirements
    if (requirements.length > 0) {
      const reqSection = `REQUIREMENTS:\n${requirements.map((r, i) => `${i + 1}. ${r}`).join('\n')}`;
      prompt = prompt.replace('{{REQUIREMENTS}}', reqSection);
    } else {
      prompt = prompt.replace('{{REQUIREMENTS}}', '');
    }

    // Add constraints
    if (constraints.length > 0) {
      const constSection = `CONSTRAINTS:\n${constraints.map((c, i) => `- ${c}`).join('\n')}`;
      prompt = prompt.replace('{{CONSTRAINTS}}', constSection);
    } else {
      prompt = prompt.replace('{{CONSTRAINTS}}', '');
    }

    // Add NEC references based on task type
    const necRefs = this.getNECReferencesForTask(taskType);
    prompt = prompt.replace('{{NEC_REFS}}', necRefs.join(', '));

    // Add safety requirements
    const safetyReqs = this.getSafetyRequirementsForTask(taskType);
    prompt = prompt.replace('{{SAFETY_REQUIREMENTS}}', safetyReqs);

    // Add few-shot examples if applicable
    if (taskType === 'calculation' || taskType === 'validation') {
      const examples = await this.generateExamples({ 
        taskType: this.mapToExampleType(task), 
        count: 2, 
        complexity: 'intermediate' 
      });
      if (examples.examples.length > 0) {
        prompt += '\n\nEXAMPLES:\n' + examples.examples.map((ex: any) => ex.prompt).join('\n\n');
      }
    }

    // Model-specific adjustments
    prompt = this.applyModelSpecificOptimizations(prompt, targetModel);

    return {
      prompt,
      metadata: {
        taskType,
        targetModel,
        includesExamples: prompt.includes('EXAMPLES:'),
        includesSafety: true,
        necReferences: necRefs,
        estimatedTokens: this.estimateTokens(prompt),
      },
    };
  }

  // Validate safety in prompts
  private async validateSafety(data: z.infer<typeof validateSafetySchema>): Promise<any> {
    const { prompt, taskType, riskLevel } = data;

    const issues = [];
    const missingSafetyElements = [];
    
    // Check for required safety keywords
    const missingKeywords = REQUIRED_SAFETY_KEYWORDS.filter(
      keyword => !prompt.toLowerCase().includes(keyword.toLowerCase())
    );
    
    if (missingKeywords.length > 0) {
      issues.push(`Missing safety keywords: ${missingKeywords.join(', ')}`);
      missingSafetyElements.push(...missingKeywords);
    }

    // Check for calculation precision
    if (taskType.includes('calculation') && !prompt.toLowerCase().includes('decimal')) {
      issues.push('No precision requirements specified for calculations');
      missingSafetyElements.push('decimal precision');
    }

    // Check for error handling
    if (!prompt.toLowerCase().includes('error') && !prompt.toLowerCase().includes('exception')) {
      issues.push('Insufficient error handling instructions');
      missingSafetyElements.push('error handling');
    }

    // Check for validation steps
    if (!prompt.toLowerCase().includes('validat') && !prompt.toLowerCase().includes('verif')) {
      issues.push('No validation steps included');
      missingSafetyElements.push('validation steps');
    }

    // Risk-level specific checks
    if (riskLevel === 'critical' || riskLevel === 'high') {
      if (!prompt.toLowerCase().includes('safety margin') && !prompt.toLowerCase().includes('safety factor')) {
        issues.push('No safety margin specified for high-risk operation');
        missingSafetyElements.push('safety margin');
      }
      
      if (!prompt.toLowerCase().includes('double-check') && !prompt.toLowerCase().includes('verify')) {
        issues.push('No verification step for critical operation');
        missingSafetyElements.push('verification step');
      }
    }

    // Check for output validation
    if (!prompt.toLowerCase().includes('output') || !prompt.toLowerCase().includes('result')) {
      issues.push('No clear output validation requirements');
      missingSafetyElements.push('output validation');
    }

    const safetyScore = Math.max(0, 1 - (issues.length * 0.15));

    return {
      isSafe: issues.length === 0,
      issues,
      missingSafetyElements,
      safetyScore,
      recommendations: this.generateSafetyRecommendations(taskType, riskLevel, issues),
    };
  }

  // Manage context for long prompts
  private async manageContext(data: z.infer<typeof manageContextSchema>): Promise<any> {
    const { fullContext, maxTokens, priority = [], taskType } = data;

    const currentTokens = this.estimateTokens(fullContext);
    
    if (currentTokens <= maxTokens * 0.8) {
      // Context fits comfortably
      return {
        strategy: 'full-context',
        managedContext: fullContext,
        tokens: currentTokens,
        reduction: 0,
      };
    }

    // Need to reduce context
    const sections = this.parseContextSections(fullContext);
    const prioritizedSections = this.prioritizeSections(sections, priority, taskType);
    
    let managedContext = '';
    let usedTokens = 0;
    const includedSections = [];

    // Always include critical sections
    const criticalSections = ['task', 'requirements', 'safety', 'nec'];
    for (const section of prioritizedSections) {
      if (criticalSections.includes(section.type) || priority.includes(section.type)) {
        managedContext += section.content + '\n\n';
        usedTokens += this.estimateTokens(section.content);
        includedSections.push(section.type);
      }
    }

    // Add remaining sections by priority until we approach limit
    for (const section of prioritizedSections) {
      if (!includedSections.includes(section.type)) {
        const sectionTokens = this.estimateTokens(section.content);
        if (usedTokens + sectionTokens < maxTokens * 0.9) {
          managedContext += section.content + '\n\n';
          usedTokens += sectionTokens;
          includedSections.push(section.type);
        } else {
          // Try to include a summary
          const summary = this.summarizeSection(section);
          const summaryTokens = this.estimateTokens(summary);
          if (usedTokens + summaryTokens < maxTokens * 0.95) {
            managedContext += summary + '\n\n';
            usedTokens += summaryTokens;
            includedSections.push(`${section.type}-summary`);
          }
        }
      }
    }

    return {
      strategy: 'selective-context',
      managedContext,
      tokens: usedTokens,
      reduction: Math.round(((currentTokens - usedTokens) / currentTokens) * 100),
      includedSections,
      excludedSections: prioritizedSections
        .filter(s => !includedSections.includes(s.type) && !includedSections.includes(`${s.type}-summary`))
        .map(s => s.type),
    };
  }

  // Generate electrical domain examples
  private async generateExamples(data: z.infer<typeof generateExamplesSchema>): Promise<any> {
    const { taskType, count, complexity } = data;

    // Check cache first
    const cacheKey = `${taskType}-${complexity}`;
    if (this.exampleCache.has(cacheKey)) {
      const cached = this.exampleCache.get(cacheKey)!;
      return {
        examples: cached.slice(0, count),
        source: 'cache',
      };
    }

    const examples = [];

    switch (taskType) {
      case 'voltage-drop':
        examples.push(...this.generateVoltageDropExamples(complexity, count));
        break;
      case 'load-calculation':
        examples.push(...this.generateLoadCalculationExamples(complexity, count));
        break;
      case 'wire-sizing':
        examples.push(...this.generateWireSizingExamples(complexity, count));
        break;
      case 'motor-control':
        examples.push(...this.generateMotorControlExamples(complexity, count));
        break;
      case 'arc-flash':
        examples.push(...this.generateArcFlashExamples(complexity, count));
        break;
    }

    // Cache the examples
    this.exampleCache.set(cacheKey, examples);

    return {
      examples: examples.slice(0, count),
      source: 'generated',
      taskType,
      complexity,
    };
  }

  // Analyze token usage
  private async analyzeTokens(data: z.infer<typeof analyzeTokensSchema>): Promise<any> {
    const { prompt, model, includeOptimizations } = data;

    const tokens = this.estimateTokens(prompt);
    const cost = this.calculateCost(tokens, model);

    const analysis = {
      tokens,
      estimatedCost: cost,
      model,
      breakdown: this.analyzePromptStructure(prompt),
    };

    if (includeOptimizations) {
      const optimized = await this.optimizePrompt({ prompt, model, optimizationGoals: ['token-efficiency'] });
      analysis.optimizations = {
        optimizedTokens: this.estimateTokens(optimized.optimized),
        tokenSavings: optimized.metrics.tokenReduction,
        costSavings: optimized.metrics.estimatedCostSavings,
        suggestions: optimized.improvements,
      };
    }

    return analysis;
  }

  // Initialize prompt templates
  private initializeTemplates(): void {
    // General template
    this.promptTemplates.set('general', `
{{TASK}}

CRITICAL SAFETY REQUIREMENTS:
- Use Decimal precision for ALL calculations
- Verify results against NEC standards
- Include appropriate safety margins
- Validate all inputs and outputs

{{REQUIREMENTS}}

{{CONSTRAINTS}}

Show step-by-step process with clear explanations.
If any input seems unsafe or invalid, STOP and explain why.
`);

    // Calculation template
    this.promptTemplates.set('calculation', `
Calculate {{TASK}} following NEC {{NEC_REFS}} standards.

CRITICAL SAFETY REQUIREMENTS:
{{SAFETY_REQUIREMENTS}}

{{REQUIREMENTS}}

Required Outputs:
1. Calculated value with units
2. Safety margin applied
3. NEC code reference
4. Pass/Fail safety check
5. Any warnings or limitations

{{CONSTRAINTS}}

Show step-by-step calculation with intermediate values.
Use Decimal.js or equivalent for precision.
`);

    // Code generation template
    this.promptTemplates.set('code-generation', `
Generate {{TASK}} code for electrical contracting application.

MANDATORY REQUIREMENTS:
1. Input validation for all electrical values
2. Bounds checking per NEC {{NEC_REFS}}
3. Error handling with descriptive messages
4. Audit logging for all calculations
5. Unit testing with edge cases

{{REQUIREMENTS}}

Technical Specifications:
- Use TypeScript with strict typing
- Include comprehensive error handling
- Add inline comments explaining safety checks
- Use Decimal.js for all calculations

{{CONSTRAINTS}}
`);

    // Analysis template
    this.promptTemplates.set('analysis', `
Analyze {{TASK}} for electrical contracting context.

Focus Areas:
1. NEC compliance ({{NEC_REFS}})
2. Safety considerations
3. Calculation accuracy
4. Performance implications
5. Best practices

{{REQUIREMENTS}}

Provide:
- Detailed findings with severity levels
- Specific recommendations
- Code references where applicable
- Risk assessment

{{CONSTRAINTS}}
`);

    // Model-specific templates
    this.promptTemplates.set('calculation-claude', `
Human: Calculate {{TASK}} following NEC {{NEC_REFS}} standards.

Requirements:
{{REQUIREMENTS}}

Please provide:
1. Step-by-step calculations using Decimal precision
2. All intermediate values with units
3. Safety margin calculations
4. NEC code references for each step
5. Final result with pass/fail determination

Important: If any input violates NEC limits or seems unsafe, stop and explain the issue.
`);
  }

  // Initialize NEC references
  private initializeNECReferences(): void {
    this.necReferences.set('voltage-drop', '210.19(A), 215.2(A)(1), 230.31(C)');
    this.necReferences.set('load-calculation', '220.12, 220.14, 220.52, 220.53');
    this.necReferences.set('wire-sizing', '310.15, 310.16, Table 310.15(B)(16)');
    this.necReferences.set('motor-control', '430.22, 430.24, 430.52, Table 430.52');
    this.necReferences.set('grounding', '250.122, Table 250.122, 250.66');
    this.necReferences.set('overcurrent', '240.4, 240.6, 240.21');
    this.necReferences.set('panel', '408.36, 408.40, 408.41');
    this.necReferences.set('arc-flash', '110.16, 130.5(H)');
  }

  // Load example cache
  private async loadExampleCache(): Promise<void> {
    // Try to load cached examples from memory
    const cachedExamples = await this.retrieveKnowledge(['examples', 'electrical'], 100);
    
    for (const memory of cachedExamples) {
      if (memory.content.type && memory.content.examples) {
        const key = `${memory.content.type}-${memory.content.complexity}`;
        this.exampleCache.set(key, memory.content.examples);
      }
    }
  }

  // Helper methods

  private injectElectricalContext(prompt: string, context: any): string {
    const contextSection = `
ELECTRICAL CONTEXT:
- Project Type: ${context.projectType || 'General electrical'}
- Voltage System: ${context.voltage || '120/240V'}
- Applicable Codes: ${context.codes?.join(', ') || 'NEC 2023'}
- Safety Critical: ${context.safetyCritical ? 'YES' : 'NO'}

`;
    return contextSection + prompt;
  }

  private extractRelevantNECReferences(prompt: string): string[] {
    const references = [];
    
    for (const [key, refs] of this.necReferences) {
      if (prompt.toLowerCase().includes(key)) {
        references.push(refs);
      }
    }
    
    return [...new Set(references.flat())];
  }

  private addNECReferences(prompt: string, references: string[]): string {
    const necSection = `\nRelevant NEC References: ${references.join(', ')}\n`;
    
    // Insert after task description but before requirements
    const lines = prompt.split('\n');
    let insertIndex = 0;
    
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].toLowerCase().includes('requirement') || 
          lines[i].toLowerCase().includes('critical')) {
        insertIndex = i;
        break;
      }
    }
    
    lines.splice(insertIndex, 0, necSection);
    return lines.join('\n');
  }

  private addPrecisionRequirements(prompt: string): string {
    const precisionNote = '\nIMPORTANT: Use Decimal.js or equivalent for ALL calculations to ensure precision.\n';
    
    if (prompt.toLowerCase().includes('calculat')) {
      return prompt.replace(/calculate/i, 'Calculate (using Decimal precision)');
    }
    
    return prompt + precisionNote;
  }

  private addSafetyValidations(prompt: string, missingSafetyElements: string[]): string {
    let safetySection = '\n\nSAFETY VALIDATIONS:\n';
    
    if (missingSafetyElements.includes('error handling')) {
      safetySection += '- Implement comprehensive error handling for all inputs\n';
    }
    if (missingSafetyElements.includes('validation steps')) {
      safetySection += '- Validate all inputs against NEC limits before processing\n';
    }
    if (missingSafetyElements.includes('safety margin')) {
      safetySection += '- Apply appropriate safety margins (minimum 125% for continuous loads)\n';
    }
    if (missingSafetyElements.includes('verification step')) {
      safetySection += '- Double-check all critical calculations and results\n';
    }
    if (missingSafetyElements.includes('output validation')) {
      safetySection += '- Validate output values are within acceptable ranges\n';
    }
    
    return prompt + safetySection;
  }

  private optimizeForTokens(prompt: string, model: string): { optimized: string; reduction: number } {
    let optimized = prompt;
    let originalLength = prompt.length;
    
    // Remove excessive whitespace
    optimized = optimized.replace(/\n{3,}/g, '\n\n');
    optimized = optimized.replace(/\s+/g, ' ');
    
    // Use abbreviations for common terms
    const abbreviations = {
      'National Electrical Code': 'NEC',
      'American Wire Gauge': 'AWG',
      'thousand circular mils': 'kcmil',
      'horsepower': 'hp',
      'kilowatt': 'kW',
      'voltage drop': 'VD',
      'equipment grounding conductor': 'EGC',
      'ground fault circuit interrupter': 'GFCI',
      'arc fault circuit interrupter': 'AFCI',
    };
    
    for (const [full, abbr] of Object.entries(abbreviations)) {
      optimized = optimized.replace(new RegExp(full, 'gi'), abbr);
    }
    
    // Remove redundant instructions
    const redundantPhrases = [
      'Please ensure that',
      'It is important to',
      'Make sure to',
      'Be certain to',
    ];
    
    for (const phrase of redundantPhrases) {
      optimized = optimized.replace(new RegExp(phrase + ' ', 'gi'), '');
    }
    
    // Compress lists
    optimized = optimized.replace(/•\s+/g, '- ');
    
    const reduction = Math.round(((originalLength - optimized.length) / originalLength) * 100);
    
    return { optimized, reduction };
  }

  private applyModelSpecificOptimizations(prompt: string, model: string): string {
    switch (model) {
      case 'claude':
        // Claude prefers clear structure with Human/Assistant format for conversations
        if (!prompt.startsWith('Human:')) {
          return prompt; // Keep as is for non-conversational prompts
        }
        break;
        
      case 'gpt':
        // GPT works well with numbered lists and clear sections
        return prompt.replace(/- /g, '• ');
        
      case 'gemini':
        // Gemini benefits from examples and explicit output format
        if (!prompt.includes('Example:') && !prompt.includes('Format:')) {
          prompt += '\n\nOutput Format: Structured response with clear sections.';
        }
        break;
    }
    
    return prompt;
  }

  private estimateTokens(text: string): number {
    // Rough estimate: ~4 characters per token for English
    // More accurate for technical content with numbers
    const words = text.split(/\s+/).length;
    const numbers = (text.match(/\d+/g) || []).length;
    
    // Technical content tends to have more tokens per word
    return Math.ceil(words * 1.3 + numbers * 0.5);
  }

  private calculateCost(tokens: number, model: string): number {
    const costs = TOKEN_COSTS[model as keyof typeof TOKEN_COSTS];
    if (!costs) return 0;
    
    // Assume 30% input, 70% output for typical use
    const inputTokens = tokens * 0.3;
    const outputTokens = tokens * 0.7;
    
    return (inputTokens * costs.input + outputTokens * costs.output) / 1000;
  }

  private calculateCostSavings(originalTokens: number, optimizedTokens: number, model: string): number {
    const originalCost = this.calculateCost(originalTokens, model);
    const optimizedCost = this.calculateCost(optimizedTokens, model);
    return originalCost - optimizedCost;
  }

  private analyzePromptStructure(prompt: string): any {
    const lines = prompt.split('\n');
    const sections = [];
    let currentSection = { name: 'intro', lines: 0, tokens: 0 };
    
    for (const line of lines) {
      if (line.match(/^[A-Z][A-Z\s]+:$/)) {
        if (currentSection.lines > 0) {
          sections.push(currentSection);
        }
        currentSection = {
          name: line.replace(':', '').toLowerCase(),
          lines: 0,
          tokens: 0,
        };
      }
      currentSection.lines++;
      currentSection.tokens += this.estimateTokens(line);
    }
    
    if (currentSection.lines > 0) {
      sections.push(currentSection);
    }
    
    return {
      sections,
      totalLines: lines.length,
      averageLineLength: prompt.length / lines.length,
      hasExamples: prompt.includes('Example:') || prompt.includes('EXAMPLE'),
      hasRequirements: prompt.includes('Requirement') || prompt.includes('REQUIREMENT'),
      hasSafety: prompt.includes('safety') || prompt.includes('SAFETY'),
    };
  }

  private generateSafetyRecommendations(taskType: string, riskLevel: string, issues: string[]): string[] {
    const recommendations = [];
    
    if (taskType.includes('calculation')) {
      recommendations.push('Add explicit decimal precision requirements');
      recommendations.push('Include NEC table references for validation');
      recommendations.push('Require showing all intermediate calculation steps');
    }
    
    if (riskLevel === 'critical' || riskLevel === 'high') {
      recommendations.push('Add double-verification requirement for results');
      recommendations.push('Include explicit safety margin calculations');
      recommendations.push('Require flagging any values near limits');
    }
    
    if (issues.some(i => i.includes('error handling'))) {
      recommendations.push('Add specific error cases to handle');
      recommendations.push('Include graceful degradation instructions');
    }
    
    if (issues.some(i => i.includes('validation'))) {
      recommendations.push('List specific validation criteria');
      recommendations.push('Include boundary value checks');
    }
    
    return recommendations;
  }

  private parseContextSections(context: string): any[] {
    const sections = [];
    const parts = context.split(/\n(?=[A-Z][A-Z\s]+:)/);
    
    for (const part of parts) {
      const lines = part.trim().split('\n');
      const header = lines[0];
      const content = lines.join('\n');
      
      sections.push({
        type: this.identifySectionType(header),
        content,
        priority: this.calculateSectionPriority(header, content),
      });
    }
    
    return sections;
  }

  private identifySectionType(header: string): string {
    const normalized = header.toLowerCase();
    
    if (normalized.includes('task') || normalized.includes('objective')) return 'task';
    if (normalized.includes('requirement')) return 'requirements';
    if (normalized.includes('safety') || normalized.includes('critical')) return 'safety';
    if (normalized.includes('nec') || normalized.includes('code')) return 'nec';
    if (normalized.includes('example')) return 'examples';
    if (normalized.includes('context') || normalized.includes('background')) return 'context';
    if (normalized.includes('constraint')) return 'constraints';
    
    return 'other';
  }

  private calculateSectionPriority(header: string, content: string): number {
    let priority = 0.5;
    
    // Critical sections
    if (header.toLowerCase().includes('safety') || 
        header.toLowerCase().includes('critical')) {
      priority = 1.0;
    }
    
    // Important sections
    if (header.toLowerCase().includes('requirement') || 
        header.toLowerCase().includes('task')) {
      priority = 0.9;
    }
    
    // NEC references
    if (content.includes('NEC') || content.includes('Article')) {
      priority = Math.max(priority, 0.8);
    }
    
    // Examples can be lower priority
    if (header.toLowerCase().includes('example')) {
      priority = 0.3;
    }
    
    return priority;
  }

  private prioritizeSections(sections: any[], userPriority: string[], taskType: string): any[] {
    return sections.sort((a, b) => {
      // User priority first
      const aUserPriority = userPriority.indexOf(a.type);
      const bUserPriority = userPriority.indexOf(b.type);
      
      if (aUserPriority !== -1 && bUserPriority !== -1) {
        return aUserPriority - bUserPriority;
      }
      if (aUserPriority !== -1) return -1;
      if (bUserPriority !== -1) return 1;
      
      // Then by calculated priority
      return b.priority - a.priority;
    });
  }

  private summarizeSection(section: any): string {
    const maxLength = 200;
    const { type, content } = section;
    
    let summary = `[${type.toUpperCase()} SUMMARY]\n`;
    
    // Extract key points
    const lines = content.split('\n').filter(l => l.trim());
    const keyPoints = lines.filter(l => 
      l.includes('must') || 
      l.includes('required') || 
      l.includes('critical') ||
      l.match(/^\d+\./) ||
      l.match(/^-/)
    );
    
    if (keyPoints.length > 0) {
      summary += keyPoints.slice(0, 3).join('\n');
    } else {
      summary += content.substring(0, maxLength) + '...';
    }
    
    return summary;
  }

  private getNECReferencesForTask(taskType: string): string[] {
    const refs = [];
    
    switch (taskType) {
      case 'calculation':
        refs.push('220.12', '220.14', '220.40');
        break;
      case 'code-generation':
        refs.push('110.3(B)', '110.12', '110.14');
        break;
      case 'validation':
        refs.push('110.3(B)', '90.4');
        break;
      case 'analysis':
        refs.push('90.1', '110.3(B)');
        break;
    }
    
    return refs;
  }

  private getSafetyRequirementsForTask(taskType: string): string {
    const requirements = [
      '- Use Decimal precision for ALL calculations',
      '- Apply appropriate safety margins',
      '- Validate all inputs before processing',
      '- Check results against NEC limits',
    ];
    
    if (taskType === 'calculation') {
      requirements.push('- Show all intermediate calculation steps');
      requirements.push('- Include units in all values');
    }
    
    if (taskType === 'code-generation') {
      requirements.push('- Include comprehensive error handling');
      requirements.push('- Add input validation for all parameters');
      requirements.push('- Log all safety-critical operations');
    }
    
    return requirements.join('\n');
  }

  private mapToExampleType(task: string): 'voltage-drop' | 'load-calculation' | 'wire-sizing' | 'motor-control' | 'arc-flash' {
    const taskLower = task.toLowerCase();
    
    if (taskLower.includes('voltage') || taskLower.includes('drop')) return 'voltage-drop';
    if (taskLower.includes('load') || taskLower.includes('demand')) return 'load-calculation';
    if (taskLower.includes('wire') || taskLower.includes('conductor')) return 'wire-sizing';
    if (taskLower.includes('motor') || taskLower.includes('starter')) return 'motor-control';
    if (taskLower.includes('arc') || taskLower.includes('flash')) return 'arc-flash';
    
    // Default to load calculation
    return 'load-calculation';
  }

  private generateVoltageDropExamples(complexity: string, count: number): any[] {
    const examples = [];
    
    if (complexity === 'basic') {
      examples.push({
        prompt: 'Calculate voltage drop for 100ft of 12 AWG copper wire carrying 20A at 120V single phase.',
        expected: 'VD = 2 × L × R × I / 1000 = 2 × 100 × 1.98 × 20 / 1000 = 7.92V (6.6%)',
        necRef: '210.19(A)',
      });
    }
    
    if (complexity === 'intermediate') {
      examples.push({
        prompt: 'Calculate voltage drop for 200ft of 3/0 AWG aluminum feeding a 200A, 480V 3-phase panel.',
        expected: 'VD = 1.732 × L × R × I / 1000 = 1.732 × 200 × 0.101 × 200 / 1000 = 7.0V (1.46%)',
        necRef: '215.2(A)(1)',
      });
    }
    
    if (complexity === 'advanced') {
      examples.push({
        prompt: 'Size conductors for 300ft run to 150A continuous load at 208V 3-phase with 3% max voltage drop.',
        expected: 'Need to consider ampacity (187.5A) and voltage drop. 350 kcmil copper gives 2.8% VD.',
        necRef: '210.19(A), 310.15',
      });
    }
    
    return examples.slice(0, count);
  }

  private generateLoadCalculationExamples(complexity: string, count: number): any[] {
    const examples = [];
    
    if (complexity === 'basic') {
      examples.push({
        prompt: 'Calculate general lighting load for 2000 sq ft dwelling unit.',
        expected: '2000 sq ft × 3 VA/sq ft = 6000 VA per Table 220.12',
        necRef: '220.12',
      });
    }
    
    if (complexity === 'intermediate') {
      examples.push({
        prompt: 'Calculate total load for dwelling with 2500 sq ft, electric range, dryer, and A/C.',
        expected: 'Lighting: 7500VA, Small appliance: 3000VA, Laundry: 1500VA, Range: 8000VA, etc.',
        necRef: '220.12, 220.52, 220.53, 220.54',
      });
    }
    
    return examples.slice(0, count);
  }

  private generateWireSizingExamples(complexity: string, count: number): any[] {
    const examples = [];
    
    examples.push({
      prompt: `Size conductors for ${complexity === 'basic' ? '30A' : '100A'} circuit at ${complexity === 'basic' ? '120V' : '480V'}`,
      expected: `Use Table 310.15(B)(16) for ampacity rating`,
      necRef: '310.15, Table 310.15(B)(16)',
    });
    
    return examples.slice(0, count);
  }

  private generateMotorControlExamples(complexity: string, count: number): any[] {
    const examples = [];
    
    examples.push({
      prompt: `Size overload protection for ${complexity === 'basic' ? '10' : '50'} HP motor`,
      expected: `Use 125% of FLA per 430.32`,
      necRef: '430.32, Table 430.52',
    });
    
    return examples.slice(0, count);
  }

  private generateArcFlashExamples(complexity: string, count: number): any[] {
    const examples = [];
    
    examples.push({
      prompt: 'Determine arc flash boundary for 480V switchgear with 30kA fault current',
      expected: 'Calculate incident energy using IEEE 1584 methods',
      necRef: '110.16, 130.5(H)',
    });
    
    return examples.slice(0, count);
  }

  private createBaseTemplate(taskType: string): string {
    return `
Perform {{TASK}} for electrical contracting application.

TASK TYPE: {{TASK_TYPE}}

{{REQUIREMENTS}}

{{CONSTRAINTS}}

Follow all applicable NEC requirements and industry best practices.
Ensure safety and accuracy in all operations.
`;
  }
}