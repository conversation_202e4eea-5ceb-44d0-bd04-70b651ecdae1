// Temporarily using mock TypeORM - will be replaced with proper database solution
// Note: Decorators removed for React Native compatibility
import { BaseEntity } from './BaseEntity';
import { Project } from './Project';

export class Material extends BaseEntity {
  name: string;

  description: string;

  category: string;

  manufacturer: string;

  modelNumber: string;

  barcode: string;

  quantity: number;

  unit: string;

  unitPrice: number;

  totalPrice: number;

  supplier: string;

  location: string;

  status: 'pending' | 'ordered' | 'received' | 'installed';

  orderedDate: string;

  receivedDate: string;

  installedDate: string;

  notes: string;

  metadata: string; // JSON string

  projectId: string;

  project: Project;
}