import React from 'react';
import { useParams } from 'react-router-dom';
import { PanelList } from '../components/panels';

export const PanelsPage: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>();

  if (!projectId) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">No project selected</p>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Panel Management</h1>
      <PanelList />
    </div>
  );
};