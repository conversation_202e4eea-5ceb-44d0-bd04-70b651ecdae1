<!DOCTYPE html>
<html>
<head>
    <title>Login Test</title>
</head>
<body>
    <h1>Login <NAME_EMAIL></h1>
    <div id="result"></div>
    
    <script>
        async function testLogin() {
            const resultDiv = document.getElementById('result');
            
            try {
                resultDiv.innerHTML = 'Attempting login...';
                
                const response = await fetch('http://localhost:3001/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'itsMike818!'
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <h2 style="color: green;">Login Successful!</h2>
                        <p>User: ${data.user.name} (${data.user.email})</p>
                        <p>Role: ${data.user.role}</p>
                        <p>Company ID: ${data.user.companyId || 'Not set'}</p>
                        <p>Access Token: ${data.accessToken ? 'Received' : 'Not received'}</p>
                        <p>Refresh Token: ${data.refreshToken ? 'Received' : 'Not received'}</p>
                        <br>
                        <p>You can now navigate to <a href="http://localhost:3000">http://localhost:3000</a> and manually login with:</p>
                        <ul>
                            <li>Email: <EMAIL></li>
                            <li>Password: itsMike818!</li>
                        </ul>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <h2 style="color: red;">Login Failed</h2>
                        <p>Status: ${response.status}</p>
                        <p>Error: ${data.error || JSON.stringify(data)}</p>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <h2 style="color: red;">Connection Error</h2>
                    <p>${error.message}</p>
                    <p>Make sure the backend is running on port 3001</p>
                `;
            }
        }
        
        // Test login automatically
        testLogin();
    </script>
</body>
</html>