import React from 'react';
import { View, ActivityIndicator, Text, StyleSheet } from 'react-native';

interface SimpleLoadingScreenProps {
  message?: string;
}

export const SimpleLoadingScreen: React.FC<SimpleLoadingScreenProps> = ({ 
  message = 'Loading...' 
}) => {
  return (
    <View style={styles.container}>
      <ActivityIndicator size="large" color="#2196f3" />
      <Text style={styles.text}>{message}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    justifyContent: 'center',
    alignItems: 'center',
  },
  text: {
    marginTop: 16,
    fontSize: 16,
    color: '#666666',
  },
});