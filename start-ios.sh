#!/bin/bash

echo "============================================================"
echo "    Electrical Contracting App - iOS Development"
echo "============================================================"
echo

# Check if on macOS
if [[ "$OSTYPE" != "darwin"* ]]; then
    echo "ERROR: iOS development requires macOS!"
    exit 1
fi

# Check Xcode
if ! xcode-select -p &> /dev/null; then
    echo "ERROR: Xcode is not installed!"
    echo "Please install Xcode from the App Store"
    exit 1
fi

# Navigate to mobile directory
cd "$(dirname "$0")/app/mobile"

# Install dependencies if needed
if [ ! -d "node_modules/react-native" ]; then
    echo "Installing mobile dependencies..."
    npm install --legacy-peer-deps
fi

# Install iOS dependencies
if [ ! -d "ios/Pods" ]; then
    echo "Installing iOS dependencies..."
    cd ios
    pod install
    cd ..
fi

# Start Metro bundler and run iOS
echo
echo "Starting React Native for iOS..."
echo

# Start Metro in new terminal
osascript -e 'tell app "Terminal" to do script "cd '"$(pwd)"' && npx react-native start"'

# Wait for Metro
sleep 5

# Run iOS app
echo "Building and running iOS app..."
npx react-native run-ios

echo
echo "============================================================"
echo "If the app doesn't appear in simulator:"
echo "1. Make sure Metro bundler is running (check other window)"
echo "2. Press Cmd+R in simulator to reload"
echo "3. Check Xcode for any build errors"
echo "============================================================"
echo