import React, { Component, ErrorInfo, ReactNode } from 'react';
import { AlertTriangle, RefreshCw, Home, Download, ChevronDown, ChevronUp } from 'lucide-react';
import { errorService } from '../services/errorService';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  showDetails?: boolean;
  resetKeys?: Array<string | number>;
  resetOnPropsChange?: boolean;
  isolate?: boolean;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string;
  showDetails: boolean;
  errorCount: number;
  previousResetKeys?: Array<string | number>;
}

export class EnhancedErrorBoundary extends Component<Props, State> {
  private resetTimeoutId?: NodeJS.Timeout;
  private previousResetKeys?: Array<string | number>;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
      showDetails: false,
      errorCount: 0,
    };
    this.previousResetKeys = props.resetKeys;
  }

  static getDerivedStateFromProps(props: Props, state: State): Partial<State> | null {
    // Reset error boundary when resetKeys change
    if (props.resetKeys && state.hasError && props.resetOnPropsChange) {
      const prevKeys = (state as any).previousResetKeys || [];
      const hasKeysChanged = !prevKeys.length ||
        props.resetKeys.some((key, index) => key !== prevKeys[index]);
      
      if (hasKeysChanged) {
        return {
          hasError: false,
          error: null,
          errorInfo: null,
          errorId: '',
          errorCount: 0,
        };
      }
    }
    return null;
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    const errorId = `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    return {
      hasError: true,
      error,
      errorId,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    const { onError } = this.props;
    const { errorCount } = this.state;

    // Log error
    this.logError(error, errorInfo);

    // Update state
    this.setState({
      errorInfo,
      errorCount: errorCount + 1,
    });

    // Call custom error handler
    if (onError) {
      onError(error, errorInfo);
    }

    // Auto-reset after multiple errors (circuit breaker pattern)
    if (errorCount >= 3) {
      this.scheduleReset(5000);
    }
  }

  componentDidUpdate(prevProps: Props): void {
    // Store previous reset keys
    if (prevProps.resetKeys !== this.props.resetKeys) {
      this.previousResetKeys = prevProps.resetKeys;
    }
  }

  componentWillUnmount(): void {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
    }
  }

  private logError(error: Error, errorInfo: ErrorInfo): void {
    const errorData = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      errorId: this.state.errorId,
      errorCount: this.state.errorCount + 1,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      isolate: this.props.isolate,
    };

    // Log to error service
    errorService.logError(error, {
      errorBoundary: true,
      ...errorData,
    });

    // Store in session storage for debugging
    try {
      const key = 'error_boundary_logs';
      const stored = sessionStorage.getItem(key);
      const logs = stored ? JSON.parse(stored) : [];
      logs.push(errorData);
      
      // Keep only last 5 errors in session
      if (logs.length > 5) logs.shift();
      
      sessionStorage.setItem(key, JSON.stringify(logs));
    } catch {
      // Ignore storage errors
    }
  }

  private scheduleReset(delay: number): void {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
    }

    this.resetTimeoutId = setTimeout(() => {
      this.handleReset();
    }, delay);
  }

  private handleReset = (): void => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
      showDetails: false,
      errorCount: 0,
    });

    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
      this.resetTimeoutId = undefined;
    }
  };

  private handleGoHome = (): void => {
    window.location.href = '/';
  };

  private handleReload = (): void => {
    window.location.reload();
  };

  private toggleDetails = (): void => {
    this.setState(prev => ({ showDetails: !prev.showDetails }));
  };

  private downloadErrorReport = (): void => {
    const { error, errorInfo, errorId } = this.state;
    
    const report = {
      errorId,
      timestamp: new Date().toISOString(),
      error: {
        message: error?.message,
        stack: error?.stack,
      },
      componentStack: errorInfo?.componentStack,
      browser: {
        userAgent: navigator.userAgent,
        url: window.location.href,
        timestamp: new Date().toISOString(),
      },
      sessionErrors: this.getSessionErrors(),
    };

    const blob = new Blob([JSON.stringify(report, null, 2)], {
      type: 'application/json',
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `error-report-${errorId}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  private getSessionErrors(): any[] {
    try {
      const stored = sessionStorage.getItem('error_boundary_logs');
      return stored ? JSON.parse(stored) : [];
    } catch {
      return [];
    }
  }

  render(): ReactNode {
    const { hasError, error, errorId, showDetails, errorCount } = this.state;
    const { children, fallback, isolate, showDetails: showDetailsProp } = this.props;

    if (hasError) {
      // Use custom fallback if provided
      if (fallback !== undefined) {
        return fallback;
      }

      // For isolated error boundaries, show a simpler UI
      if (isolate) {
        return (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center text-red-700">
              <AlertTriangle className="h-5 w-5 mr-2" />
              <span className="font-medium">Component Error</span>
            </div>
            <p className="mt-2 text-sm text-red-600">
              This component encountered an error and cannot be displayed.
            </p>
            <button
              onClick={this.handleReset}
              className="mt-3 text-sm text-red-700 hover:text-red-800 underline"
            >
              Try Again
            </button>
          </div>
        );
      }

      // Full error UI
      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
          <div className="max-w-2xl w-full bg-white rounded-lg shadow-lg">
            <div className="p-8">
              <div className="flex items-center mb-6">
                <div className="p-3 bg-red-100 rounded-full">
                  <AlertTriangle className="h-8 w-8 text-red-600" />
                </div>
                <div className="ml-4">
                  <h1 className="text-2xl font-bold text-gray-900">
                    Something went wrong
                  </h1>
                  <p className="text-sm text-gray-500 mt-1">
                    Error ID: <code className="bg-gray-100 px-2 py-1 rounded">{errorId}</code>
                  </p>
                </div>
              </div>

              <div className="mb-6">
                <p className="text-gray-600">
                  We apologize for the inconvenience. The application encountered an unexpected error.
                </p>
                {errorCount > 1 && (
                  <p className="text-sm text-amber-600 mt-2">
                    This error has occurred {errorCount} times.
                  </p>
                )}
              </div>

              {/* Error details section */}
              {(showDetailsProp || import.meta.env.DEV) && (
                <div className="mb-6">
                  <button
                    onClick={this.toggleDetails}
                    className="flex items-center text-sm text-gray-600 hover:text-gray-800"
                  >
                    {showDetails ? <ChevronUp className="h-4 w-4 mr-1" /> : <ChevronDown className="h-4 w-4 mr-1" />}
                    {showDetails ? 'Hide' : 'Show'} Error Details
                  </button>
                  
                  {showDetails && error && (
                    <div className="mt-3 p-4 bg-gray-50 rounded-lg">
                      <h3 className="text-sm font-semibold text-gray-700 mb-2">
                        Error Message:
                      </h3>
                      <p className="text-sm text-gray-600 mb-3">{error.message}</p>
                      
                      <h3 className="text-sm font-semibold text-gray-700 mb-2">
                        Stack Trace:
                      </h3>
                      <pre className="text-xs text-gray-600 overflow-auto max-h-40 bg-white p-2 rounded border">
                        {error.stack}
                      </pre>
                    </div>
                  )}
                </div>
              )}

              {/* Action buttons */}
              <div className="flex flex-wrap gap-3">
                <button
                  onClick={this.handleReset}
                  className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Try Again
                </button>
                
                <button
                  onClick={this.handleReload}
                  className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Reload Page
                </button>
                
                <button
                  onClick={this.handleGoHome}
                  className="flex items-center px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors"
                >
                  <Home className="h-4 w-4 mr-2" />
                  Go Home
                </button>
                
                {import.meta.env.DEV && (
                  <button
                    onClick={this.downloadErrorReport}
                    className="flex items-center px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Download Report
                  </button>
                )}
              </div>

              {/* Support information */}
              <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                <p className="text-sm text-blue-800">
                  If this problem persists, please contact support with the error ID above.
                  {errorCount >= 3 && ' The application will attempt to recover automatically in a few seconds.'}
                </p>
              </div>
            </div>
          </div>
        </div>
      );
    }

    return children;
  }
}

// Export a hook for using error boundary in functional components
export function useErrorHandler() {
  return (error: Error, errorInfo?: ErrorInfo) => {
    errorService.logError(error, {
      errorBoundary: false,
      componentStack: errorInfo?.componentStack,
    });
  };
}