import { prisma } from '../database/prisma';

/**
 * Generate a unique quote number
 * Format: Q-YYYY-NNNN (e.g., Q-2025-0001)
 */
export async function generateQuoteNumber(): Promise<string> {
  const year = new Date().getFullYear();
  const prefix = `Q-${year}-`;
  
  // Get the last quote number for this year
  const lastQuote = await prisma.quote.findFirst({
    where: {
      quote_number: {
        startsWith: prefix
      }
    },
    orderBy: {
      quote_number: 'desc'
    }
  });
  
  let nextNumber = 1;
  if (lastQuote) {
    const lastNumber = parseInt(lastQuote.quote_number.split('-')[2]);
    if (!isNaN(lastNumber)) {
      nextNumber = lastNumber + 1;
    }
  }
  
  return `${prefix}${nextNumber.toString().padStart(4, '0')}`;
}

/**
 * Generate a unique job number
 * Format: J-YYYY-NNNN (e.g., J-2025-0001)
 */
export async function generateJobNumber(): Promise<string> {
  const year = new Date().getFullYear();
  const prefix = `J-${year}-`;
  
  // Get the last job number for this year
  const lastJob = await prisma.job.findFirst({
    where: {
      name: {
        startsWith: prefix
      }
    },
    orderBy: {
      name: 'desc'
    }
  });
  
  let nextNumber = 1;
  if (lastJob) {
    const match = lastJob.name.match(/J-\d{4}-(\d{4})/);
    if (match && match[1]) {
      const lastNumber = parseInt(match[1]);
      if (!isNaN(lastNumber)) {
        nextNumber = lastNumber + 1;
      }
    }
  }
  
  return `${prefix}${nextNumber.toString().padStart(4, '0')}`;
}

/**
 * Generate a unique invoice number
 * Format: INV-YYYY-NNNN (e.g., INV-2025-0001)
 */
export async function generateInvoiceNumber(): Promise<string> {
  const year = new Date().getFullYear();
  const prefix = `INV-${year}-`;
  
  // Note: Add Invoice model to schema if needed
  // For now, returning a timestamp-based number
  const timestamp = Date.now().toString().slice(-6);
  return `${prefix}${timestamp}`;
}

/**
 * Generate a unique material SKU if none provided
 * Format: MAT-XXXXX (e.g., MAT-A1B2C)
 */
export function generateMaterialSKU(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let sku = 'MAT-';
  for (let i = 0; i < 5; i++) {
    sku += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return sku;
}

/**
 * Generate a public access token for quotes
 * Returns a URL-safe random string
 */
export function generatePublicToken(length: number = 32): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_';
  let token = '';
  for (let i = 0; i < length; i++) {
    token += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return token;
}