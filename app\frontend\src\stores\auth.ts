import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { api, setAuthToken } from '../services/api';
import type { User } from '@electrical/shared';

interface AuthState {
  user: User | null;
  accessToken: string | null;
  refreshToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  hasHydrated: boolean;
  
  // Actions
  login: (email: string, password: string) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => Promise<void>;
  refreshAccessToken: () => Promise<void>;
  checkAuth: () => void;
  clearError: () => void;
  setHasHydrated: (hydrated: boolean) => void;
}

interface RegisterData {
  email: string;
  password: string;
  name: string;
  role?: string;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      accessToken: null,
      refreshToken: null,
      isAuthenticated: false,
      isLoading: false, // Don't start with loading to avoid race conditions
      error: null,
      hasHydrated: false,
      
      login: async (email: string, password: string) => {
        console.log('[Auth Store] Login attempt for:', email);
        set({ isLoading: true, error: null });
        try {
          const response = await api.post('/auth/login', { email, password });
          const { user, accessToken, refreshToken } = response.data;
          
          console.log('[Auth Store] Login successful:', {
            userId: user?.id,
            hasToken: !!accessToken,
            hasRefreshToken: !!refreshToken
          });
          
          // Set token immediately
          setAuthToken(accessToken);
          
          // Update state - this will trigger persistence
          set({
            user,
            accessToken,
            refreshToken,
            isAuthenticated: true,
            isLoading: false,
          });
          
          // Verify persistence immediately
          setTimeout(() => {
            const stored = localStorage.getItem('auth-storage');
            console.log('[Auth Store] Verification after login - auth persisted:', !!stored);
            if (stored) {
              try {
                const parsed = JSON.parse(stored);
                console.log('[Auth Store] Persisted state:', {
                  hasToken: !!parsed.state?.accessToken,
                  hasUser: !!parsed.state?.user,
                  isAuthenticated: parsed.state?.isAuthenticated
                });
              } catch (e) {
                console.error('[Auth Store] Failed to parse persisted state:', e);
              }
            }
          }, 100);
        } catch (error) {
          let errorMessage = 'Login failed';
          
          if (error instanceof Error) {
            errorMessage = error.message;
          } else if ((error as any).response?.data) {
            const errorData = (error as any).response.data;
            // Handle the backend's error response structure: { error: { message, code, details } }
            if (errorData.error && typeof errorData.error === 'object') {
              errorMessage = errorData.error.message || 'Login failed';
            } else {
              // Fallback for other error formats
              errorMessage = errorData.message || 
                            errorData.error ||
                            'Login failed';
            }
          }
          
          set({
            error: errorMessage,
            isLoading: false,
          });
          throw error;
        }
      },
      
      register: async (data: RegisterData) => {
        set({ isLoading: true, error: null });
        try {
          const response = await api.post('/auth/register', data);
          const { user, accessToken, refreshToken } = response.data;
          
          setAuthToken(accessToken);
          
          set({
            user,
            accessToken,
            refreshToken,
            isAuthenticated: true,
            isLoading: false,
          });
        } catch (error) {
          let errorMessage = 'Registration failed';
          
          if (error instanceof Error) {
            errorMessage = error.message;
          } else if ((error as any).response?.data) {
            const errorData = (error as any).response.data;
            // Handle the backend's error response structure: { error: { message, code, details } }
            if (errorData.error && typeof errorData.error === 'object') {
              errorMessage = errorData.error.message || 'Registration failed';
              // If there are validation details, show the first one
              if (Array.isArray(errorData.error.details)) {
                const firstError = errorData.error.details[0];
                if (firstError?.message) {
                  errorMessage = firstError.message;
                }
              }
            } else {
              // Fallback for other error formats
              errorMessage = errorData.message || 
                            errorData.error ||
                            'Registration failed';
            }
          }
          
          set({
            error: errorMessage,
            isLoading: false,
          });
          throw error;
        }
      },
      
      logout: async () => {
        console.log('[Auth Store] Logout initiated');
        try {
          await api.post('/auth/logout');
        } catch (error) {
          console.log('[Auth Store] Logout API call failed (ignoring):', error);
        } finally {
          setAuthToken(null);
          set({
            user: null,
            accessToken: null,
            refreshToken: null,
            isAuthenticated: false,
            error: null,
          });
          console.log('[Auth Store] Logout complete, auth cleared');
        }
      },
      
      refreshAccessToken: async () => {
        const refreshToken = get().refreshToken;
        if (!refreshToken) {
          throw new Error('No refresh token');
        }
        
        try {
          const response = await api.post('/auth/refresh', { refreshToken });
          const { accessToken, refreshToken: newRefreshToken } = response.data;
          
          setAuthToken(accessToken);
          
          set({
            accessToken,
            refreshToken: newRefreshToken,
          });
        } catch (error) {
          // If refresh fails, logout
          get().logout();
          throw error;
        }
      },
      
      checkAuth: () => {
        const state = get();
        const { accessToken, user, hasHydrated } = state;
        
        // Don't do anything if not hydrated yet
        if (!hasHydrated) {
          console.log('[Auth Store] checkAuth called but store not hydrated yet');
          return;
        }
        
        // Validate both token AND user object exist
        if (accessToken && user && user.id && user.email) {
          setAuthToken(accessToken);
          // Only set authenticated if we have both valid token and complete user object
          set({ isAuthenticated: true, isLoading: false });
        } else {
          // Clear authentication if either token or user is missing/invalid
          set({ 
            isAuthenticated: false,
            isLoading: false,
            accessToken: null,
            refreshToken: null,
            user: null
          });
          setAuthToken(null);
        }
      },
      
      clearError: () => set({ error: null }),
      
      setHasHydrated: (hydrated: boolean) => set({ hasHydrated: hydrated }),
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => ({
        getItem: (name) => {
          const value = localStorage.getItem(name);
          console.log('[Auth Storage] Getting item:', name, 'exists:', !!value);
          return value;
        },
        setItem: (name, value) => {
          console.log('[Auth Storage] Setting item:', name);
          localStorage.setItem(name, value);
          // Verify it was actually saved
          const saved = localStorage.getItem(name);
          if (!saved) {
            console.error('[Auth Storage] Failed to save to localStorage!');
          } else {
            console.log('[Auth Storage] Successfully saved to localStorage');
          }
        },
        removeItem: (name) => {
          console.log('[Auth Storage] Removing item:', name);
          localStorage.removeItem(name);
        },
      })),
      partialize: (state) => ({
        user: state.user,
        accessToken: state.accessToken,
        refreshToken: state.refreshToken,
        isAuthenticated: state.isAuthenticated,
      }),
      version: 1, // Add version for migration support
      migrate: (persistedState: any, version: number) => {
        // Handle any future migrations here
        return persistedState;
      },
      onRehydrateStorage: () => (state) => {
        console.log('[Auth Store] Starting rehydration');
        
        // Log current localStorage state before rehydration
        const currentStorage = localStorage.getItem('auth-storage');
        if (currentStorage) {
          try {
            const parsed = JSON.parse(currentStorage);
            console.log('[Auth Store] Pre-rehydration localStorage:', {
              hasState: !!parsed.state,
              hasToken: !!parsed.state?.accessToken,
              hasUser: !!parsed.state?.user,
              isAuthenticated: parsed.state?.isAuthenticated
            });
          } catch (e) {
            console.error('[Auth Store] Failed to parse pre-rehydration state:', e);
          }
        } else {
          console.log('[Auth Store] No auth-storage found in localStorage before rehydration');
        }
        
        return (state, error) => {
          console.log('[Auth Store] Rehydration callback called', { hasState: !!state, hasError: !!error });

          if (error) {
            console.error('[Auth Store] Rehydration error:', error);
            // Force hydration even on error
            const currentState = useAuthStore.getState();
            currentState.setHasHydrated(true);
          } else if (state) {
            console.log('[Auth Store] Rehydration complete', {
              hasToken: !!state.accessToken,
              hasUser: !!state.user,
              userId: state.user?.id,
              userEmail: state.user?.email,
              isAuthenticated: state.isAuthenticated
            });
            
            // Mark as hydrated first
            state.setHasHydrated(true);
            
            // If we have valid auth data, set the token immediately
            if (state.accessToken && state.user?.id && state.user?.email) {
              console.log('[Auth Store] Found valid auth in storage, setting token');
              setAuthToken(state.accessToken);
              // Ensure authenticated is true
              state.isAuthenticated = true;
              state.isLoading = false;
              
              // Double-check persistence after rehydration
              setTimeout(() => {
                const postHydration = localStorage.getItem('auth-storage');
                console.log('[Auth Store] Post-rehydration check - auth still in localStorage:', !!postHydration);
              }, 100);
            } else {
              console.log('[Auth Store] No valid auth found in storage:', {
                hasToken: !!state.accessToken,
                hasUser: !!state.user,
                userId: state.user?.id,
                userEmail: state.user?.email
              });
              // Clear any partial auth state
              state.accessToken = null;
              state.refreshToken = null;
              state.user = null;
              state.isAuthenticated = false;
              state.isLoading = false;
              setAuthToken(null);
            }
          } else {
            // No stored state - this is a fresh start, still need to mark as hydrated
            console.log('[Auth Store] Rehydration complete with no state (fresh start)');
            const currentState = useAuthStore.getState();
            currentState.setHasHydrated(true);
          }
        };
      },
    }
  )
);