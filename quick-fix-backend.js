// Quick fix for backend startup - install dependencies directly
const { exec } = require('child_process');
const path = require('path');

async function runCommand(command, cwd) {
  return new Promise((resolve, reject) => {
    console.log(`Running: ${command}`);
    exec(command, { cwd }, (error, stdout, stderr) => {
      if (error) {
        console.error(`Error: ${error.message}`);
        resolve({ success: false, error: error.message });
      } else {
        console.log(stdout);
        if (stderr) console.error(stderr);
        resolve({ success: true, stdout });
      }
    });
  });
}

async function fixBackend() {
  const backendDir = path.join(__dirname, 'app', 'backend');
  
  console.log('🔧 Quick fix for backend dependencies...');
  
  // Try to install express directly
  console.log('Installing express directly...');
  const expressResult = await runCommand('npm install express --no-save', backendDir);
  
  if (expressResult.success) {
    console.log('✅ Express installed');
    
    // Try to start with npm run dev
    console.log('Starting backend with npm run dev...');
    const devResult = await runCommand('npm run dev', backendDir);
    
    if (devResult.success) {
      console.log('✅ Backend started successfully!');
    } else {
      console.log('❌ Backend failed to start with npm run dev');
      
      // Try alternative - use npx tsx directly
      console.log('Trying npx tsx directly...');
      const tsxResult = await runCommand('npx tsx watch src/index.ts', backendDir);
      
      if (tsxResult.success) {
        console.log('✅ Backend started with npx tsx!');
      } else {
        console.log('❌ All attempts failed');
      }
    }
  } else {
    console.log('❌ Failed to install express');
  }
}

fixBackend();
