// Test to see what App is being imported
import React from 'react';
import AppDirect from './App';

console.log('=== Direct Import Test ===');
console.log('AppDirect:', AppDirect);
console.log('AppDirect type:', typeof AppDirect);
console.log('AppDirect toString:', AppDirect?.toString?.());

// Try dynamic import
import('./App').then(module => {
  console.log('=== Dynamic Import Test ===');
  console.log('Module:', module);
  console.log('Module.default:', module.default);
  console.log('Module keys:', Object.keys(module));
});

// Check if something is overriding the module
const originalRequire = (window as any).require;
if (originalRequire) {
  console.log('Window.require exists!', originalRequire);
}

export function TestImport() {
  return <div>Check console for import test results</div>;
}