import { PrismaClient } from '@prisma/client';
import { Decimal } from 'decimal.js';
import { seedMaterials } from './seed-materials';

const prisma = new PrismaClient();

// Real electrical material data based on 2024 pricing
const electricalMaterials = [
  // Wire - THHN
  {
    catalog_number: 'THHN-14-BLK-500',
    description: '14 AWG THHN Black Wire - 500ft',
    category: 'WIRE',
    unit: 'ROLL',
    unit_cost: 85.00,
    wire_size: '14',
    wire_type: 'THHN',
    voltage_rating: 600
  },
  {
    catalog_number: 'THHN-12-BLK-500',
    description: '12 AWG THHN Black Wire - 500ft',
    category: 'WIRE',
    unit: 'ROLL',
    unit_cost: 125.00,
    wire_size: '12',
    wire_type: 'THHN',
    voltage_rating: 600
  },
  {
    catalog_number: 'THHN-10-BLK-500',
    description: '10 AWG THHN Black Wire - 500ft',
    category: 'WIRE',
    unit: 'ROLL',
    unit_cost: 195.00,
    wire_size: '10',
    wire_type: 'THHN',
    voltage_rating: 600
  },
  {
    catalog_number: 'THHN-8-BLK-500',
    description: '8 AWG THHN Black Wire - 500ft',
    category: 'WIRE',
    unit: 'ROLL',
    unit_cost: 320.00,
    wire_size: '8',
    wire_type: 'THHN',
    voltage_rating: 600
  },
  {
    catalog_number: 'THHN-6-BLK-500',
    description: '6 AWG THHN Black Wire - 500ft',
    category: 'WIRE',
    unit: 'ROLL',
    unit_cost: 485.00,
    wire_size: '6',
    wire_type: 'THHN',
    voltage_rating: 600
  },

  // NM Cable (Romex)
  {
    catalog_number: 'NM-14-2-250',
    description: '14-2 NM-B Cable w/Ground - 250ft',
    category: 'WIRE',
    unit: 'ROLL',
    unit_cost: 165.00,
    wire_size: '14',
    wire_type: 'NM',
    voltage_rating: 600
  },
  {
    catalog_number: 'NM-12-2-250',
    description: '12-2 NM-B Cable w/Ground - 250ft',
    category: 'WIRE',
    unit: 'ROLL',
    unit_cost: 235.00,
    wire_size: '12',
    wire_type: 'NM',
    voltage_rating: 600
  },
  {
    catalog_number: 'NM-10-3-250',
    description: '10-3 NM-B Cable w/Ground - 250ft',
    category: 'WIRE',
    unit: 'ROLL',
    unit_cost: 425.00,
    wire_size: '10',
    wire_type: 'NM',
    voltage_rating: 600
  },

  // EMT Conduit
  {
    catalog_number: 'EMT-1/2-10',
    description: '1/2" EMT Conduit - 10ft',
    category: 'CONDUIT',
    unit: 'EA',
    unit_cost: 3.85,
    conduit_size: '1/2',
    conduit_type: 'EMT'
  },
  {
    catalog_number: 'EMT-3/4-10',
    description: '3/4" EMT Conduit - 10ft',
    category: 'CONDUIT',
    unit: 'EA',
    unit_cost: 5.25,
    conduit_size: '3/4',
    conduit_type: 'EMT'
  },
  {
    catalog_number: 'EMT-1-10',
    description: '1" EMT Conduit - 10ft',
    category: 'CONDUIT',
    unit: 'EA',
    unit_cost: 7.95,
    conduit_size: '1',
    conduit_type: 'EMT'
  },
  {
    catalog_number: 'EMT-1.25-10',
    description: '1-1/4" EMT Conduit - 10ft',
    category: 'CONDUIT',
    unit: 'EA',
    unit_cost: 12.50,
    conduit_size: '1-1/4',
    conduit_type: 'EMT'
  },

  // Devices
  {
    catalog_number: 'REC-15A-TR-WH',
    description: '15A Tamper Resistant Receptacle - White',
    category: 'DEVICES',
    unit: 'EA',
    unit_cost: 2.85,
    voltage_rating: 125,
    amperage_rating: 15
  },
  {
    catalog_number: 'REC-20A-TR-WH',
    description: '20A Tamper Resistant Receptacle - White',
    category: 'DEVICES',
    unit: 'EA',
    unit_cost: 4.25,
    voltage_rating: 125,
    amperage_rating: 20
  },
  {
    catalog_number: 'SW-15A-SP-WH',
    description: '15A Single Pole Switch - White',
    category: 'DEVICES',
    unit: 'EA',
    unit_cost: 1.95,
    voltage_rating: 120,
    amperage_rating: 15
  },
  {
    catalog_number: 'SW-15A-3W-WH',
    description: '15A 3-Way Switch - White',
    category: 'DEVICES',
    unit: 'EA',
    unit_cost: 3.85,
    voltage_rating: 120,
    amperage_rating: 15
  },
  {
    catalog_number: 'REC-GFCI-20A-WH',
    description: '20A GFCI Receptacle - White',
    category: 'DEVICES',
    unit: 'EA',
    unit_cost: 24.95,
    voltage_rating: 125,
    amperage_rating: 20
  },

  // Panels
  {
    catalog_number: 'PANEL-100A-20SP-MB',
    description: '100A 20 Space Main Breaker Load Center',
    category: 'PANELS',
    unit: 'EA',
    unit_cost: 145.00,
    voltage_rating: 240,
    amperage_rating: 100,
    phase: '1PH'
  },
  {
    catalog_number: 'PANEL-200A-40SP-MB',
    description: '200A 40 Space Main Breaker Load Center',
    category: 'PANELS',
    unit: 'EA',
    unit_cost: 285.00,
    voltage_rating: 240,
    amperage_rating: 200,
    phase: '1PH'
  },
  {
    catalog_number: 'BRK-1P-20A',
    description: '20A Single Pole Circuit Breaker',
    category: 'PANELS',
    unit: 'EA',
    unit_cost: 8.95,
    voltage_rating: 120,
    amperage_rating: 20,
    phase: '1PH'
  },
  {
    catalog_number: 'BRK-2P-30A',
    description: '30A Double Pole Circuit Breaker',
    category: 'PANELS',
    unit: 'EA',
    unit_cost: 18.95,
    voltage_rating: 240,
    amperage_rating: 30,
    phase: '1PH'
  },
  {
    catalog_number: 'BRK-2P-50A',
    description: '50A Double Pole Circuit Breaker',
    category: 'PANELS',
    unit: 'EA',
    unit_cost: 42.95,
    voltage_rating: 240,
    amperage_rating: 50,
    phase: '1PH'
  },

  // Fixtures
  {
    catalog_number: 'FIX-LED-4FT-40W',
    description: '4ft LED Strip Light - 40W',
    category: 'FIXTURES',
    unit: 'EA',
    unit_cost: 38.95,
    voltage_rating: 120
  },
  {
    catalog_number: 'FIX-CAN-6IN-LED',
    description: '6" LED Recessed Can Light',
    category: 'FIXTURES',
    unit: 'EA',
    unit_cost: 28.50,
    voltage_rating: 120
  },
  {
    catalog_number: 'FIX-EXIT-LED-RD',
    description: 'LED Exit Sign - Red',
    category: 'FIXTURES',
    unit: 'EA',
    unit_cost: 45.00,
    voltage_rating: 120
  },

  // Misc
  {
    catalog_number: 'BOX-4SQ-1.5',
    description: '4" Square Box 1-1/2" Deep',
    category: 'MISC',
    unit: 'EA',
    unit_cost: 2.35
  },
  {
    catalog_number: 'BOX-1G-PLASTIC',
    description: '1 Gang Plastic Box',
    category: 'MISC',
    unit: 'EA',
    unit_cost: 0.85
  },
  {
    catalog_number: 'STRAP-GROUND-#10',
    description: '#10 Ground Clamp',
    category: 'MISC',
    unit: 'EA',
    unit_cost: 4.25
  },
  {
    catalog_number: 'WIRENUT-RED-100',
    description: 'Red Wire Nuts - Box of 100',
    category: 'MISC',
    unit: 'BOX',
    unit_cost: 8.95
  }
];

async function seedDatabase(): Promise<void> {
  try {
    console.log('🌱 Starting database seed...');

    // Clear existing data in correct order (respecting foreign key constraints)
    await prisma.$transaction([
      // First delete tables that depend on projects
      prisma.materialPriceHistory.deleteMany(),
      prisma.calculationLog.deleteMany(),
      prisma.laborItem.deleteMany(),
      prisma.materialItem.deleteMany(),
      prisma.inspectionChecklist.deleteMany(),
      prisma.permitDocument.deleteMany(),
      prisma.panel.deleteMany(),
      prisma.estimate.deleteMany(),
      // Then delete projects and their dependencies
      prisma.project.deleteMany(),
      prisma.customer.deleteMany(),
      prisma.user.deleteMany()
    ]);

    console.log('✨ Cleared existing data');

    // Create sample users
    const users = await Promise.all([
      prisma.user.create({
        data: {
          email: '<EMAIL>',
          password_hash: '$2a$12$hBlvO2m3k3MxJ1RdiAyiaO3zM.Fx0uiHKGr0rRF2BtMfW4O5SMgza', // password123
          name: 'System Administrator',
          role: 'admin'
        }
      }),
      prisma.user.create({
        data: {
          email: '<EMAIL>',
          password_hash: '$2a$12$hBlvO2m3k3MxJ1RdiAyiaO3zM.Fx0uiHKGr0rRF2BtMfW4O5SMgza', // password123
          name: 'John Estimator',
          role: 'estimator'
        }
      }),
      prisma.user.create({
        data: {
          email: '<EMAIL>',
          password_hash: '$2a$12$hBlvO2m3k3MxJ1RdiAyiaO3zM.Fx0uiHKGr0rRF2BtMfW4O5SMgza', // password123
          name: 'Mike Electrician',
          role: 'electrician'
        }
      })
    ]);

    console.log(`✅ Created ${users.length} users`);

    // Create sample customers
    const customers = await Promise.all([
      prisma.customer.create({
        data: {
          name: 'ABC Construction Inc.',
          email: '<EMAIL>',
          phone: '**********',
          address: '123 Builder Lane',
          city: 'Dallas',
          state: 'TX',
          zip: '75201',
          license_number: 'TECL-28854',
          credit_limit: 50000,
          payment_terms: 'NET30'
        }
      }),
      prisma.customer.create({
        data: {
          name: 'Johnson Residential',
          email: '<EMAIL>',
          phone: '**********',
          address: '456 Oak Street',
          city: 'Houston',
          state: 'TX',
          zip: '77001',
          payment_terms: 'NET15'
        }
      }),
      prisma.customer.create({
        data: {
          name: 'Premier Commercial Properties',
          email: '<EMAIL>',
          phone: '**********',
          address: '789 Corporate Blvd',
          city: 'Austin',
          state: 'TX',
          zip: '78701',
          license_number: 'TECL-31245',
          credit_limit: 150000,
          payment_terms: 'NET45'
        }
      })
    ]);

    console.log(`✅ Created ${customers.length} customers`);

    // Create sample projects
    const projects = await Promise.all([
      prisma.project.create({
        data: {
          customer_id: customers[0].id,
          name: 'New Office Building - Phase 1',
          address: '1000 Commerce Street',
          city: 'Dallas',
          state: 'TX',
          zip: '75202',
          type: 'COMMERCIAL',
          status: 'IN_PROGRESS',
          voltage_system: '277/480V_3PH',
          service_size: 800,
          square_footage: 25000,
          permit_number: '2024-E-0156',
          permit_expiry: new Date('2025-01-15'),
          inspection_status: 'ROUGH_IN_PASSED'
        }
      }),
      prisma.project.create({
        data: {
          customer_id: customers[1].id,
          name: 'Kitchen Remodel - Johnson Residence',
          address: '456 Oak Street',
          city: 'Houston',
          state: 'TX',
          zip: '77001',
          type: 'RESIDENTIAL',
          status: 'APPROVED',
          voltage_system: '120/240V_1PH',
          service_size: 200,
          square_footage: 2500,
          permit_number: '2024-R-0892'
        }
      }),
      prisma.project.create({
        data: {
          customer_id: customers[2].id,
          name: 'Retail Store Renovation',
          address: '2500 Lamar Street',
          city: 'Austin',
          state: 'TX',
          zip: '78705',
          type: 'COMMERCIAL',
          status: 'PLANNING',
          voltage_system: '208V_3PH',
          service_size: 400,
          square_footage: 8500
        }
      })
    ]);

    console.log(`✅ Created ${projects.length} projects`);

    // Add material price history
    const priceHistory = await Promise.all(
      electricalMaterials.slice(0, 10).map(material =>
        prisma.materialPriceHistory.create({
          data: {
            catalog_number: material.catalog_number,
            supplier: 'Graybar Electric',
            unit_cost: material.unit_cost,
            effective_date: new Date()
          }
        })
      )
    );

    console.log(`✅ Created ${priceHistory.length} price history records`);

    // Create a sample estimate for the first project
    const estimate = await prisma.estimate.create({
      data: {
        project_id: projects[0].id,
        version: 1,
        status: 'SENT',
        valid_until: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
        profit_margin: 15,
        contingency_percent: 10,
        notes: 'Estimate includes all materials and labor for Phase 1 electrical installation',
        terms: 'Payment due within 30 days of invoice. 1.5% monthly interest on overdue amounts.',
        created_by: users[1].id // Use the estimator user
      }
    });

    console.log('✅ Created sample estimate');

    // Add some material items to the estimate
    const materialItems = await Promise.all([
      prisma.materialItem.create({
        data: {
          estimate_id: estimate.id,
          catalog_number: 'THHN-12-BLK-500',
          description: '12 AWG THHN Black Wire - 500ft',
          category: 'WIRE',
          unit: 'ROLL',
          quantity: 15,
          unit_cost: 125.00,
          markup_percent: 35,
          waste_percent: 10,
          tax_rate: 0.0825,
          wire_size: '12',
          wire_type: 'THHN',
          voltage_rating: 600
        }
      }),
      prisma.materialItem.create({
        data: {
          estimate_id: estimate.id,
          catalog_number: 'EMT-3/4-10',
          description: '3/4" EMT Conduit - 10ft',
          category: 'CONDUIT',
          unit: 'EA',
          quantity: 200,
          unit_cost: 5.25,
          markup_percent: 35,
          waste_percent: 5,
          tax_rate: 0.0825,
          conduit_size: '3/4',
          conduit_type: 'EMT'
        }
      }),
      prisma.materialItem.create({
        data: {
          estimate_id: estimate.id,
          catalog_number: 'PANEL-200A-40SP-MB',
          description: '200A 40 Space Main Breaker Load Center',
          category: 'PANELS',
          unit: 'EA',
          quantity: 2,
          unit_cost: 285.00,
          markup_percent: 35,
          waste_percent: 0,
          tax_rate: 0.0825,
          voltage_rating: 240,
          amperage_rating: 200,
          phase: '1PH'
        }
      })
    ]);

    console.log(`✅ Created ${materialItems.length} material items`);

    // Add labor items
    const laborItems = await Promise.all([
      prisma.laborItem.create({
        data: {
          estimate_id: estimate.id,
          description: 'Install main electrical panels',
          trade: 'ELECTRICIAN',
          hours: 16,
          rate: 85.00,
          overtime_hours: 0,
          overtime_rate: 127.50,
          burden_percent: 35
        }
      }),
      prisma.laborItem.create({
        data: {
          estimate_id: estimate.id,
          description: 'Pull wire and install conduit',
          trade: 'ELECTRICIAN',
          hours: 80,
          rate: 85.00,
          overtime_hours: 8,
          overtime_rate: 127.50,
          burden_percent: 35
        }
      }),
      prisma.laborItem.create({
        data: {
          estimate_id: estimate.id,
          description: 'Assist with wire pulling',
          trade: 'APPRENTICE',
          hours: 80,
          rate: 45.00,
          overtime_hours: 8,
          overtime_rate: 67.50,
          burden_percent: 35
        }
      })
    ]);

    console.log(`✅ Created ${laborItems.length} labor items`);

    // Create a sample calculation log (commented out for now)
    // await prisma.calculationLog.create({
    //   data: {
    //     calculation_type: 'LOAD_CALC',
    //     project_id: projects[0].id,
    //     input_data: JSON.stringify({
    //       square_footage: 25000,
    //       building_type: 'OFFICE',
    //       voltage_system: '277/480V_3PH'
    //     }),
    //     output_data: JSON.stringify({
    //       general_lighting_va: 87500,
    //       receptacle_va: 25000,
    //       hvac_va: 150000,
    //       total_computed_va: 262500,
    //       demand_factor: 0.9,
    //       total_demand_va: 236250,
    //       required_amperage: 285,
    //       recommended_service: 800
    //     }),
    //     nec_references: JSON.stringify(['220.12', '220.14', '220.82']),
    //     performed_by: users[2].id // Use the electrician user
    //   }
    // });

    // console.log('✅ Created sample calculation log');

    console.log('🎉 Database seeding completed successfully!');
    
    // Display summary
    const summary = await prisma.$transaction([
      prisma.customer.count(),
      prisma.project.count(),
      prisma.estimate.count(),
      prisma.materialItem.count(),
      prisma.laborItem.count(),
      prisma.materialPriceHistory.count(),
      prisma.calculationLog.count()
    ]);

    console.log('\n📊 Database Summary:');
    console.log(`   Customers: ${summary[0]}`);
    console.log(`   Projects: ${summary[1]}`);
    console.log(`   Estimates: ${summary[2]}`);
    console.log(`   Material Items: ${summary[3]}`);
    console.log(`   Labor Items: ${summary[4]}`);
    console.log(`   Price History: ${summary[5]}`);
    console.log(`   Calculation Logs: ${summary[6]}`);

  } catch (error) {
    console.error('❌ Error seeding database:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seed function
seedDatabase().catch((error) => {
  console.error('Failed to seed database:', error);
  process.exit(1);
});