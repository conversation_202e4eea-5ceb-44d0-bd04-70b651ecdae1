
// Restore original workspace configuration
const fs = require('fs');
const path = require('path');

async function restore() {
  console.log('🔄 Restoring original workspace configuration...');
  
  const appDir = path.join(__dirname, 'app');
  const backendDir = path.join(appDir, 'backend');
  const frontendDir = path.join(appDir, 'frontend');
  
  // Restore app package.json
  const appBackup = path.join(appDir, 'package-workspace-backup.json');
  const appPackage = path.join(appDir, 'package.json');
  
  if (fs.existsSync(appBackup)) {
    fs.copyFileSync(appBackup, appPackage);
    fs.unlinkSync(appBackup);
    console.log('✅ Restored app package.json');
  }
  
  // Restore backend package.json
  const backendBackup = path.join(backendDir, 'package-original.json');
  const backendPackage = path.join(backendDir, 'package.json');
  
  if (fs.existsSync(backendBackup)) {
    fs.copyFileSync(backendBackup, backendPackage);
    fs.unlinkSync(backendBackup);
    console.log('✅ Restored backend package.json');
  }
  
  // Restore frontend package.json
  const frontendBackup = path.join(frontendDir, 'package-original.json');
  const frontendPackage = path.join(frontendDir, 'package.json');
  
  if (fs.existsSync(frontendBackup)) {
    fs.copyFileSync(frontendBackup, frontendPackage);
    fs.unlinkSync(frontendBackup);
    console.log('✅ Restored frontend package.json');
  }
  
  console.log('🎉 Workspace configuration restored!');
}

restore().catch(console.error);
