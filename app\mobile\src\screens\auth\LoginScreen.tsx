import React, { useState } from 'react';
import {
  Box,
  VStack,
  HStack,
  Input,
  Button,
  Text,
  Heading,
  Icon,
  Pressable,
  FormControl,
  WarningOutlineIcon,
  Center,
  KeyboardAvoidingView,
  ScrollView,
} from 'native-base';
import { Platform } from 'react-native';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import { useDispatch, useSelector } from 'react-redux';
import { AuthStackScreenProps } from '@types/navigation';
import { AppDispatch, RootState } from '@store/index';
import { login, demoLogin } from '@store/slices/authSlice';
import { showToast } from '@store/slices/uiSlice';

type Props = AuthStackScreenProps<'Login'>;

const LoginScreen: React.FC<Props> = ({ navigation }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { isLoading } = useSelector((state: RootState) => state.auth);
  
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showPassword, setShowPassword] = useState(false);

  const validate = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Invalid email format';
    }
    
    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleLogin = async () => {
    if (!validate()) return;

    try {
      await dispatch(login(formData)).unwrap();
      dispatch(showToast({ message: 'Login successful!', type: 'success' }));
    } catch (error: any) {
      dispatch(showToast({ 
        message: error.message || 'Login failed. Please try again.', 
        type: 'error' 
      }));
    }
  };

  return (
    <KeyboardAvoidingView
      flex={1}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        contentContainerStyle={{ flexGrow: 1 }}
        keyboardShouldPersistTaps="handled"
      >
        <Center flex={1} px={4} bg="white">
          <VStack space={6} w="100%" maxW="300" alignItems="center">
            <VStack space={2} alignItems="center">
              <Icon as={MaterialIcons} name="electric-bolt" size={16} color="primary.500" />
              <Heading size="xl" color="primary.600">
                Electrical Contractor
              </Heading>
              <Text fontSize="md" color="gray.600">
                Sign in to your account
              </Text>
            </VStack>

            <VStack space={4} w="100%">
              <FormControl isInvalid={'email' in errors}>
                <FormControl.Label>Email</FormControl.Label>
                <Input
                  placeholder="Enter email"
                  value={formData.email}
                  onChangeText={value => setFormData({ ...formData, email: value })}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  InputLeftElement={
                    <Icon as={MaterialIcons} name="email" size={5} ml={2} color="muted.400" />
                  }
                />
                <FormControl.ErrorMessage leftIcon={<WarningOutlineIcon size="xs" />}>
                  {errors.email}
                </FormControl.ErrorMessage>
              </FormControl>

              <FormControl isInvalid={'password' in errors}>
                <FormControl.Label>Password</FormControl.Label>
                <Input
                  placeholder="Enter password"
                  value={formData.password}
                  onChangeText={value => setFormData({ ...formData, password: value })}
                  type={showPassword ? 'text' : 'password'}
                  InputLeftElement={
                    <Icon as={MaterialIcons} name="lock" size={5} ml={2} color="muted.400" />
                  }
                  InputRightElement={
                    <Pressable onPress={() => setShowPassword(!showPassword)}>
                      <Icon
                        as={MaterialIcons}
                        name={showPassword ? 'visibility' : 'visibility-off'}
                        size={5}
                        mr={2}
                        color="muted.400"
                      />
                    </Pressable>
                  }
                />
                <FormControl.ErrorMessage leftIcon={<WarningOutlineIcon size="xs" />}>
                  {errors.password}
                </FormControl.ErrorMessage>
              </FormControl>

              <Button
                mt={2}
                onPress={handleLogin}
                isLoading={isLoading}
                isLoadingText="Signing in..."
              >
                Sign In
              </Button>

              <Button
                mt={2}
                variant="outline"
                onPress={() => {
                  dispatch(demoLogin());
                  dispatch(showToast({ message: 'Demo mode activated!', type: 'success' }));
                }}
              >
                Demo Mode (No Backend Required)
              </Button>

              <HStack justifyContent="space-between">
                <Pressable onPress={() => navigation.navigate('ForgotPassword')}>
                  <Text color="primary.500" fontSize="sm">
                    Forgot password?
                  </Text>
                </Pressable>
              </HStack>
            </VStack>

            <HStack space={2}>
              <Text color="gray.600">Don't have an account?</Text>
              <Pressable onPress={() => navigation.navigate('Register')}>
                <Text color="primary.500" fontWeight="medium">
                  Sign Up
                </Text>
              </Pressable>
            </HStack>
          </VStack>
        </Center>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

export default LoginScreen;