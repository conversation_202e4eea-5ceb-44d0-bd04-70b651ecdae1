import React from 'react';
import { View, Text, StyleSheet, Modal } from 'react-native';

interface CalculatorModalProps {
  visible?: boolean;
  onClose?: () => void;
}

const CalculatorModal: React.FC<CalculatorModalProps> = ({ visible = true, onClose }) => {
  return (
    <Modal visible={visible} animationType="slide" onRequestClose={onClose}>
      <View style={styles.container}>
        <Text style={styles.title}>Calculator</Text>
        <Text>Calculator functionality coming soon...</Text>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#fff', justifyContent: 'center', alignItems: 'center' },
  title: { fontSize: 24, fontWeight: 'bold', marginBottom: 20 }
});

export default CalculatorModal;
