{"name": "@electrical/backend", "version": "1.0.0", "description": "Backend API for electrical contracting application", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "npx tsc", "start": "node dist/index.js", "test": "npx jest", "test:watch": "npx jest --watch", "test:coverage": "npx jest --coverage", "test:coverage:html": "npx jest --coverage --coverageReporters=html", "test:load-calculation": "npx jest src/services/calculations/__tests__/load-calculation.test.ts", "test:voltage-drop": "npx jest src/services/calculations/__tests__/voltage-drop.test.ts", "test:wire-size": "npx jest src/services/calculations/__tests__/wire-size.test.ts", "test:conduit-fill": "npx jest src/services/calculations/__tests__/conduit-fill.test.ts", "test:calculations": "npx jest src/services/calculations/__tests__", "test:ci": "npx jest --ci --coverage --maxWorkers=2", "lint": "npx eslint src --ext .ts", "typecheck": "npx tsc --noEmit", "db:seed": "npx tsx src/database/seed.ts", "db:migrate": "npx prisma migrate dev", "db:deploy": "npx prisma migrate deploy", "db:studio": "npx prisma studio", "clear:rate-limits": "npx tsx scripts/clear-rate-limits.ts"}, "dependencies": {"@google/genai": "^1.9.0", "@google/generative-ai": "^0.24.1", "@prisma/client": "^5.8.1", "@types/nodemailer": "^6.4.17", "@types/pdfkit": "^0.17.0", "@types/user-agents": "^1.0.4", "axios": "^1.6.5", "bcryptjs": "^2.4.3", "bullmq": "^5.1.5", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "csv-parser": "^3.0.0", "date-fns": "^3.3.1", "decimal.js": "^10.4.3", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "ioredis": "^5.3.2", "json2csv": "^5.0.7", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "nodemailer": "^7.0.5", "pdf-lib": "^1.17.1", "pdfkit": "^0.17.1", "playwright": "^1.54.1", "prisma": "^5.8.1", "qrcode": "^1.5.3", "rate-limiter-flexible": "^3.0.0", "socket.io": "^4.7.4", "user-agents": "^1.1.601", "uuid": "^9.0.1", "validator": "^13.15.15", "winston": "^3.11.0", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.11", "@types/json2csv": "^5.0.7", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/node": "^20.11.5", "@types/qrcode": "^1.5.5", "@types/uuid": "^9.0.7", "@types/validator": "^13.15.2", "@typescript-eslint/eslint-plugin": "^6.19.0", "@typescript-eslint/parser": "^6.19.0", "eslint": "^8.56.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "tsx": "^4.7.0", "typescript": "^5.3.3"}}