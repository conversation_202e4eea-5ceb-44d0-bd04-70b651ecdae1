import { Router } from 'express';
import { z } from 'zod';
import { authenticate, authorize } from '../middleware/auth';
import { validateInput } from '../security/validators';
import { rateLimitMiddleware } from '../security/rate-limiting';
import { queryAuditLogs, detectSuspiciousActivity } from '../security/audit';
import { listApiKeys, createApi<PERSON><PERSON>, revoke<PERSON>pi<PERSON><PERSON>, API_KEY_SCOPES } from '../security/api-keys';
import { prisma } from '../database/prisma';
import { AppError } from '../middleware/errorHandler';

const router: Router = Router();

// All security endpoints require admin role
router.use(authenticate);
router.use(authorize('admin'));

// Get security dashboard data
router.get('/dashboard', async (req, res, next) => {
  try {
    const now = new Date();
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    // Get security metrics
    const [
      recentLogins,
      failedLogins,
      activeUsers,
      suspiciousEvents,
      rateLimitViolations,
      apiKeyUsage
    ] = await Promise.all([
      // Recent successful logins
      prisma.$queryRaw`
        SELECT COUNT(*) as count
        FROM audit_logs
        WHERE action = 'auth.login.success'
        AND created_at > ${oneDayAgo}
      `,
      
      // Failed login attempts
      prisma.$queryRaw`
        SELECT COUNT(*) as count, COUNT(DISTINCT user_id) as unique_users
        FROM audit_logs
        WHERE action = 'auth.login.failed'
        AND created_at > ${oneDayAgo}
      `,
      
      // Active users (logged in within last hour)
      prisma.$queryRaw`
        SELECT COUNT(DISTINCT user_id) as count
        FROM audit_logs
        WHERE created_at > datetime('now', '-1 hour')
        AND user_id IS NOT NULL
      `,
      
      // Suspicious events
      prisma.$queryRaw`
        SELECT * FROM security_events
        WHERE resolved = false
        ORDER BY created_at DESC
        LIMIT 10
      `,
      
      // Rate limit violations
      prisma.$queryRaw`
        SELECT identifier, limiter_name, violations_count, last_violation_at
        FROM rate_limit_violations
        WHERE last_violation_at > ${oneDayAgo}
        ORDER BY violations_count DESC
        LIMIT 10
      `,
      
      // API key usage
      prisma.$queryRaw`
        SELECT name, usage_count, last_used_at
        FROM api_keys
        WHERE is_active = true
        ORDER BY usage_count DESC
        LIMIT 10
      `
    ]);
    
    // Get security trends
    const loginTrends = await prisma.$queryRaw`
      SELECT 
        DATE(created_at) as date,
        COUNT(CASE WHEN action = 'auth.login.success' THEN 1 END) as successful,
        COUNT(CASE WHEN action = 'auth.login.failed' THEN 1 END) as failed
      FROM audit_logs
      WHERE created_at > ${oneWeekAgo}
      AND action IN ('auth.login.success', 'auth.login.failed')
      GROUP BY DATE(created_at)
      ORDER BY date DESC
    `;
    
    res.json({
      metrics: {
        recentLogins: (recentLogins as any)[0].count,
        failedLogins: (failedLogins as any)[0],
        activeUsers: (activeUsers as any)[0].count,
        unresolvedEvents: (suspiciousEvents as any[]).length
      },
      suspiciousEvents,
      rateLimitViolations,
      apiKeyUsage,
      loginTrends
    });
  } catch (error) {
    next(error);
  }
});

// Query audit logs
router.get('/audit-logs', 
  validateInput(z.object({
    userId: z.string().optional(),
    action: z.string().optional(),
    resourceType: z.string().optional(),
    startDate: z.string().datetime().optional(),
    endDate: z.string().datetime().optional(),
    limit: z.number().min(1).max(1000).default(100),
    offset: z.number().min(0).default(0)
  })),
  async (req, res, next) => {
    try {
      const filters = {
        ...req.query,
        startDate: req.query.startDate ? new Date(req.query.startDate as string) : undefined,
        endDate: req.query.endDate ? new Date(req.query.endDate as string) : undefined,
        limit: parseInt(req.query.limit as string) || 100,
        offset: parseInt(req.query.offset as string) || 0
      };
      
      const logs = await queryAuditLogs(filters);
      
      res.json({ logs });
    } catch (error) {
      next(error);
    }
  }
);

// Get security events
router.get('/events',
  validateInput(z.object({
    resolved: z.boolean().optional(),
    severity: z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']).optional(),
    limit: z.number().min(1).max(100).default(50)
  })),
  async (req, res, next) => {
    try {
      const events = await prisma.$queryRaw`
        SELECT * FROM security_events
        WHERE 1=1
        ${req.query.resolved !== undefined ? `AND resolved = ${req.query.resolved}` : ''}
        ${req.query.severity ? `AND severity = ${req.query.severity}` : ''}
        ORDER BY created_at DESC
        LIMIT ${req.query.limit || 50}
      `;
      
      res.json({ events });
    } catch (error) {
      next(error);
    }
  }
);

// Resolve security event
router.patch('/events/:eventId/resolve',
  async (req, res, next) => {
    try {
      await prisma.$executeRaw`
        UPDATE security_events
        SET resolved = true,
            resolved_by = ${req.user!.userId},
            resolved_at = ${new Date()}
        WHERE id = ${req.params.eventId}
      `;
      
      res.json({ success: true });
    } catch (error) {
      next(error);
    }
  }
);

// API Key Management
router.get('/api-keys', async (req, res, next) => {
  try {
    const keys = await listApiKeys(req.user!.userId);
    res.json({ keys });
  } catch (error) {
    next(error);
  }
});

router.post('/api-keys',
  validateInput(z.object({
    name: z.string().min(1).max(100),
    scopes: z.array(z.enum(Object.values(API_KEY_SCOPES) as [string, ...string[]])),
    expiresInDays: z.number().min(1).max(365).optional()
  })),
  rateLimitMiddleware.apiKey,
  async (req, res, next) => {
    try {
      const expiresAt = req.body.expiresInDays
        ? new Date(Date.now() + req.body.expiresInDays * 24 * 60 * 60 * 1000)
        : undefined;
      
      const { key, id } = await createApiKey(
        req.user!.userId,
        req.body.name,
        req.body.scopes,
        expiresAt
      );
      
      res.json({ 
        id, 
        key,
        message: 'Store this key securely. It will not be shown again.'
      });
    } catch (error) {
      next(error);
    }
  }
);

router.delete('/api-keys/:keyId', async (req, res, next) => {
  try {
    await revokeApiKey(req.user!.userId, req.params.keyId);
    res.json({ success: true });
  } catch (error) {
    next(error);
  }
});

// User security settings
router.get('/users/:userId/security', async (req, res, next) => {
  try {
    const user = await prisma.user.findUnique({
      where: { id: req.params.userId },
      select: {
        id: true,
        email: true,
        last_login_at: true,
        locked_until: true
      }
    });
    
    if (!user) {
      throw new AppError(404, 'User not found', true, 'USER_NOT_FOUND');
    }
    
    // Check for suspicious activity
    const suspicious = await detectSuspiciousActivity(user.id);
    
    res.json({
      ...user,
      suspicious_activity_detected: suspicious
    });
  } catch (error) {
    next(error);
  }
});

// Lock/unlock user account
router.post('/users/:userId/lock', async (req, res, next) => {
  try {
    const lockUntil = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
    
    await prisma.user.update({
      where: { id: req.params.userId },
      data: { locked_until: lockUntil }
    });
    
    res.json({ locked_until: lockUntil });
  } catch (error) {
    next(error);
  }
});

router.post('/users/:userId/unlock', async (req, res, next) => {
  try {
    await prisma.user.update({
      where: { id: req.params.userId },
      data: { 
        locked_until: null
      }
    });
    
    res.json({ success: true });
  } catch (error) {
    next(error);
  }
});

// Export security report
router.get('/reports/export',
  rateLimitMiddleware.export,
  async (req, res, next) => {
    try {
      const reportData = {
        generated_at: new Date(),
        generated_by: req.user!.email,
        period: {
          start: req.query.startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
          end: req.query.endDate || new Date()
        },
        summary: await generateSecuritySummary(),
        details: await generateSecurityDetails()
      };
      
      res.json(reportData);
    } catch (error) {
      next(error);
    }
  }
);

// Helper functions
async function generateSecuritySummary() {
  const [
    totalUsers,
    activeUsers,
    totalLogins,
    failedLogins,
    securityEvents
  ] = await Promise.all([
    prisma.user.count({ where: { deleted_at: null } }),
    prisma.$queryRaw`
      SELECT COUNT(DISTINCT user_id) as count
      FROM audit_logs
      WHERE created_at > datetime('now', '-30 days')
    `,
    prisma.$queryRaw`
      SELECT COUNT(*) as count
      FROM audit_logs
      WHERE action = 'auth.login.success'
      AND created_at > datetime('now', '-30 days')
    `,
    prisma.$queryRaw`
      SELECT COUNT(*) as count
      FROM audit_logs
      WHERE action = 'auth.login.failed'
      AND created_at > datetime('now', '-30 days')
    `,
    prisma.$queryRaw`
      SELECT 
        severity,
        COUNT(*) as count,
        COUNT(CASE WHEN resolved = false THEN 1 END) as unresolved
      FROM security_events
      WHERE created_at > datetime('now', '-30 days')
      GROUP BY severity
    `
  ]);
  
  return {
    totalUsers,
    activeUsers: (activeUsers as any)[0].count,
    totalLogins: (totalLogins as any)[0].count,
    failedLogins: (failedLogins as any)[0].count,
    securityEvents
  };
}

async function generateSecurityDetails() {
  const [
    topFailedLogins,
    apiKeyActivity,
    dataExports,
    suspiciousPatterns
  ] = await Promise.all([
    prisma.$queryRaw`
      SELECT 
        user_id,
        COUNT(*) as attempts,
        MAX(created_at) as last_attempt
      FROM audit_logs
      WHERE action = 'auth.login.failed'
      AND created_at > datetime('now', '-30 days')
      GROUP BY user_id
      ORDER BY attempts DESC
      LIMIT 10
    `,
    prisma.$queryRaw`
      SELECT 
        name,
        usage_count,
        last_used_at,
        created_at
      FROM api_keys
      WHERE is_active = true
      ORDER BY usage_count DESC
    `,
    prisma.$queryRaw`
      SELECT 
        export_type,
        COUNT(*) as count,
        SUM(file_size) as total_size
      FROM data_export_log
      WHERE created_at > datetime('now', '-30 days')
      GROUP BY export_type
    `,
    prisma.$queryRaw`
      SELECT 
        action,
        COUNT(*) as count,
        COUNT(DISTINCT user_id) as unique_users
      FROM audit_logs
      WHERE created_at > datetime('now', '-30 days')
      GROUP BY action
      HAVING count > 100
      ORDER BY count DESC
    `
  ]);
  
  return {
    topFailedLogins,
    apiKeyActivity,
    dataExports,
    suspiciousPatterns
  };
}

export { router as securityRouter };