@echo off
setlocal enabledelayedexpansion

echo =====================================
echo Electrical Contractor Mobile App
echo Android Build Script (npm-based)
echo =====================================
echo.

REM Check if we're in the correct directory
if not exist "android\settings.gradle" (
    if exist "mobile\android\settings.gradle" (
        echo Switching to mobile directory...
        cd mobile
    ) else (
        echo ERROR: Cannot find Android project files
        echo Please run this script from the project root or mobile directory
        echo Current directory: %CD%
        pause
        exit /b 1
    )
)

REM Set Java Home (using path from SET_JAVA.bat)
echo Setting up Java environment...
set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot
set PATH=%JAVA_HOME%\bin;%PATH%

REM Verify Java installation
java -version 2>nul
if %errorlevel% neq 0 (
    echo.
    echo ERROR: Java is not properly configured
    echo Expected JAVA_HOME: %JAVA_HOME%
    echo.
    echo Please ensure Java 17 is installed at the expected location
    echo Download from: https://adoptium.net/
    echo.
    pause
    exit /b 1
)

echo Java configured successfully
echo JAVA_HOME: %JAVA_HOME%
echo.

REM Check Node.js and npm
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js 18 or higher
    pause
    exit /b 1
)

npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: npm is not available
    echo Please ensure npm is installed with Node.js
    pause
    exit /b 1
)

echo Node.js and npm are available
echo.

REM Menu for build options
echo Select build option:
echo 1. Quick Build (keep existing dependencies)
echo 2. Clean Build (reinstall dependencies)
echo 3. Deep Clean Build (remove all caches and reinstall)
echo 4. Build Release APK
echo 5. Run on Device/Emulator
echo.
choice /c 12345 /n /m "Enter your choice (1-5): "
set BUILD_OPTION=%errorlevel%

REM Handle Deep Clean
if %BUILD_OPTION% equ 3 (
    echo.
    echo Performing deep clean...
    
    REM Clean gradle directories
    if exist "android\.gradle" (
        echo Removing android\.gradle...
        rmdir /s /q "android\.gradle"
    )
    if exist "android\app\build" (
        echo Removing android\app\build...
        rmdir /s /q "android\app\build"
    )
    if exist "android\build" (
        echo Removing android\build...
        rmdir /s /q "android\build"
    )
    
    REM Clean gradle cache
    if exist "%USERPROFILE%\.gradle\caches" (
        echo Clearing global gradle cache...
        rmdir /s /q "%USERPROFILE%\.gradle\caches"
    )
    
    REM Remove node_modules
    if exist "node_modules" (
        echo Removing node_modules...
        rmdir /s /q "node_modules"
    )
    
    REM Clear npm cache
    echo Clearing npm cache...
    call npm cache clean --force
    
    echo Deep clean completed!
    echo.
)

REM Handle Clean Build or Deep Clean
if %BUILD_OPTION% equ 2 (
    echo.
    echo Performing clean build...
    if exist "node_modules" (
        echo Removing node_modules...
        rmdir /s /q "node_modules"
    )
)

REM Install dependencies if needed
if not exist "node_modules" (
    echo.
    echo Installing dependencies with npm...
    echo This may take several minutes...
    call npm install --legacy-peer-deps
    if %errorlevel% neq 0 (
        echo.
        echo ERROR: Failed to install dependencies
        echo Try running: npm install --force
        pause
        exit /b 1
    )
    echo Dependencies installed successfully!
    echo.
)

REM Clean gradle before build
echo.
echo Cleaning gradle project...
cd android
call gradlew.bat --init-script init.gradle clean 2>nul
cd ..

REM Build based on option
if %BUILD_OPTION% equ 4 (
    REM Build Release APK
    echo.
    echo Building Release APK...
    echo.
    cd android
    call gradlew.bat --init-script init.gradle assembleRelease
    set BUILD_RESULT=%errorlevel%
    cd ..
    
    if !BUILD_RESULT! equ 0 (
        echo.
        echo =====================================
        echo RELEASE BUILD SUCCESSFUL!
        echo =====================================
        echo APK location: android\app\build\outputs\apk\release\app-release.apk
        echo.
        echo Note: The APK needs to be signed before distribution
    ) else (
        echo.
        echo =====================================
        echo RELEASE BUILD FAILED
        echo =====================================
        echo Check the error messages above
    )
) else if %BUILD_OPTION% equ 5 (
    REM Run on device
    echo.
    echo Checking for connected devices...
    adb devices
    echo.
    echo Starting React Native Metro bundler and installing app...
    call npx react-native run-android
    
    if %errorlevel% neq 0 (
        echo.
        echo If the Metro bundler failed to start, try:
        echo 1. Open a new terminal and run: npm start
        echo 2. Then run this script again with option 1
    )
) else (
    REM Build Debug APK
    echo.
    echo Building Debug APK...
    echo.
    cd android
    call gradlew.bat --init-script init.gradle assembleDebug
    set BUILD_RESULT=%errorlevel%
    cd ..
    
    if !BUILD_RESULT! equ 0 (
        echo.
        echo =====================================
        echo DEBUG BUILD SUCCESSFUL!
        echo =====================================
        echo APK location: android\app\build\outputs\apk\debug\app-debug.apk
        echo.
        echo To install on a connected device:
        echo   adb install android\app\build\outputs\apk\debug\app-debug.apk
        echo.
        echo Or run this script with option 5 to install and run automatically
    ) else (
        echo.
        echo =====================================
        echo DEBUG BUILD FAILED
        echo =====================================
        echo.
        echo Common solutions:
        echo 1. Run this script with option 3 (Deep Clean Build)
        echo 2. Check android\local.properties has correct SDK path
        echo 3. Ensure Android SDK is properly installed
        echo 4. Check for specific error messages above
    )
)

echo.
echo Press any key to exit...
pause >nul