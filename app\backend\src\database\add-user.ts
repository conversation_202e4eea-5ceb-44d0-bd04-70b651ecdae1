import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function addUser() {
  try {
    const user = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password_hash: '$2a$12$iiYWAG7OWPPHhbRJb06jNunXR/eQ2FTc83TuhiB/1r1OjHAnBhHku', // itsMike818!
        name: '<PERSON>',
        role: 'admin'
      }
    });
    
    console.log('✅ User created successfully:', user.email);
  } catch (error) {
    console.error('❌ Error creating user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

addUser().catch(console.error);