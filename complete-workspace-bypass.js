// Complete workspace bypass solution
const fs = require('fs');
const path = require('path');
const { spawn, exec } = require('child_process');

async function runCommand(command, cwd, timeout = 60000) {
  return new Promise((resolve) => {
    console.log(`\n🔧 Running: ${command}`);
    console.log(`📁 Directory: ${cwd}`);
    
    const process = exec(command, { cwd, timeout }, (error, stdout, stderr) => {
      if (error) {
        console.error(`❌ Error: ${error.message}`);
        resolve({ success: false, error: error.message, stdout, stderr });
      } else {
        console.log(`✅ Success: ${stdout}`);
        if (stderr) console.warn(`⚠️ Warning: ${stderr}`);
        resolve({ success: true, stdout, stderr });
      }
    });
  });
}

async function disableWorkspaceConfiguration() {
  console.log('\n🔧 === DISABLING WORKSPACE CONFIGURATION ===');
  
  const appDir = path.join(__dirname, 'app');
  const appPackageJson = path.join(appDir, 'package.json');
  const appPackageBackup = path.join(appDir, 'package-workspace-backup.json');
  
  try {
    if (fs.existsSync(appPackageJson)) {
      // Backup the workspace package.json
      fs.copyFileSync(appPackageJson, appPackageBackup);
      console.log('✅ Backed up workspace package.json');
      
      // Create a minimal package.json without workspaces
      const minimalPackage = {
        "name": "electrical-contracting-saas",
        "version": "1.0.0",
        "description": "Electrical contracting application",
        "private": true,
        "scripts": {
          "dev": "echo 'Use individual service directories'"
        }
      };
      
      fs.writeFileSync(appPackageJson, JSON.stringify(minimalPackage, null, 2));
      console.log('✅ Created minimal package.json without workspaces');
    }
    
    return true;
  } catch (error) {
    console.error('❌ Failed to disable workspace configuration:', error.message);
    return false;
  }
}

async function createStandaloneServices() {
  console.log('\n🔧 === CREATING STANDALONE SERVICES ===');
  
  const backendDir = path.join(__dirname, 'app', 'backend');
  const frontendDir = path.join(__dirname, 'app', 'frontend');
  
  // Backend standalone package.json
  const backendStandalone = {
    "name": "electrical-backend",
    "version": "1.0.0",
    "main": "dist/index.js",
    "scripts": {
      "dev": "tsx watch src/index.ts",
      "build": "tsc",
      "start": "node dist/index.js"
    },
    "dependencies": {
      "express": "^4.18.2",
      "cors": "^2.8.5",
      "helmet": "^7.1.0",
      "dotenv": "^16.3.1",
      "bcryptjs": "^2.4.3",
      "jsonwebtoken": "^9.0.2",
      "uuid": "^9.0.1",
      "zod": "^3.22.4",
      "winston": "^3.11.0",
      "@prisma/client": "^5.8.1",
      "prisma": "^5.8.1"
    },
    "devDependencies": {
      "@types/express": "^4.17.21",
      "@types/cors": "^2.8.17",
      "@types/bcryptjs": "^2.4.6",
      "@types/jsonwebtoken": "^9.0.5",
      "@types/node": "^20.11.5",
      "@types/uuid": "^9.0.7",
      "tsx": "^4.7.0",
      "typescript": "^5.3.3"
    }
  };
  
  // Frontend standalone package.json
  const frontendStandalone = {
    "name": "electrical-frontend",
    "version": "1.0.0",
    "type": "module",
    "scripts": {
      "dev": "vite",
      "build": "tsc && vite build",
      "preview": "vite preview"
    },
    "dependencies": {
      "react": "^18.2.0",
      "react-dom": "^18.2.0",
      "react-router-dom": "^6.20.1",
      "axios": "^1.6.5",
      "zustand": "^4.4.7",
      "zod": "^3.22.4",
      "@headlessui/react": "^1.7.17",
      "@heroicons/react": "^2.0.18"
    },
    "devDependencies": {
      "@types/react": "^18.2.43",
      "@types/react-dom": "^18.2.17",
      "@vitejs/plugin-react": "^4.2.1",
      "typescript": "^5.2.2",
      "vite": "^5.0.8",
      "tailwindcss": "^3.3.6",
      "autoprefixer": "^10.4.16",
      "postcss": "^8.4.32"
    }
  };
  
  try {
    // Backup and replace backend package.json
    const backendPackage = path.join(backendDir, 'package.json');
    const backendBackup = path.join(backendDir, 'package-original.json');
    
    if (fs.existsSync(backendPackage)) {
      fs.copyFileSync(backendPackage, backendBackup);
    }
    fs.writeFileSync(backendPackage, JSON.stringify(backendStandalone, null, 2));
    console.log('✅ Created standalone backend package.json');
    
    // Backup and replace frontend package.json
    const frontendPackage = path.join(frontendDir, 'package.json');
    const frontendBackup = path.join(frontendDir, 'package-original.json');
    
    if (fs.existsSync(frontendPackage)) {
      fs.copyFileSync(frontendPackage, frontendBackup);
    }
    fs.writeFileSync(frontendPackage, JSON.stringify(frontendStandalone, null, 2));
    console.log('✅ Created standalone frontend package.json');
    
    return true;
  } catch (error) {
    console.error('❌ Failed to create standalone services:', error.message);
    return false;
  }
}

async function installDependencies() {
  console.log('\n📦 === INSTALLING DEPENDENCIES ===');
  
  const backendDir = path.join(__dirname, 'app', 'backend');
  const frontendDir = path.join(__dirname, 'app', 'frontend');
  
  // Clean and install backend
  console.log('Installing backend dependencies...');
  await runCommand('rmdir /s /q node_modules 2>nul || echo "Cleaned"', backendDir);
  const backendResult = await runCommand('npm install --no-package-lock', backendDir);
  
  // Clean and install frontend
  console.log('Installing frontend dependencies...');
  await runCommand('rmdir /s /q node_modules 2>nul || echo "Cleaned"', frontendDir);
  const frontendResult = await runCommand('npm install --no-package-lock', frontendDir);
  
  return backendResult.success && frontendResult.success;
}

async function startServicesAndTest() {
  console.log('\n🚀 === STARTING SERVICES ===');
  
  const backendDir = path.join(__dirname, 'app', 'backend');
  const frontendDir = path.join(__dirname, 'app', 'frontend');
  
  // Start backend
  console.log('Starting backend server...');
  const backendProcess = spawn('npm', ['run', 'dev'], {
    cwd: backendDir,
    stdio: 'pipe',
    shell: true,
    env: { ...process.env, NODE_ENV: 'development' }
  });
  
  backendProcess.stdout.on('data', (data) => {
    console.log(`[Backend] ${data}`);
  });
  
  backendProcess.stderr.on('data', (data) => {
    console.log(`[Backend Error] ${data}`);
  });
  
  // Wait for backend
  await new Promise(resolve => setTimeout(resolve, 10000));
  
  // Start frontend
  console.log('Starting frontend server...');
  const frontendProcess = spawn('npm', ['run', 'dev'], {
    cwd: frontendDir,
    stdio: 'pipe',
    shell: true,
    env: { ...process.env, NODE_ENV: 'development' }
  });
  
  frontendProcess.stdout.on('data', (data) => {
    console.log(`[Frontend] ${data}`);
  });
  
  frontendProcess.stderr.on('data', (data) => {
    console.log(`[Frontend Error] ${data}`);
  });
  
  // Wait for frontend
  await new Promise(resolve => setTimeout(resolve, 15000));
  
  // Test services
  console.log('\n🧪 Testing services...');
  
  const http = require('http');
  
  const backendTest = await new Promise((resolve) => {
    const req = http.request({
      hostname: 'localhost',
      port: 3001,
      path: '/api/health',
      method: 'GET',
      timeout: 5000
    }, (res) => {
      console.log(`✅ Backend running! Status: ${res.statusCode}`);
      resolve(true);
    });
    
    req.on('error', (error) => {
      console.log(`❌ Backend not responding: ${error.message}`);
      resolve(false);
    });
    
    req.end();
  });
  
  const frontendTest = await new Promise((resolve) => {
    const req = http.request({
      hostname: 'localhost',
      port: 3000,
      path: '/',
      method: 'GET',
      timeout: 5000
    }, (res) => {
      console.log(`✅ Frontend running! Status: ${res.statusCode}`);
      resolve(true);
    });
    
    req.on('error', (error) => {
      console.log(`❌ Frontend not responding: ${error.message}`);
      resolve(false);
    });
    
    req.end();
  });
  
  if (backendTest && frontendTest) {
    console.log('\n🎉 === SERVICES RUNNING SUCCESSFULLY ===');
    console.log('Frontend: http://localhost:3000');
    console.log('Backend: http://localhost:3001');
    
    // Run comprehensive test
    await runComprehensiveTest();
  }
  
  return { backendProcess, frontendProcess, backend: backendTest, frontend: frontendTest };
}

async function runComprehensiveTest() {
  console.log('\n🧪 === COMPREHENSIVE AUTHENTICATION TEST ===');
  
  try {
    const { chromium } = require('playwright');
    
    const browser = await chromium.launch({ headless: false });
    const context = await browser.newContext();
    const page = await context.newPage();
    
    // Test login
    console.log('1. Testing login...');
    await page.goto('http://localhost:3000/login', { waitUntil: 'domcontentloaded', timeout: 30000 });
    await page.waitForTimeout(3000);
    
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'itsMike818!');
    await page.click('button[type="submit"]');
    
    await page.waitForTimeout(5000);
    
    // Take screenshot
    await page.screenshot({ path: 'comprehensive-test.png' });
    console.log('📸 Screenshot saved: comprehensive-test.png');
    
    const currentUrl = page.url();
    console.log(`Current URL: ${currentUrl}`);
    
    if (!currentUrl.includes('/login')) {
      console.log('✅ Login successful!');
      
      // Test navigation
      try {
        await page.click('a[href="/quotes"]');
        await page.waitForTimeout(3000);
        await page.screenshot({ path: 'quotes-navigation.png' });
        console.log('📸 Screenshot saved: quotes-navigation.png');
        console.log('✅ Quotes navigation successful!');
      } catch (error) {
        console.log('⚠️ Quotes navigation failed:', error.message);
      }
      
      console.log('🎉 COMPREHENSIVE TEST COMPLETED SUCCESSFULLY!');
    } else {
      console.log('❌ Login failed - still on login page');
    }
    
    await browser.close();
    
  } catch (error) {
    console.error('❌ Comprehensive test failed:', error.message);
  }
}

async function createRestoreScript() {
  const restoreScript = `
// Restore original workspace configuration
const fs = require('fs');
const path = require('path');

async function restore() {
  console.log('🔄 Restoring original workspace configuration...');
  
  const appDir = path.join(__dirname, 'app');
  const backendDir = path.join(appDir, 'backend');
  const frontendDir = path.join(appDir, 'frontend');
  
  // Restore app package.json
  const appBackup = path.join(appDir, 'package-workspace-backup.json');
  const appPackage = path.join(appDir, 'package.json');
  
  if (fs.existsSync(appBackup)) {
    fs.copyFileSync(appBackup, appPackage);
    fs.unlinkSync(appBackup);
    console.log('✅ Restored app package.json');
  }
  
  // Restore backend package.json
  const backendBackup = path.join(backendDir, 'package-original.json');
  const backendPackage = path.join(backendDir, 'package.json');
  
  if (fs.existsSync(backendBackup)) {
    fs.copyFileSync(backendBackup, backendPackage);
    fs.unlinkSync(backendBackup);
    console.log('✅ Restored backend package.json');
  }
  
  // Restore frontend package.json
  const frontendBackup = path.join(frontendDir, 'package-original.json');
  const frontendPackage = path.join(frontendDir, 'package.json');
  
  if (fs.existsSync(frontendBackup)) {
    fs.copyFileSync(frontendBackup, frontendPackage);
    fs.unlinkSync(frontendBackup);
    console.log('✅ Restored frontend package.json');
  }
  
  console.log('🎉 Workspace configuration restored!');
}

restore().catch(console.error);
`;
  
  fs.writeFileSync(path.join(__dirname, 'restore-workspace.js'), restoreScript);
  console.log('✅ Created restore script');
}

async function main() {
  try {
    console.log('🚀 === COMPLETE WORKSPACE BYPASS SOLUTION ===');
    
    // Step 1: Disable workspace configuration
    const workspaceDisabled = await disableWorkspaceConfiguration();
    if (!workspaceDisabled) {
      console.log('❌ Failed to disable workspace configuration');
      return;
    }
    
    // Step 2: Create standalone services
    const servicesCreated = await createStandaloneServices();
    if (!servicesCreated) {
      console.log('❌ Failed to create standalone services');
      return;
    }
    
    // Step 3: Install dependencies
    const depsInstalled = await installDependencies();
    if (!depsInstalled) {
      console.log('❌ Failed to install dependencies');
      return;
    }
    
    console.log('\n✅ All dependencies installed successfully!');
    
    // Step 4: Start services and test
    const results = await startServicesAndTest();
    
    if (results.backend && results.frontend) {
      console.log('\n🎉 === COMPLETE SUCCESS ===');
      console.log('Application is fully functional!');
      console.log('Frontend: http://localhost:3000');
      console.log('Backend: http://localhost:3001');
      console.log('Login: <EMAIL> / itsMike818!');
    }
    
    // Step 5: Create restore script
    await createRestoreScript();
    
  } catch (error) {
    console.error('Main execution error:', error);
  } finally {
    console.log('\nServices will continue running.');
    console.log('To restore workspace: node restore-workspace.js');
  }
}

main();
