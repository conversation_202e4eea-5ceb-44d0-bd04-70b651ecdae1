import { Router } from 'express';
import { z } from 'zod';
import { prisma } from '../database/prisma';
import { authenticate, AuthRequest } from '../middleware/auth';
import { Request, Response } from 'express';
import { lookupMaterialByBarcode } from '../database/seed-materials';
import { getRedis } from '../services/redis';
import { logger } from '../utils/logger';
import multer from 'multer';
import csv from 'csv-parser';
import fs from 'fs';

const redis = getRedis();
import path from 'path';
import QRCode from 'qrcode';

const router: Router = Router();
const upload = multer({ dest: 'uploads/' });

// Apply authentication to all routes
router.use(authenticate);

// Lookup material by barcode
router.get('/lookup/:barcode', async (req: AuthRequest, res, next) => {
  try {
    const { barcode } = req.params;
    
    // Check cache first
    const cacheKey = `barcode:${barcode}`;
    const redis = getRedis();
    const cached = redis ? await redis.get(cacheKey) : null;
    if (cached) {
      return res.json(JSON.parse(cached));
    }

    // Look up material
    const material = await lookupMaterialByBarcode(barcode);
    
    if (!material) {
      return res.status(404).json({ error: 'Material not found' });
    }

    // Get current price
    const latestPrice = await prisma.materialPriceHistory.findFirst({
      where: { catalog_number: material.sku },
      orderBy: { effective_date: 'desc' }
    });

    // Check for alternatives
    const alternatives = await prisma.material.findMany({
      where: {
        category: material.category,
        id: { not: material.id },
        voltage_rating: material.voltage_rating,
        amperage_rating: material.amperage_rating
      },
      take: 5
    });

    // Check for bundle items (e.g., wire often comes with conduit)
    let bundleItems = [];
    if (material.category === 'WIRE') {
      const conduits = await prisma.material.findMany({
        where: {
          category: 'CONDUIT',
          conduit_size: getConduitSizeForWire(material.wire_size)
        },
        take: 3
      });
      bundleItems = conduits.map(c => ({
        materialId: c.id,
        name: c.name,
        quantity: 1,
        unit: c.unit
      }));
    }

    const response = {
      ...material,
      price: latestPrice?.unit_cost || material.current_price,
      alternatives: alternatives.map(alt => ({
        id: alt.id,
        sku: alt.sku,
        name: alt.name,
        manufacturer: alt.manufacturer,
        price: alt.current_price,
        availability: 'in-stock',
        reason: 'Similar specifications'
      })),
      bundleItems
    };

    // Cache for 1 hour
    try {
      if (redis) {
        await redis.setex(cacheKey, 3600, JSON.stringify(response));
      }
    } catch (cacheError) {
      logger.warn({
        message: 'Failed to cache barcode scan result',
        barcode,
        error: cacheError instanceof Error ? cacheError.message : 'Unknown error'
      });
      // Continue without caching
    }

    res.json(response);
  } catch (error) {
    next(error);
  }
});

// Save scan session
router.post('/sessions', async (req: AuthRequest, res, next) => {
  try {
    const sessionSchema = z.object({
      id: z.string(),
      startTime: z.string().transform(str => new Date(str)),
      endTime: z.string().transform(str => new Date(str)).optional(),
      scansCount: z.number(),
      mode: z.enum(['single', 'batch', 'continuous']),
      scans: z.array(z.object({
        id: z.string(),
        barcode: z.string(),
        type: z.string(),
        timestamp: z.string().transform(str => new Date(str)),
        materialId: z.string().optional(),
        quantity: z.number(),
        notes: z.string().optional()
      }))
    });

    const session = sessionSchema.parse(req.body);
    
    // Save session data
    try {
      if (redis) {
        await redis.setex(
          `scan_session:${session.id}`,
          86400 * 7, // Keep for 7 days
          JSON.stringify(session)
        );
      }
    } catch (redisError) {
      logger.warn({
        message: 'Failed to save scan session to Redis',
        sessionId: session.id,
        error: redisError instanceof Error ? redisError.message : 'Unknown error'
      });
      // Continue without Redis caching
    }

    // Update analytics
    try {
      await updateScanAnalytics(session.scans.map(scan => ({
        materialId: scan.materialId,
        timestamp: scan.timestamp
      })));
    } catch (analyticsError) {
      logger.error({
        message: 'Failed to update scan analytics',
        sessionId: session.id,
        error: analyticsError instanceof Error ? analyticsError.message : 'Unknown error'
      });
      // Don't fail the request due to analytics error
    }

    res.json({ success: true, sessionId: session.id });
  } catch (error) {
    next(error);
  }
});

// Inventory transactions
router.post('/inventory/transactions/bulk', async (req: AuthRequest, res, next) => {
  try {
    const transactionSchema = z.object({
      transactions: z.array(z.object({
        id: z.string(),
        type: z.enum(['add', 'remove', 'transfer', 'adjust']),
        materialId: z.string(),
        quantity: z.number(),
        fromLocation: z.string().optional(),
        toLocation: z.string().optional(),
        reason: z.string().optional(),
        performedBy: z.string(),
        timestamp: z.string().transform(str => new Date(str)),
        scanSessionId: z.string().optional()
      }))
    });

    const { transactions } = transactionSchema.parse(req.body);
    
    // Process each transaction
    // In a real system, this would update actual inventory tables
    for (const transaction of transactions) {
      await redis.lpush(
        `inventory:${transaction.toLocation || transaction.fromLocation}`,
        JSON.stringify(transaction)
      );
    }

    res.json({ success: true, processed: transactions.length });
  } catch (error) {
    next(error);
  }
});

// Get scan analytics
router.get('/analytics/scans', async (req: AuthRequest, res, next) => {
  try {
    const { startDate, endDate } = req.query;
    
    // Get scan data from Redis
    const scanKeys = await redis.keys('scan_session:*');
    const sessions = await Promise.all(
      scanKeys.map(key => redis.get(key))
    );

    const allScans = sessions
      .filter(Boolean)
      .map(s => JSON.parse(s!))
      .flatMap(session => session.scans);

    // Calculate analytics
    const analytics = {
      totalScans: allScans.length,
      uniqueMaterials: new Set(allScans.map(s => s.materialId).filter(Boolean)).size,
      topScannedMaterials: await getTopScannedMaterials(allScans),
      scansByCategory: await getScansByCategory(allScans),
      scansByLocation: {}, // Would need location tracking
      scansByUser: {}, // Would need user tracking
      timeRange: { start: startDate, end: endDate }
    };

    res.json(analytics);
  } catch (error) {
    next(error);
  }
});

// Generate custom QR code
router.post('/qr-codes/custom', async (req: AuthRequest, res, next) => {
  try {
    const qrSchema = z.object({
      type: z.enum(['material', 'job', 'location', 'bundle', 'custom']),
      label: z.string(),
      data: z.record(z.unknown()),
      projectId: z.string().optional()
    });

    const qrData = qrSchema.parse(req.body);
    
    // Generate QR code
    const qrCodeData = {
      type: qrData.type,
      version: '1.0',
      data: qrData.data,
      created: new Date(),
      createdBy: req.user!.userId
    };

    const qrCodeString = await QRCode.toDataURL(JSON.stringify(qrCodeData), {
      width: 300,
      margin: 2
    });

    // Save QR code info
    const qrId = `qr_${Date.now()}`;
    await redis.setex(
      `custom_qr:${qrId}`,
      86400 * 30, // Keep for 30 days
      JSON.stringify({ ...qrData, qrCodeString })
    );

    res.json({
      id: qrId,
      qrCodeData: qrCodeString,
      ...qrData
    });
  } catch (error) {
    next(error);
  }
});

// OCR text extraction
router.post('/ocr/extract', upload.single('image'), async (req: AuthRequest, res, next) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No image provided' });
    }

    // In a real implementation, this would use an OCR service like Tesseract or Google Vision
    // For now, return mock data
    const mockOCRResult = {
      text: 'THHN-12-BLK\nSouthwire 12 AWG\nBlack Wire 500ft\nUPC: 737576229559',
      confidence: 0.95,
      blocks: [
        {
          text: 'THHN-12-BLK',
          confidence: 0.98,
          boundingBox: { x: 10, y: 10, width: 200, height: 50 }
        },
        {
          text: 'Southwire 12 AWG',
          confidence: 0.94,
          boundingBox: { x: 10, y: 70, width: 250, height: 40 }
        }
      ]
    };

    // Clean up uploaded file
    fs.unlinkSync(req.file.path);

    res.json(mockOCRResult);
  } catch (error) {
    next(error);
  }
});

// Bulk import from CSV
router.post('/materials/import/csv', upload.single('file'), async (req: AuthRequest, res, next) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file provided' });
    }

    const jobId = `import_${Date.now()}`;
    const results: Array<{
      barcode: string;
      materialId: string;
      quantity: number;
      location: string;
    }> = [];
    const errors: Array<{
      line: number;
      barcode: string;
      error: string;
    }> = [];
    let lineNumber = 0;

    // Process CSV file
    fs.createReadStream(req.file.path)
      .pipe(csv())
      .on('data', async (row) => {
        lineNumber++;
        try {
          // Validate and process row
          if (row.barcode && row.quantity) {
            const material = await lookupMaterialByBarcode(row.barcode);
            if (material) {
              results.push({
                barcode: row.barcode,
                materialId: material.id,
                quantity: parseFloat(row.quantity),
                location: row.location || 'default'
              });
            } else {
              errors.push({
                line: lineNumber,
                barcode: row.barcode,
                error: 'Material not found'
              });
            }
          }
        } catch (error) {
          errors.push({
            line: lineNumber,
            barcode: row.barcode,
            error: error.message
          });
        }
      })
      .on('end', () => {
        // Clean up file
        fs.unlinkSync(req.file!.path);

        // Return import job result
        res.json({
          id: jobId,
          fileName: req.file!.originalname,
          totalItems: lineNumber,
          processedItems: results.length,
          successCount: results.length,
          errorCount: errors.length,
          errors: errors,
          status: 'completed',
          startTime: new Date(),
          endTime: new Date()
        });
      })
      .on('error', (error) => {
        fs.unlinkSync(req.file!.path);
        next(error);
      });
  } catch (error) {
    if (req.file) {
      fs.unlinkSync(req.file.path);
    }
    next(error);
  }
});

// Download offline catalog
router.get('/catalog/download', async (req: AuthRequest, res, next) => {
  try {
    // Get all materials with their latest prices
    const materials = await prisma.material.findMany({
      where: { in_stock: true }
    });

    const catalog: Record<string, typeof materials[0]> = {};

    for (const material of materials) {
      // Add by SKU
      catalog[material.sku] = material;
      
      // Also add by common barcodes if available
      // In a real system, you'd have a barcode mapping table
    }

    res.json(catalog);
  } catch (error) {
    next(error);
  }
});

// Helper functions

function getConduitSizeForWire(wireSize: string): string {
  // Simple mapping - in reality this would be more complex based on fill calculations
  const sizeMap: Record<string, string> = {
    '14 AWG': '1/2',
    '12 AWG': '1/2',
    '10 AWG': '3/4',
    '8 AWG': '3/4',
    '6 AWG': '1',
    '4 AWG': '1',
    '2 AWG': '1-1/4',
    '1 AWG': '1-1/2',
    '1/0 AWG': '1-1/2',
    '2/0 AWG': '2',
    '3/0 AWG': '2',
    '4/0 AWG': '2-1/2'
  };
  return sizeMap[wireSize] || '1/2';
}

async function updateScanAnalytics(scans: Array<{ materialId?: string; timestamp: Date }>) {
  if (!redis) return; // Skip if Redis is not available
  
  const date = new Date().toISOString().split('T')[0];
  
  for (const scan of scans) {
    if (scan.materialId) {
      try {
        await redis.hincrby(`scan_count:${date}`, scan.materialId, 1);
        await redis.hincrby('scan_count:total', scan.materialId, 1);
      } catch (error) {
        logger.warn({
          message: 'Failed to update scan analytics in Redis',
          materialId: scan.materialId,
          date,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        // Continue with other scans
      }
    }
  }
}

async function getTopScannedMaterials(scans: Array<{ materialId?: string; timestamp: Date }>) {
  const counts = new Map<string, number>();
  
  scans.forEach(scan => {
    if (scan.materialId) {
      counts.set(scan.materialId, (counts.get(scan.materialId) || 0) + 1);
    }
  });

  const sorted = Array.from(counts.entries())
    .sort((a, b) => b[1] - a[1])
    .slice(0, 10);

  // Get material details
  const materialIds = sorted.map(([id]) => id);
  const materials = await prisma.material.findMany({
    where: { id: { in: materialIds } }
  });

  const materialMap = new Map(materials.map(m => [m.id, m]));

  return sorted.map(([id, count]) => {
    const material = materialMap.get(id);
    return {
      materialId: id,
      name: material?.name || 'Unknown',
      sku: material?.sku || '',
      scanCount: count,
      lastScanned: new Date() // Would track this properly
    };
  });
}

async function getScansByCategory(scans: Array<{ materialId?: string; timestamp: Date }>) {
  const materialIds = scans.map(s => s.materialId).filter(Boolean);
  const materials = await prisma.material.findMany({
    where: { id: { in: materialIds } },
    select: { id: true, category: true }
  });

  const materialCategoryMap = new Map(materials.map(m => [m.id, m.category]));
  const categoryCounts: Record<string, number> = {};

  scans.forEach(scan => {
    if (scan.materialId) {
      const category = materialCategoryMap.get(scan.materialId) || 'UNKNOWN';
      categoryCounts[category] = (categoryCounts[category] || 0) + 1;
    }
  });

  return categoryCounts;
}

export { router as barcodeRouter };