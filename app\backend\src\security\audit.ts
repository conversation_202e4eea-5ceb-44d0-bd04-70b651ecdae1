import { prisma } from '../database/prisma';
import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';

export interface AuditLogEntry {
  action: string;
  userId?: string;
  resourceType: string;
  resourceId?: string;
  details?: Record<string, unknown>;
  ipAddress?: string;
  userAgent?: string;
  requestId?: string;
  timestamp?: Date;
}

// Audit action types
export const AUDIT_ACTIONS = {
  // Authentication
  LOGIN_SUCCESS: 'auth.login.success',
  LOGIN_FAILED: 'auth.login.failed',
  LOGOUT: 'auth.logout',
  TOKEN_REFRESH: 'auth.token.refresh',
  PASSWORD_CHANGE: 'auth.password.change',
  PASSWORD_RESET: 'auth.password.reset',
  
  // API Keys
  API_KEY_CREATED: 'api_key.created',
  API_KEY_REVOKED: 'api_key.revoked',
  API_KEY_USED: 'api_key.used',
  
  // Data Access
  DATA_EXPORT: 'data.export',
  DATA_DELETE: 'data.delete',
  SENSITIVE_DATA_ACCESS: 'data.sensitive.access',
  
  // Projects
  PROJECT_CREATE: 'project.create',
  PROJECT_UPDATE: 'project.update',
  PROJECT_DELETE: 'project.delete',
  PROJECT_ARCHIVE: 'project.archive',
  
  // Calculations
  CALCULATION_PERFORM: 'calculation.perform',
  CALCULATION_EXPORT: 'calculation.export',
  
  // Permits
  PERMIT_SUBMIT: 'permit.submit',
  PERMIT_APPROVE: 'permit.approve',
  PERMIT_REJECT: 'permit.reject',
  
  // Inspections
  INSPECTION_CREATE: 'inspection.create',
  INSPECTION_COMPLETE: 'inspection.complete',
  INSPECTION_FAIL: 'inspection.fail',
  
  // Security
  PERMISSION_GRANT: 'security.permission.grant',
  PERMISSION_REVOKE: 'security.permission.revoke',
  SUSPICIOUS_ACTIVITY: 'security.suspicious',
  RATE_LIMIT_EXCEEDED: 'security.rate_limit',
  
  // System
  CONFIG_CHANGE: 'system.config.change',
  BACKUP_CREATE: 'system.backup.create',
  BACKUP_RESTORE: 'system.backup.restore'
} as const;

export type AuditAction = typeof AUDIT_ACTIONS[keyof typeof AUDIT_ACTIONS];

// Create audit log entry
export async function createAuditLog(entry: AuditLogEntry): Promise<void> {
  try {
    await prisma.$executeRaw`
      INSERT INTO audit_logs (
        id, action, user_id, resource_type, resource_id, 
        details, ip_address, user_agent, request_id, created_at
      )
      VALUES (
        ${crypto.randomUUID()}, ${entry.action}, ${entry.userId}, 
        ${entry.resourceType}, ${entry.resourceId}, ${JSON.stringify(entry.details)},
        ${entry.ipAddress}, ${entry.userAgent}, ${entry.requestId}, ${entry.timestamp || new Date()}
      )
    `;
    
    // Also log to Winston for real-time monitoring
    logger.info('Audit log', {
      ...entry,
      timestamp: entry.timestamp || new Date()
    });
  } catch (error) {
    logger.error('Failed to create audit log', { error, entry });
  }
}

// Middleware to automatically log certain actions
interface AuthenticatedRequest extends Request {
  user?: {
    userId: string;
    [key: string]: unknown;
  };
  id?: string;
}

export function auditMiddleware(action: AuditAction, resourceType: string) {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    const originalSend = res.send;
    
    res.send = function(data: unknown) {
      // Log successful responses
      if (res.statusCode >= 200 && res.statusCode < 300) {
        const resourceId = req.params.id || (data && data.id);
        
        createAuditLog({
          action,
          userId: req.user?.userId,
          resourceType,
          resourceId,
          details: {
            method: req.method,
            path: req.path,
            query: req.query,
            body: sanitizeBody(req.body)
          },
          ipAddress: getClientIp(req),
          userAgent: req.headers['user-agent'],
          requestId: req.id
        });
      }
      
      return originalSend.call(this, data);
    };
    
    next();
  };
}

// Get client IP address
function getClientIp(req: Request): string {
  const forwarded = req.headers['x-forwarded-for'];
  if (forwarded) {
    return (forwarded as string).split(',')[0].trim();
  }
  return req.socket.remoteAddress || 'unknown';
}

// Sanitize request body for logging
function sanitizeBody(body: unknown): unknown {
  if (!body) return null;
  
  const sanitized = { ...body };
  const sensitiveFields = [
    'password', 'password_hash', 'ssn', 'social_security_number',
    'credit_card', 'bank_account', 'api_key', 'refresh_token',
    'cvv', 'pin', 'secret'
  ];
  
  sensitiveFields.forEach(field => {
    if (sanitized[field]) {
      sanitized[field] = '[REDACTED]';
    }
  });
  
  return sanitized;
}

// Query audit logs
export async function queryAuditLogs(filters: {
  userId?: string;
  action?: string;
  resourceType?: string;
  resourceId?: string;
  startDate?: Date;
  endDate?: Date;
  limit?: number;
  offset?: number;
}): Promise<any[]> {
  let query = 'SELECT * FROM audit_logs WHERE 1=1';
  const params: any[] = [];
  
  if (filters.userId) {
    query += ' AND user_id = ?';
    params.push(filters.userId);
  }
  
  if (filters.action) {
    query += ' AND action = ?';
    params.push(filters.action);
  }
  
  if (filters.resourceType) {
    query += ' AND resource_type = ?';
    params.push(filters.resourceType);
  }
  
  if (filters.resourceId) {
    query += ' AND resource_id = ?';
    params.push(filters.resourceId);
  }
  
  if (filters.startDate) {
    query += ' AND created_at >= ?';
    params.push(filters.startDate);
  }
  
  if (filters.endDate) {
    query += ' AND created_at <= ?';
    params.push(filters.endDate);
  }
  
  query += ' ORDER BY created_at DESC';
  
  if (filters.limit) {
    query += ' LIMIT ?';
    params.push(filters.limit);
  }
  
  if (filters.offset) {
    query += ' OFFSET ?';
    params.push(filters.offset);
  }
  
  return prisma.$queryRawUnsafe(query, ...params);
}

// Security event monitoring
export async function detectSuspiciousActivity(userId: string): Promise<boolean> {
  const recentLogs = await prisma.$queryRaw<any[]>`
    SELECT action, COUNT(*) as count
    FROM audit_logs
    WHERE user_id = ${userId}
      AND created_at > datetime('now', '-15 minutes')
    GROUP BY action
  `;
  
  // Check for suspicious patterns
  const suspiciousPatterns = [
    { action: AUDIT_ACTIONS.LOGIN_FAILED, threshold: 5 },
    { action: AUDIT_ACTIONS.DATA_EXPORT, threshold: 10 },
    { action: AUDIT_ACTIONS.SENSITIVE_DATA_ACCESS, threshold: 20 }
  ];
  
  for (const pattern of suspiciousPatterns) {
    const log = recentLogs.find(l => l.action === pattern.action);
    if (log && log.count >= pattern.threshold) {
      await createAuditLog({
        action: AUDIT_ACTIONS.SUSPICIOUS_ACTIVITY,
        userId,
        resourceType: 'user',
        resourceId: userId,
        details: {
          pattern: pattern.action,
          count: log.count,
          threshold: pattern.threshold
        }
      });
      return true;
    }
  }
  
  return false;
}