import BackgroundFetch from 'react-native-background-fetch';
import { SyncEngine } from './SyncEngine';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';

export class BackgroundSync {
  private static instance: BackgroundSync;
  private syncEngine: SyncEngine;
  private isConfigured: boolean = false;

  private constructor() {
    this.syncEngine = SyncEngine.getInstance();
  }

  static getInstance(): BackgroundSync {
    if (!BackgroundSync.instance) {
      BackgroundSync.instance = new BackgroundSync();
    }
    return BackgroundSync.instance;
  }

  async configure() {
    if (this.isConfigured) {
      return;
    }

    const settings = await this.getSyncSettings();

    await BackgroundFetch.configure({
      minimumFetchInterval: settings.syncInterval || 15, // minutes
      stopOnTerminate: false,
      startOnBoot: true,
      enableHeadless: true,
      requiresBatteryNotLow: settings.batteryOptimized,
      requiresCharging: false,
      requiresDeviceIdle: false,
      requiresStorageNotLow: true,
      requiredNetworkType: settings.wifiOnly 
        ? BackgroundFetch.NETWORK_TYPE_UNMETERED 
        : BackgroundFetch.NETWORK_TYPE_ANY,
    }, async (taskId) => {
      try {
        const result = await this.performBackgroundSync();
        BackgroundFetch.finish(taskId);
      } catch (error) {
        console.error('[BackgroundSync] Sync failed:', error);
        BackgroundFetch.finish(taskId);
      }
    }, (taskId) => {
      console.log('[BackgroundSync] Task timeout:', taskId);
      BackgroundFetch.finish(taskId);
    });

    // Android-specific headless task
    if (Platform.OS === 'android') {
      BackgroundFetch.registerHeadlessTask(async (taskId) => {
        console.log('[BackgroundSync] Headless task:', taskId);
        
        try {
          await this.performBackgroundSync();
        } catch (error) {
          console.error('[BackgroundSync] Headless sync failed:', error);
        }
        
        BackgroundFetch.finish(taskId);
      });
    }

    await BackgroundFetch.start();
    this.isConfigured = true;
  }

  async performBackgroundSync() {
    const settings = await this.getSyncSettings();
    
    if (!settings.enabled) {
      return { skipped: true, reason: 'Background sync disabled' };
    }

    const lastSync = await this.getLastSyncTime();
    const now = Date.now();
    const timeSinceLastSync = now - lastSync;
    
    // Skip if synced recently
    if (timeSinceLastSync < settings.minSyncInterval * 60 * 1000) {
      return { 
        skipped: true, 
        reason: 'Synced recently',
        nextSync: lastSync + (settings.minSyncInterval * 60 * 1000),
      };
    }

    const result = await this.syncEngine.sync({
      automatic: true,
      wifiOnly: settings.wifiOnly,
      conflictStrategy: 'last-write-wins',
      maxRetries: 3,
    });

    await this.setLastSyncTime(now);
    await this.updateSyncStats(result);

    return result;
  }

  async scheduleSync(delayMinutes: number = 0) {
    if (delayMinutes > 0) {
      await BackgroundFetch.scheduleTask({
        taskId: 'com.electricalcontractor.sync.delayed',
        delay: delayMinutes * 60 * 1000, // Convert to milliseconds
        periodic: false,
        stopOnTerminate: false,
        enableHeadless: true,
      });
    } else {
      // Trigger immediate sync
      BackgroundFetch.start();
    }
  }

  async stop() {
    await BackgroundFetch.stop();
  }

  async getStatus(): Promise<number> {
    return BackgroundFetch.status();
  }

  private async getSyncSettings(): Promise<{
    enabled: boolean;
    syncInterval: number;
    minSyncInterval: number;
    wifiOnly: boolean;
    batteryOptimized: boolean;
  }> {
    try {
      const settings = await AsyncStorage.getItem('sync_settings');
      return settings ? JSON.parse(settings) : this.getDefaultSettings();
    } catch {
      return this.getDefaultSettings();
    }
  }

  private getDefaultSettings() {
    return {
      enabled: true,
      syncInterval: 15, // minutes
      minSyncInterval: 5, // minutes
      wifiOnly: false,
      batteryOptimized: true,
    };
  }

  async updateSyncSettings(settings: Partial<{
    enabled: boolean;
    syncInterval: number;
    wifiOnly: boolean;
    batteryOptimized: boolean;
  }>) {
    const current = await this.getSyncSettings();
    const updated = { ...current, ...settings };
    
    await AsyncStorage.setItem('sync_settings', JSON.stringify(updated));
    
    // Reconfigure if settings changed
    if (settings.enabled !== undefined || 
        settings.syncInterval !== undefined ||
        settings.wifiOnly !== undefined) {
      this.isConfigured = false;
      await this.configure();
    }
  }

  private async getLastSyncTime(): Promise<number> {
    try {
      const time = await AsyncStorage.getItem('last_sync_time');
      return time ? parseInt(time) : 0;
    } catch {
      return 0;
    }
  }

  private async setLastSyncTime(time: number) {
    await AsyncStorage.setItem('last_sync_time', time.toString());
  }

  private async updateSyncStats(result: any) {
    try {
      const stats = await AsyncStorage.getItem('sync_stats');
      const currentStats = stats ? JSON.parse(stats) : {
        totalSyncs: 0,
        successfulSyncs: 0,
        failedSyncs: 0,
        totalItemsSynced: 0,
        totalConflicts: 0,
      };

      currentStats.totalSyncs++;
      
      if (result.success) {
        currentStats.successfulSyncs++;
        currentStats.totalItemsSynced += result.syncedItems || 0;
        currentStats.totalConflicts += result.conflicts?.length || 0;
      } else {
        currentStats.failedSyncs++;
      }

      currentStats.lastSync = {
        time: Date.now(),
        result: result,
      };

      await AsyncStorage.setItem('sync_stats', JSON.stringify(currentStats));
    } catch (error) {
      console.error('[BackgroundSync] Failed to update stats:', error);
    }
  }

  async getSyncStats() {
    try {
      const stats = await AsyncStorage.getItem('sync_stats');
      return stats ? JSON.parse(stats) : null;
    } catch {
      return null;
    }
  }
}