import { Router } from 'express';
import { z } from 'zod';
import { authenticate, AuthRequest } from '../middleware/auth';
import { NaturalLanguageCalculationService } from '../services/ai/natural-language-calculation.service';
import { CalculationValidatorService } from '../services/ai/calculation-validator.service';
import { CostPredictionService } from '../services/ai/cost-prediction.service';
import { AppError } from '../utils/errors';

const router = Router();

// Natural language calculation schema
const nlCalculationSchema = z.object({
  query: z.string().min(5).max(500),
  context: z.object({
    project_id: z.string().optional(),
    panel_id: z.string().optional(),
    location: z.string().optional(),
    additional_info: z.record(z.any()).optional()
  }).optional()
});

// Validation schema
const validationSchema = z.object({
  calculation_type: z.string(),
  input: z.record(z.any()),
  output: z.record(z.any())
});

// Cost prediction schema
const costPredictionSchema = z.object({
  type: z.enum(['RESIDENTIAL', 'COMMERCIAL', 'INDUSTRIAL', 'SERVICE_UPGRADE']),
  square_footage: z.number().positive(),
  voltage_system: z.string(),
  service_size: z.number().positive(),
  location: z.string(),
  scope: z.string().optional(),
  project_id: z.string().optional()
});

// Material cost prediction schema
const materialCostSchema = z.object({
  materials: z.array(z.object({
    item: z.string(),
    quantity: z.number().positive()
  })),
  timeframe: z.number().min(1).max(365).default(30)
});

// Bid optimization schema
const bidOptimizationSchema = z.object({
  base_cost: z.number().positive(),
  project_details: z.record(z.any()),
  competitor_data: z.record(z.any()).optional()
});

/**
 * @route POST /api/ai/calculate
 * @desc Process natural language calculation request
 * @access Private
 */
router.post('/calculate', authenticate, async (req: AuthRequest, res, next) => {
  try {
    const data = nlCalculationSchema.parse(req.body);
    
    const nlService = new NaturalLanguageCalculationService();
    const result = await nlService.processCalculationRequest(
      data.query,
      data.context
    );
    
    // Log the calculation if successful
    if (result.success && result.result) {
      const { prisma } = await import('../database/prisma');
      await prisma.calculationLog.create({
        data: {
          calculation_type: result.calculationType,
          input_data: JSON.stringify({ query: data.query, context: data.context }),
          output_data: JSON.stringify(result.result),
          nec_references: JSON.stringify(result.necReferences),
          performed_by: req.user!.userId,
          project_id: data.context?.project_id
        }
      });
    }
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route POST /api/ai/validate
 * @desc Validate calculation results with AI
 * @access Private
 */
router.post('/validate', authenticate, async (req: AuthRequest, res, next) => {
  try {
    const data = validationSchema.parse(req.body);
    
    const validator = new CalculationValidatorService();
    const validationResult = await validator.validateCalculation(
      data.calculation_type,
      data.input,
      data.output
    );
    
    // Also check safety
    const safetyCheck = await validator.validateSafety(
      data.calculation_type,
      data.output
    );
    
    res.json({
      success: true,
      data: {
        validation: validationResult,
        safety: safetyCheck
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route POST /api/ai/validate-field
 * @desc Real-time field validation during data entry
 * @access Private
 */
router.post('/validate-field', authenticate, async (req: AuthRequest, res, next) => {
  try {
    const { calculation_type, field, value, context } = req.body;
    
    const validator = new CalculationValidatorService();
    const validation = await validator.validateInput(
      calculation_type,
      field,
      value,
      context
    );
    
    res.json({
      success: true,
      data: validation
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route POST /api/ai/predict-cost
 * @desc Predict project cost using AI
 * @access Private
 */
router.post('/predict-cost', authenticate, async (req: AuthRequest, res, next) => {
  try {
    const data = costPredictionSchema.parse(req.body);
    
    const predictor = new CostPredictionService();
    const prediction = await predictor.predictProjectCost(data);
    
    res.json({
      success: true,
      data: prediction
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route POST /api/ai/predict-material-costs
 * @desc Predict material cost trends
 * @access Private
 */
router.post('/predict-material-costs', authenticate, async (req: AuthRequest, res, next) => {
  try {
    const data = materialCostSchema.parse(req.body);
    
    const predictor = new CostPredictionService();
    const prediction = await predictor.predictMaterialCosts(
      data.materials,
      data.timeframe
    );
    
    res.json({
      success: true,
      data: prediction
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route POST /api/ai/optimize-bid
 * @desc Get AI-optimized bid pricing
 * @access Private
 */
router.post('/optimize-bid', authenticate, async (req: AuthRequest, res, next) => {
  try {
    const data = bidOptimizationSchema.parse(req.body);
    
    const predictor = new CostPredictionService();
    const optimization = await predictor.optimizeBidPrice(
      data.base_cost,
      data.project_details,
      data.competitor_data
    );
    
    res.json({
      success: true,
      data: optimization
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/ai/cost-overrun-analysis
 * @desc Analyze historical cost overrun patterns
 * @access Private (Admin only)
 */
router.get('/cost-overrun-analysis', authenticate, async (req: AuthRequest, res, next) => {
  try {
    // Check if user is admin
    if (req.user!.role !== 'admin') {
      throw new AppError('Unauthorized - Admin access required', 403);
    }
    
    const predictor = new CostPredictionService();
    const analysis = await predictor.analyzeCostOverruns();
    
    res.json({
      success: true,
      data: analysis
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route POST /api/ai/suggest-corrections
 * @desc Get AI suggestions for calculation errors
 * @access Private
 */
router.post('/suggest-corrections', authenticate, async (req: AuthRequest, res, next) => {
  try {
    const { error, context } = req.body;
    
    const validator = new CalculationValidatorService();
    const suggestions = await validator.suggestCorrections(error, context);
    
    res.json({
      success: true,
      data: suggestions
    });
  } catch (error) {
    next(error);
  }
});

// Health check for AI services
router.get('/health', authenticate, async (req: AuthRequest, res) => {
  const hasApiKey = !!process.env.GEMINI_API_KEY;
  
  res.json({
    success: true,
    data: {
      ai_services_available: hasApiKey,
      services: {
        natural_language_calculation: hasApiKey,
        validation: hasApiKey,
        cost_prediction: hasApiKey
      },
      message: hasApiKey 
        ? 'All AI services are operational' 
        : 'AI services require GEMINI_API_KEY environment variable'
    }
  });
});

export default router;