#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

// Try to find vite in various locations
const possiblePaths = [
  path.join(__dirname, 'node_modules', '.bin', 'vite'),
  path.join(__dirname, '..', 'node_modules', '.bin', 'vite'),
  path.join(__dirname, '..', '..', 'node_modules', '.bin', 'vite'),
  'vite' // fallback to global
];

function tryVite(viteCmd) {
  console.log(`Trying to start vite with: ${viteCmd}`);
  
  const vite = spawn(viteCmd, [], {
    stdio: 'inherit',
    shell: true,
    cwd: __dirname
  });

  vite.on('error', (err) => {
    console.error(`Failed to start with ${viteCmd}:`, err.message);
  });

  vite.on('exit', (code) => {
    if (code !== 0) {
      console.error(`Vite exited with code ${code}`);
    }
  });
}

// Try npx first
console.log('Attempting to start Vite development server...');
tryVite('npx vite');