import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate, Link } from 'react-router-dom';
import { Project } from '@electrical/shared';
import { api } from '../../services/api';
import {
  BuildingOfficeIcon,
  HomeIcon,
  CogIcon,
  BoltIcon,
  DocumentTextIcon,
  ClipboardDocumentListIcon,
  FireIcon,
  ExclamationTriangleIcon,
  DocumentCheckIcon,
  ArrowLeftIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';

export function ProjectDetailPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [project, setProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Redirect to form page if id is "new"
    if (id === 'new') {
      navigate('/projects/new', { replace: true });
      return;
    }
    
    if (id) {
      loadProject();
    }
  }, [id, navigate]);

  const loadProject = async () => {
    try {
      setLoading(true);
      const response = await api.get(`/projects/${id}`);
      setProject(response.data);
      setError(null);
    } catch (err: any) {
      console.error('Error loading project with ID:', id, err);
      const errorMessage = err.response?.data?.message || `Failed to load project${id ? ` (ID: ${id})` : ''}`;
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const getProjectIcon = (type: string) => {
    switch (type) {
      case 'RESIDENTIAL':
        return <HomeIcon className="h-8 w-8 text-blue-600" />;
      case 'COMMERCIAL':
        return <BuildingOfficeIcon className="h-8 w-8 text-green-600" />;
      case 'INDUSTRIAL':
        return <CogIcon className="h-8 w-8 text-purple-600" />;
      default:
        return <BoltIcon className="h-8 w-8 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PLANNING':
        return 'bg-gray-100 text-gray-800';
      case 'APPROVED':
        return 'bg-green-100 text-green-800';
      case 'IN_PROGRESS':
        return 'bg-blue-100 text-blue-800';
      case 'COMPLETED':
        return 'bg-purple-100 text-purple-800';
      case 'ON_HOLD':
        return 'bg-amber-100 text-amber-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error || !project) {
    return (
      <div className="rounded-lg bg-red-50 p-4 text-red-800">
        <p className="font-medium">Error loading project</p>
        <p className="text-sm mt-1">{error}</p>
      </div>
    );
  }

  const features = [
    {
      name: 'Panel Management',
      description: 'Manage electrical panels, circuits, and create panel schedules',
      icon: BoltIcon,
      href: `/projects/${id}/panels`,
      color: 'bg-blue-500'
    },
    {
      name: 'Arc Flash Analysis',
      description: 'Perform arc flash calculations and generate safety labels',
      icon: FireIcon,
      href: `/projects/${id}/arc-flash`,
      color: 'bg-red-500'
    },
    {
      name: 'Short Circuit Analysis',
      description: 'Calculate fault currents and verify equipment ratings',
      icon: ExclamationTriangleIcon,
      href: `/projects/${id}/short-circuit`,
      color: 'bg-yellow-500'
    },
    {
      name: 'Permit Documents',
      description: 'Generate and manage permit applications and documentation',
      icon: DocumentTextIcon,
      href: `/projects/${id}/permits`,
      color: 'bg-green-500'
    },
    {
      name: 'Inspections',
      description: 'Create inspection checklists and track inspection progress',
      icon: DocumentCheckIcon,
      href: `/projects/${id}/inspections`,
      color: 'bg-purple-500'
    },
    {
      name: 'Estimates',
      description: 'Create and manage project estimates and quotes',
      icon: ClipboardDocumentListIcon,
      href: `/projects/${id}/estimates`,
      color: 'bg-indigo-500'
    }
  ];

  return (
    <div>
      {/* Header */}
      <div className="mb-6">
        <button
          onClick={() => navigate('/projects')}
          className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-4"
        >
          <ArrowLeftIcon className="h-4 w-4 mr-1" />
          Back to Projects
        </button>
        
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              {getProjectIcon(project.type)}
              <div className="ml-4">
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {project.name}
                </h1>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {project.address}, {project.city}, {project.state} {project.zip_code}
                </p>
              </div>
            </div>
            <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(project.status)}`}>
              {project.status.replace('_', ' ')}
            </span>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-6">
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">Service Size</p>
              <p className="font-medium text-gray-900 dark:text-white">{project.service_size}A</p>
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">Voltage System</p>
              <p className="font-medium text-gray-900 dark:text-white">{project.voltage_system}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">Project Type</p>
              <p className="font-medium text-gray-900 dark:text-white">{project.type}</p>
            </div>
            {project.permit_number && (
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">Permit Number</p>
                <p className="font-medium text-gray-900 dark:text-white">{project.permit_number}</p>
              </div>
            )}
          </div>

          {project.notes && (
            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
              <p className="text-sm text-gray-500 dark:text-gray-400">Notes</p>
              <p className="mt-1 text-sm text-gray-900 dark:text-white">{project.notes}</p>
            </div>
          )}
        </div>
      </div>

      {/* Features Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {features.map((feature) => (
          <Link
            key={feature.name}
            to={feature.href}
            className="bg-white dark:bg-gray-800 rounded-lg shadow hover:shadow-lg transition-shadow p-6 border border-gray-200 dark:border-gray-700"
          >
            <div className="flex items-center">
              <div className={`${feature.color} rounded-lg p-3`}>
                <feature.icon className="h-6 w-6 text-white" />
              </div>
              <h3 className="ml-4 text-lg font-medium text-gray-900 dark:text-white">
                {feature.name}
              </h3>
            </div>
            <p className="mt-3 text-sm text-gray-500 dark:text-gray-400">
              {feature.description}
            </p>
          </Link>
        ))}
      </div>

      {/* Quick Stats */}
      <div className="mt-8 bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Project Statistics
        </h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-gray-900 dark:text-white">0</p>
            <p className="text-sm text-gray-500 dark:text-gray-400">Panels</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-gray-900 dark:text-white">0</p>
            <p className="text-sm text-gray-500 dark:text-gray-400">Circuits</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-gray-900 dark:text-white">0</p>
            <p className="text-sm text-gray-500 dark:text-gray-400">Inspections</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-gray-900 dark:text-white">0</p>
            <p className="text-sm text-gray-500 dark:text-gray-400">Documents</p>
          </div>
        </div>
      </div>
    </div>
  );
}