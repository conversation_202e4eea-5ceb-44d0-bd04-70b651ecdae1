// Comprehensive solution to fix pnpm workspace issues and start services
const fs = require('fs');
const path = require('path');
const { spawn, exec } = require('child_process');

async function runCommand(command, cwd, timeout = 30000) {
  return new Promise((resolve) => {
    console.log(`\n🔧 Running: ${command}`);
    console.log(`📁 Directory: ${cwd}`);
    
    const process = exec(command, { cwd, timeout }, (error, stdout, stderr) => {
      if (error) {
        console.error(`❌ Error: ${error.message}`);
        resolve({ success: false, error: error.message, stdout, stderr });
      } else {
        console.log(`✅ Success: ${stdout}`);
        if (stderr) console.warn(`⚠️ Warning: ${stderr}`);
        resolve({ success: true, stdout, stderr });
      }
    });
  });
}

async function fixBackendDependencies() {
  console.log('\n🔧 === FIXING BACKEND DEPENDENCIES ===');
  
  const backendDir = path.join(__dirname, 'app', 'backend');
  
  // Step 1: Remove problematic node_modules
  console.log('1. Removing existing node_modules...');
  await runCommand('rmdir /s /q node_modules 2>nul || echo "No node_modules found"', backendDir);
  
  // Step 2: Install dependencies directly with npm (bypass pnpm workspace)
  console.log('2. Installing dependencies with npm...');
  const installResult = await runCommand('npm install --no-package-lock --legacy-peer-deps', backendDir);
  
  if (!installResult.success) {
    console.log('3. Trying alternative installation...');
    // Try installing just essential dependencies
    await runCommand('npm install express tsx typescript @types/node --no-package-lock', backendDir);
  }
  
  return true;
}

async function fixFrontendDependencies() {
  console.log('\n🔧 === FIXING FRONTEND DEPENDENCIES ===');
  
  const frontendDir = path.join(__dirname, 'app', 'frontend');
  
  // Step 1: Remove problematic node_modules
  console.log('1. Removing existing node_modules...');
  await runCommand('rmdir /s /q node_modules 2>nul || echo "No node_modules found"', frontendDir);
  
  // Step 2: Install dependencies directly with npm
  console.log('2. Installing dependencies with npm...');
  const installResult = await runCommand('npm install --no-package-lock --legacy-peer-deps', frontendDir);
  
  if (!installResult.success) {
    console.log('3. Trying alternative installation...');
    // Try installing just essential dependencies
    await runCommand('npm install vite react react-dom typescript --no-package-lock', frontendDir);
  }
  
  return true;
}

async function startBackend() {
  console.log('\n🚀 === STARTING BACKEND SERVER ===');
  
  const backendDir = path.join(__dirname, 'app', 'backend');
  
  // Try multiple startup methods
  console.log('1. Trying npx tsx watch src/index.ts...');
  
  const backendProcess = spawn('npx', ['tsx', 'watch', 'src/index.ts'], {
    cwd: backendDir,
    stdio: 'pipe',
    shell: true,
    env: { 
      ...process.env, 
      NODE_ENV: 'development',
      PATH: process.env.PATH + ';' + path.join(backendDir, 'node_modules', '.bin')
    }
  });
  
  backendProcess.stdout.on('data', (data) => {
    console.log(`[Backend] ${data}`);
  });
  
  backendProcess.stderr.on('data', (data) => {
    console.log(`[Backend Error] ${data}`);
  });
  
  backendProcess.on('error', (error) => {
    console.error('Backend process error:', error);
  });
  
  console.log('✅ Backend process started');
  return backendProcess;
}

async function startFrontend() {
  console.log('\n🚀 === STARTING FRONTEND SERVER ===');
  
  const frontendDir = path.join(__dirname, 'app', 'frontend');
  
  console.log('1. Trying npm run dev...');
  
  const frontendProcess = spawn('npm', ['run', 'dev'], {
    cwd: frontendDir,
    stdio: 'pipe',
    shell: true,
    env: { 
      ...process.env, 
      NODE_ENV: 'development',
      PATH: process.env.PATH + ';' + path.join(frontendDir, 'node_modules', '.bin')
    }
  });
  
  frontendProcess.stdout.on('data', (data) => {
    console.log(`[Frontend] ${data}`);
  });
  
  frontendProcess.stderr.on('data', (data) => {
    console.log(`[Frontend Error] ${data}`);
  });
  
  frontendProcess.on('error', (error) => {
    console.error('Frontend process error:', error);
  });
  
  console.log('✅ Frontend process started');
  return frontendProcess;
}

async function testServices() {
  console.log('\n🧪 === TESTING SERVICES ===');
  
  const http = require('http');
  
  // Test backend
  const backendTest = await new Promise((resolve) => {
    console.log('Testing backend on port 3001...');
    
    const req = http.request({
      hostname: 'localhost',
      port: 3001,
      path: '/api/health',
      method: 'GET',
      timeout: 5000
    }, (res) => {
      console.log(`✅ Backend is running! Status: ${res.statusCode}`);
      resolve(true);
    });
    
    req.on('error', (error) => {
      console.log(`❌ Backend not responding: ${error.message}`);
      resolve(false);
    });
    
    req.on('timeout', () => {
      console.log('❌ Backend request timeout');
      req.destroy();
      resolve(false);
    });
    
    req.end();
  });
  
  // Test frontend
  const frontendTest = await new Promise((resolve) => {
    console.log('Testing frontend on port 3000...');
    
    const req = http.request({
      hostname: 'localhost',
      port: 3000,
      path: '/',
      method: 'GET',
      timeout: 5000
    }, (res) => {
      console.log(`✅ Frontend is running! Status: ${res.statusCode}`);
      resolve(true);
    });
    
    req.on('error', (error) => {
      console.log(`❌ Frontend not responding: ${error.message}`);
      resolve(false);
    });
    
    req.on('timeout', () => {
      console.log('❌ Frontend request timeout');
      req.destroy();
      resolve(false);
    });
    
    req.end();
  });
  
  return { backend: backendTest, frontend: frontendTest };
}

async function runComprehensiveTest() {
  console.log('\n🧪 === RUNNING COMPREHENSIVE AUTHENTICATION TEST ===');
  
  try {
    const { chromium } = require('playwright');
    
    const browser = await chromium.launch({ headless: false });
    const context = await browser.newContext();
    const page = await context.newPage();
    
    // Test 1: Navigate to application
    console.log('1. Navigating to application...');
    await page.goto('http://localhost:3000', { waitUntil: 'domcontentloaded', timeout: 30000 });
    
    // Wait for initialization
    await page.waitForTimeout(5000);
    
    // Take screenshot
    await page.screenshot({ path: 'app-loaded.png' });
    console.log('📸 Screenshot saved: app-loaded.png');
    
    // Test 2: Navigate to login
    console.log('2. Navigating to login page...');
    await page.goto('http://localhost:3000/login', { waitUntil: 'domcontentloaded' });
    await page.waitForTimeout(3000);
    
    // Test 3: Attempt login
    console.log('3. Attempting login with real credentials...');
    
    const emailInput = await page.locator('input[type="email"], input[name="email"]').first();
    const passwordInput = await page.locator('input[type="password"], input[name="password"]').first();
    const loginButton = await page.locator('button[type="submit"], button:has-text("Login"), button:has-text("Sign in")').first();
    
    if (await emailInput.isVisible()) {
      await emailInput.fill('<EMAIL>');
      console.log('✅ Email filled: <EMAIL>');
    }
    
    if (await passwordInput.isVisible()) {
      await passwordInput.fill('itsMike818!');
      console.log('✅ Password filled');
    }
    
    if (await loginButton.isVisible()) {
      await loginButton.click();
      console.log('✅ Login button clicked');
    }
    
    // Wait for response
    await page.waitForTimeout(5000);
    
    // Take screenshot after login
    await page.screenshot({ path: 'after-login.png' });
    console.log('📸 Screenshot saved: after-login.png');
    
    // Test 4: Check if logged in
    const currentUrl = page.url();
    console.log(`Current URL: ${currentUrl}`);
    
    if (!currentUrl.includes('/login')) {
      console.log('✅ Authentication successful - navigated away from login page');
      
      // Test 5: Navigate to quotes section
      console.log('4. Testing quotes navigation...');
      
      try {
        await page.click('a[href="/quotes"], button:has-text("Quotes")');
        await page.waitForTimeout(3000);
        
        // Take screenshot of quotes page
        await page.screenshot({ path: 'quotes-page.png' });
        console.log('📸 Screenshot saved: quotes-page.png');
        
        console.log('✅ Quotes navigation successful');
      } catch (error) {
        console.log('⚠️ Quotes navigation failed:', error.message);
      }
      
      console.log('🎉 COMPREHENSIVE TEST COMPLETED SUCCESSFULLY!');
    } else {
      console.log('❌ Authentication failed - still on login page');
    }
    
    await browser.close();
    return true;
    
  } catch (error) {
    console.error('❌ Comprehensive test failed:', error.message);
    return false;
  }
}

let backendProcess = null;
let frontendProcess = null;

async function cleanup() {
  console.log('\n🧹 Cleaning up processes...');
  
  if (backendProcess) {
    backendProcess.kill();
  }
  
  if (frontendProcess) {
    frontendProcess.kill();
  }
}

async function main() {
  try {
    console.log('🚀 === COMPREHENSIVE WORKSPACE FIX AND SERVICE STARTUP ===');
    console.log('This will fix pnpm workspace issues and start the electrical application');
    
    // Step 1: Fix dependencies
    await fixBackendDependencies();
    await fixFrontendDependencies();
    
    // Step 2: Start services
    backendProcess = await startBackend();
    await new Promise(resolve => setTimeout(resolve, 10000)); // Wait for backend
    
    frontendProcess = await startFrontend();
    await new Promise(resolve => setTimeout(resolve, 15000)); // Wait for frontend
    
    // Step 3: Test services
    const serviceStatus = await testServices();
    
    if (serviceStatus.backend && serviceStatus.frontend) {
      console.log('\n✅ === SERVICES RUNNING SUCCESSFULLY ===');
      console.log('Frontend: http://localhost:3000');
      console.log('Backend: http://localhost:3001');
      console.log('Credentials: <EMAIL> / itsMike818!');
      
      // Step 4: Run comprehensive test
      const testSuccess = await runComprehensiveTest();
      
      if (testSuccess) {
        console.log('\n🎉 === COMPLETE SUCCESS ===');
        console.log('Application is fully functional and tested!');
      }
      
    } else {
      console.log('\n❌ === SERVICE STARTUP FAILED ===');
      if (!serviceStatus.backend) console.log('  - Backend not responding');
      if (!serviceStatus.frontend) console.log('  - Frontend not responding');
    }
    
  } catch (error) {
    console.error('Main execution error:', error);
  } finally {
    console.log('\nServices will continue running. Press Ctrl+C to stop.');
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\nShutting down services...');
  cleanup();
  process.exit(0);
});

process.on('SIGTERM', () => {
  cleanup();
  process.exit(0);
});

main();
