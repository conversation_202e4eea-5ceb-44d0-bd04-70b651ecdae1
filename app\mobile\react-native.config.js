const path = require('path');

module.exports = {
  project: {
    ios: {
      sourceDir: './ios',
    },
    android: {
      sourceDir: './android',
    },
  },
  assets: ['./src/assets/fonts/'],
  dependencies: {
    'react-native-vector-icons': {
      platforms: {
        ios: null,
      },
    },
    'react-native-sqlite-storage': {
      platforms: {
        android: null, // Temporarily disable auto-linking for Android
      },
    },
  },
  // Standard npm configuration - React Native modules are in node_modules
  // No special configuration needed for npm
};