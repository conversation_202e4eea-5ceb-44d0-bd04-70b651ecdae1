Need to install the following packages:
react-native@0.80.1
Ok to proceed? (y) 
npm warn deprecated inflight@1.0.6: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.
npm warn deprecated rimraf@3.0.2: Rimraf versions prior to v4 are no longer supported
npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
error Unable to find React Native files looking up from c:\Projects\electrical\app\mobile. Make sure "react-native" module is installed in your project dependencies. If you are using React Native from a non-standard location, consider setting: { reactNativePath: "./path/to/react-native" } in your `react-native.config.js`.
