import React from 'react';
import { useAuthStore } from '../stores/auth';

export function useAuthInit() {
  const hasHydrated = useAuthStore((state) => state.hasHydrated);
  const isLoading = useAuthStore((state) => state.isLoading);
  const setHasHydrated = useAuthStore((state) => state.setHasHydrated);

  // Force initialization after a short delay if hydration hasn't completed
  const [forceInit, setForceInit] = React.useState(false);

  React.useEffect(() => {
    console.log('[useAuthInit] Effect running, hasHydrated:', hasHydrated, 'isLoading:', isLoading);

    // If hydration hasn't completed after 2 seconds, force it
    const timer = setTimeout(() => {
      if (!hasHydrated) {
        console.log('[useAuthInit] Forcing hydration after timeout');
        setHasHydrated(true);
      }
      setForceInit(true);
    }, 2000);

    return () => clearTimeout(timer);
  }, [hasHydrated, isLoading, setHasHydrated]);

  // Also check if localStorage is empty and force hydration immediately
  React.useEffect(() => {
    const authStorage = localStorage.getItem('auth-storage');
    if (!authStorage && !hasHydrated) {
      console.log('[useAuthInit] No auth storage found, forcing hydration');
      setHasHydrated(true);
    }
  }, [hasHydrated, setHasHydrated]);

  const isInitialized = forceInit || (hasHydrated && !isLoading);

  console.log('[useAuthInit] Returning isInitialized:', isInitialized, {
    forceInit,
    hasHydrated,
    isLoading
  });

  return { isInitialized };
}