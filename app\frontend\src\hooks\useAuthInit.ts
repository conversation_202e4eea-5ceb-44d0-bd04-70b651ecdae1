import React from 'react';
import { useAuthStore } from '../stores/auth';

export function useAuthInit() {
  // Simply return whether the store has hydrated and is no longer loading
  const hasHydrated = useAuthStore((state) => state.hasHydrated);
  const isLoading = useAuthStore((state) => state.isLoading);
  
  // TEMPORARY FIX: Force initialization after a short delay
  const [forceInit, setForceInit] = React.useState(false);
  
  React.useEffect(() => {
    const timer = setTimeout(() => {
      setForceInit(true);
    }, 1000); // Wait 1 second then force initialization
    
    return () => clearTimeout(timer);
  }, []);
  
  const isInitialized = forceInit || (hasHydrated && !isLoading);
  
  return { isInitialized };
}