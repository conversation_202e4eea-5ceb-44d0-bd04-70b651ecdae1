import { GoogleGenAI } from '@google/genai';
import { prisma } from '../../database/prisma';
import { Decimal } from 'decimal.js';

interface CostPrediction {
  estimatedCost: number;
  confidence: number; // 0-100%
  breakdown: CostBreakdown;
  recommendations: string[];
  riskFactors: RiskFactor[];
  marketFactors: MarketFactor[];
}

interface CostBreakdown {
  materials: number;
  labor: number;
  permits: number;
  equipment: number;
  overhead: number;
  profit: number;
  contingency: number;
}

interface RiskFactor {
  factor: string;
  impact: 'HIGH' | 'MEDIUM' | 'LOW';
  mitigation: string;
  costImpact: number;
}

interface MarketFactor {
  factor: string;
  trend: 'INCREASING' | 'STABLE' | 'DECREASING';
  impact: number; // percentage
}

export class CostPredictionService {
  private genAI: GoogleGenAI;
  private model: any;

  constructor() {
    if (!process.env.GEMINI_API_KEY) {
      throw new Error('GEMINI_API_KEY environment variable is required');
    }
    
    this.genAI = new GoogleGenAI({ apiKey: process.env.GEMINI_API_KEY });
    this.model = this.genAI.models;
  }

  async predictProjectCost(projectData: any): Promise<CostPrediction> {
    try {
      // Get similar historical projects
      const historicalProjects = await this.getHistoricalProjects(projectData);
      
      // Get current market conditions
      const marketData = await this.getCurrentMarketData();
      
      const predictionPrompt = `
      As an electrical contracting cost estimation expert, analyze this project and predict costs.
      
      New Project Details:
      - Type: ${projectData.type}
      - Square Footage: ${projectData.square_footage}
      - Voltage System: ${projectData.voltage_system}
      - Service Size: ${projectData.service_size}A
      - Location: ${projectData.location}
      - Scope: ${projectData.scope || 'Standard electrical installation'}
      
      Historical Similar Projects (last 12 months):
      ${JSON.stringify(historicalProjects, null, 2)}
      
      Current Market Conditions:
      ${JSON.stringify(marketData, null, 2)}
      
      Provide a detailed cost prediction with:
      {
        "estimatedCost": total_cost_in_dollars,
        "confidence": confidence_percentage_0_to_100,
        "breakdown": {
          "materials": material_cost,
          "labor": labor_cost,
          "permits": permit_cost,
          "equipment": equipment_rental_cost,
          "overhead": overhead_cost,
          "profit": profit_margin,
          "contingency": contingency_amount
        },
        "recommendations": [
          "cost saving recommendations",
          "value engineering suggestions"
        ],
        "riskFactors": [
          {
            "factor": "risk description",
            "impact": "HIGH|MEDIUM|LOW",
            "mitigation": "how to mitigate",
            "costImpact": potential_cost_increase
          }
        ],
        "marketFactors": [
          {
            "factor": "market condition",
            "trend": "INCREASING|STABLE|DECREASING",
            "impact": percentage_impact
          }
        ]
      }
      
      Consider:
      - Current copper and aluminum prices
      - Local labor rates and availability
      - Seasonal factors
      - Supply chain conditions
      - Code requirements for the area
      - Complexity factors based on project type
      `;

      const response = await this.model.generateContent({
        model: 'gemini-2.0-flash-001',
        contents: predictionPrompt,
        generationConfig: {
          responseFormat: 'json'
        }
      });

      const prediction = JSON.parse(response.text);
      
      // Store prediction for future learning
      await this.storePrediction(projectData, prediction);
      
      return prediction;
    } catch (error) {
      console.error('Cost prediction error:', error);
      throw new Error('Unable to generate cost prediction');
    }
  }

  async predictMaterialCosts(
    materials: Array<{ item: string; quantity: number }>,
    timeframe: number = 30 // days
  ): Promise<{
    current: number;
    predicted: number;
    trends: Array<{ item: string; trend: string; change: number }>;
  }> {
    const materialPricePrompt = `
    Predict material costs for these electrical items over the next ${timeframe} days.
    
    Materials List:
    ${JSON.stringify(materials, null, 2)}
    
    Consider:
    - Current commodity prices (copper, aluminum, steel)
    - Supply chain conditions
    - Seasonal demand patterns
    - Manufacturing capacity
    - Recent price history
    
    Return:
    {
      "current": current_total_cost,
      "predicted": predicted_total_cost,
      "trends": [
        {
          "item": "material name",
          "trend": "price trend description",
          "change": percentage_change
        }
      ]
    }
    `;

    try {
      const response = await this.model.generateContent({
        model: 'gemini-2.0-flash-001',
        contents: materialPricePrompt,
        generationConfig: {
          responseFormat: 'json'
        }
      });

      return JSON.parse(response.text);
    } catch (error) {
      console.error('Material cost prediction error:', error);
      return {
        current: 0,
        predicted: 0,
        trends: []
      };
    }
  }

  async optimizeBidPrice(
    baseCost: number,
    projectDetails: any,
    competitorData?: any
  ): Promise<{
    recommendedBid: number;
    winProbability: number;
    profitMargin: number;
    justification: string;
  }> {
    const bidOptimizationPrompt = `
    Optimize bid pricing for this electrical project.
    
    Base Cost: $${baseCost}
    Project Details: ${JSON.stringify(projectDetails)}
    Competitor Information: ${JSON.stringify(competitorData || 'Limited data')}
    
    Consider:
    - Market competition level
    - Project complexity and risk
    - Client relationship and history
    - Current workload and capacity
    - Strategic value of the project
    - Typical profit margins in the area
    
    Recommend optimal bid price with:
    {
      "recommendedBid": bid_amount,
      "winProbability": percentage_0_to_100,
      "profitMargin": percentage,
      "justification": "detailed reasoning"
    }
    `;

    try {
      const response = await this.model.generateContent({
        model: 'gemini-2.0-flash-001',
        contents: bidOptimizationPrompt,
        generationConfig: {
          responseFormat: 'json'
        }
      });

      return JSON.parse(response.text);
    } catch (error) {
      console.error('Bid optimization error:', error);
      // Fallback to standard markup
      const recommendedBid = baseCost * 1.15; // 15% markup
      return {
        recommendedBid,
        winProbability: 50,
        profitMargin: 13, // After overhead
        justification: 'Standard markup applied due to prediction service unavailability'
      };
    }
  }

  private async getHistoricalProjects(projectData: any) {
    try {
      // Get similar completed projects
      const projects = await prisma.project.findMany({
        where: {
          type: projectData.type,
          square_footage: {
            gte: projectData.square_footage * 0.7,
            lte: projectData.square_footage * 1.3
          },
          status: 'COMPLETED',
          created_at: {
            gte: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000) // Last year
          }
        },
        include: {
          estimates: {
            where: { 
              status: 'APPROVED'
            },
            include: {
              material_items: true,
              labor_items: true
            }
          }
        },
        take: 10,
        orderBy: { created_at: 'desc' }
      });

      // Transform to summary format
      return projects.map(project => {
        const approvedEstimate = project.estimates[0];
        const materialCost = approvedEstimate?.material_items.reduce(
          (sum, item) => sum + (new Decimal(item.unit_cost).times(item.quantity).toNumber()),
          0
        ) || 0;
        
        const laborCost = approvedEstimate?.labor_items.reduce(
          (sum, item) => sum + (new Decimal(item.hourly_rate).times(item.hours).toNumber()),
          0
        ) || 0;

        return {
          project_type: project.type,
          square_footage: project.square_footage,
          service_size: project.service_size,
          total_cost: approvedEstimate?.total_amount || 0,
          material_cost: materialCost,
          labor_cost: laborCost,
          completion_date: project.updated_at,
          duration_days: Math.floor(
            (project.updated_at.getTime() - project.created_at.getTime()) / (1000 * 60 * 60 * 24)
          )
        };
      });
    } catch (error) {
      console.error('Error fetching historical projects:', error);
      return [];
    }
  }

  private async getCurrentMarketData() {
    // In a real implementation, this would fetch from market data APIs
    // For now, return simulated market conditions
    return {
      copper_price_trend: 'INCREASING',
      copper_change_30d: 5.2,
      aluminum_price_trend: 'STABLE',
      aluminum_change_30d: -0.8,
      labor_availability: 'TIGHT',
      average_hourly_rate: 75,
      permit_costs_trend: 'INCREASING',
      supply_chain_status: 'MODERATE_DELAYS',
      inflation_rate: 3.2
    };
  }

  private async storePrediction(projectData: any, prediction: CostPrediction) {
    try {
      // Store for future model improvement
      await prisma.calculationLog.create({
        data: {
          calculation_type: 'COST_PREDICTION',
          input_data: JSON.stringify(projectData),
          output_data: JSON.stringify(prediction),
          performed_by: 'AI_SERVICE',
          project_id: projectData.project_id
        }
      });
    } catch (error) {
      console.error('Failed to store prediction:', error);
    }
  }

  // Analyze cost overruns in completed projects
  async analyzeCostOverruns(): Promise<{
    patterns: Array<{
      pattern: string;
      frequency: number;
      averageOverrun: number;
      prevention: string;
    }>;
    recommendations: string[];
  }> {
    const projects = await prisma.project.findMany({
      where: {
        status: 'COMPLETED',
        created_at: {
          gte: new Date(Date.now() - 180 * 24 * 60 * 60 * 1000) // Last 6 months
        }
      },
      include: {
        estimates: {
          include: {
            material_items: true,
            labor_items: true
          }
        }
      }
    });

    const analysisPrompt = `
    Analyze these completed electrical projects for cost overrun patterns.
    
    Projects: ${JSON.stringify(projects, null, 2)}
    
    Identify:
    1. Common causes of cost overruns
    2. Project types most prone to overruns
    3. Seasonal patterns
    4. Material vs labor overrun trends
    
    Return:
    {
      "patterns": [
        {
          "pattern": "description of pattern",
          "frequency": percentage_of_projects,
          "averageOverrun": percentage,
          "prevention": "how to prevent"
        }
      ],
      "recommendations": [
        "specific recommendations to reduce overruns"
      ]
    }
    `;

    try {
      const response = await this.model.generateContent({
        model: 'gemini-2.0-flash-001',
        contents: analysisPrompt,
        generationConfig: {
          responseFormat: 'json'
        }
      });

      return JSON.parse(response.text);
    } catch (error) {
      console.error('Cost overrun analysis error:', error);
      return {
        patterns: [],
        recommendations: ['Unable to analyze cost overruns at this time']
      };
    }
  }
}