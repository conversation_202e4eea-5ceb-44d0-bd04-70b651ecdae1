import React, { useEffect } from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { useSelector } from 'react-redux';
// import SplashScreen from 'react-native-splash-screen';

import { RootState } from '@store/index';
import { RootStackParamList } from '@types/navigation';
import AuthNavigator from './AuthNavigator';
import MainNavigator from './MainNavigator';
import ProjectDetailsScreen from '@screens/projects/ProjectDetailsScreen';
import InspectionDetailsScreen from '@screens/inspections/InspectionDetailsScreen';
import CalculatorModal from '@screens/modals/CalculatorModal';
import BarcodeScannerScreen from '@screens/common/BarcodeScannerScreen';
import PhotoViewerScreen from '@screens/common/PhotoViewerScreen';

const Stack = createNativeStackNavigator<RootStackParamList>();

const RootNavigator: React.FC = () => {
  const { isAuthenticated } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    // Hide splash screen when navigator is ready
    // SplashScreen.hide();
  }, []);

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        animation: 'slide_from_right',
      }}
    >
      {!isAuthenticated ? (
        <Stack.Screen name="Auth" component={AuthNavigator} />
      ) : (
        <>
          <Stack.Screen name="Main" component={MainNavigator} />
          <Stack.Screen
            name="ProjectDetails"
            component={ProjectDetailsScreen}
            options={{
              headerShown: true,
              title: 'Project Details',
              animation: 'slide_from_right',
            }}
          />
          <Stack.Screen
            name="InspectionDetails"
            component={InspectionDetailsScreen}
            options={{
              headerShown: true,
              title: 'Inspection Details',
              animation: 'slide_from_right',
            }}
          />
          <Stack.Screen
            name="CalculatorModal"
            component={CalculatorModal}
            options={{
              presentation: 'modal',
              headerShown: true,
              animation: 'slide_from_bottom',
            }}
          />
          <Stack.Screen
            name="BarcodeScanner"
            component={BarcodeScannerScreen}
            options={{
              headerShown: true,
              title: 'Scan Barcode',
              animation: 'slide_from_bottom',
            }}
          />
          <Stack.Screen
            name="PhotoViewer"
            component={PhotoViewerScreen}
            options={{
              presentation: 'fullScreenModal',
              animation: 'fade',
            }}
          />
        </>
      )}
    </Stack.Navigator>
  );
};

export default RootNavigator;