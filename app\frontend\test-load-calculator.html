<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Load Calculator Form</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="number"], select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .error {
            color: red;
            font-size: 14px;
            margin-top: 5px;
        }
        .success {
            color: green;
            font-size: 14px;
            margin-top: 5px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>Test Load Calculator - Input Validation</h1>
    
    <form id="loadForm">
        <div class="form-group">
            <label for="square_footage">Square Footage (Required)</label>
            <input type="number" id="square_footage" name="square_footage" placeholder="2500">
            <div class="error" id="square_footage_error"></div>
        </div>
        
        <div class="form-group">
            <label for="small_appliance_circuits">Small Appliance Circuits (Min: 2)</label>
            <input type="number" id="small_appliance_circuits" name="small_appliance_circuits" value="2" min="2">
            <div class="error" id="small_appliance_circuits_error"></div>
        </div>
        
        <div class="form-group">
            <label for="heating_va">Heating Load (VA)</label>
            <input type="number" id="heating_va" name="heating_va" placeholder="0" value="0">
            <div class="error" id="heating_va_error"></div>
        </div>
        
        <div class="form-group">
            <label for="cooling_va">Cooling Load (VA)</label>
            <input type="number" id="cooling_va" name="cooling_va" placeholder="0" value="0">
            <div class="error" id="cooling_va_error"></div>
        </div>
        
        <div class="form-group">
            <label for="largest_motor_va">Largest Motor (VA)</label>
            <input type="number" id="largest_motor_va" name="largest_motor_va" placeholder="0" value="0">
            <div class="error" id="largest_motor_va_error"></div>
        </div>
        
        <div class="form-group">
            <label for="other_loads_va">Other Loads (VA)</label>
            <input type="number" id="other_loads_va" name="other_loads_va" placeholder="0" value="0">
            <div class="error" id="other_loads_va_error"></div>
        </div>
        
        <button type="submit">Calculate Load</button>
        
        <div id="result" style="margin-top: 20px;"></div>
    </form>
    
    <script>
        // Test the form validation logic
        const form = document.getElementById('loadForm');
        const result = document.getElementById('result');
        
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            
            // Clear previous errors
            document.querySelectorAll('.error').forEach(el => el.textContent = '');
            result.innerHTML = '';
            
            // Get form data
            const formData = new FormData(form);
            const data = {};
            let hasErrors = false;
            
            // Parse and validate inputs
            for (let [key, value] of formData.entries()) {
                const inputEl = document.getElementById(key);
                const errorEl = document.getElementById(key + '_error');
                
                // Convert to number
                const numValue = value === '' ? NaN : Number(value);
                
                // Validate
                if (key === 'square_footage') {
                    if (isNaN(numValue) || numValue <= 0) {
                        errorEl.textContent = 'Square footage must be a positive number';
                        hasErrors = true;
                    } else if (!Number.isInteger(numValue)) {
                        errorEl.textContent = 'Square footage must be a whole number';
                        hasErrors = true;
                    }
                } else if (key === 'small_appliance_circuits') {
                    if (isNaN(numValue) || numValue < 2) {
                        errorEl.textContent = 'Minimum 2 circuits required';
                        hasErrors = true;
                    } else if (!Number.isInteger(numValue)) {
                        errorEl.textContent = 'Must be a whole number';
                        hasErrors = true;
                    }
                } else {
                    if (isNaN(numValue) || numValue < 0) {
                        errorEl.textContent = 'Cannot be negative';
                        hasErrors = true;
                    }
                }
                
                data[key] = numValue;
            }
            
            if (!hasErrors) {
                result.innerHTML = '<div class="success">✓ Form validation passed! Data ready for submission:</div>';
                result.innerHTML += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } else {
                result.innerHTML = '<div class="error">✗ Please fix the errors above before submitting.</div>';
            }
        });
        
        // Test with empty values
        console.log('Testing empty string conversion:');
        console.log('Empty string with Number():', Number(''));
        console.log('Empty string with parseInt():', parseInt(''));
        console.log('Empty string with parseFloat():', parseFloat(''));
        console.log('Empty string with +:', +'');
        
        // This shows that Number('') returns 0, but we need to handle empty inputs differently
    </script>
</body>
</html>