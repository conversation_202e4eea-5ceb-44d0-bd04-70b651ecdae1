import PDFDocument from 'pdfkit';
import { Quote } from '@prisma/client';

export class PdfGenerationService {
  async generateQuotePDF(
    quote: Quote & {
      customer: any;
      project: any;
      items: any[];
      company?: any;
    },
    companyId: string
  ): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      try {
        const doc = new PDFDocument({
          size: 'A4',
          margin: 50
        });

        const chunks: Buffer[] = [];
        doc.on('data', (chunk) => chunks.push(chunk));
        doc.on('end', () => resolve(Buffer.concat(chunks)));

        // Header
        doc.fontSize(20).text('QUOTE', 50, 50);
        doc.fontSize(12).text(`Quote #: ${quote.quote_number}`, 50, 80);
        doc.text(`Date: ${new Date(quote.created_at).toLocaleDateString()}`, 50, 100);
        doc.text(`Valid Until: ${new Date(quote.expires_at).toLocaleDateString()}`, 50, 120);

        // Company info (if available)
        if (quote.company) {
          doc.fontSize(14).text(quote.company.name, 350, 50);
          doc.fontSize(10)
            .text(quote.company.address || '', 350, 70)
            .text(`${quote.company.city || ''}, ${quote.company.state || ''} ${quote.company.zip || ''}`, 350, 85)
            .text(quote.company.phone || '', 350, 100)
            .text(quote.company.email || '', 350, 115);
        }

        // Customer info
        if (quote.customer) {
          doc.fontSize(12).text('BILL TO:', 50, 160);
          doc.fontSize(10)
            .text(quote.customer.name, 50, 180)
            .text(quote.customer.email || '', 50, 195)
            .text(quote.customer.phone || '', 50, 210);
        } else {
          doc.fontSize(12).text('BILL TO:', 50, 160);
          doc.fontSize(10).text('No customer assigned', 50, 180);
        }

        // Project info
        if (quote.project) {
          doc.fontSize(12).text('PROJECT:', 300, 160);
          doc.fontSize(10)
            .text(quote.project.name, 300, 180)
            .text(quote.project.address, 300, 195)
            .text(`${quote.project.city}, ${quote.project.state} ${quote.project.zip}`, 300, 210);
        } else {
          doc.fontSize(12).text('PROJECT:', 300, 160);
          doc.fontSize(10).text('No project assigned', 300, 180);
        }

        // Project overview and scope
        if (quote.project_overview) {
          doc.moveDown(2);
          doc.fontSize(12).text('PROJECT OVERVIEW', 50, doc.y);
          doc.fontSize(10).text(quote.project_overview, 50, doc.y + 15, {
            width: 500,
            align: 'justify'
          });
        }

        if (quote.scope_of_work) {
          doc.moveDown(2);
          doc.fontSize(12).text('SCOPE OF WORK', 50, doc.y);
          doc.fontSize(10).text(quote.scope_of_work, 50, doc.y + 15, {
            width: 500,
            align: 'justify'
          });
        }

        // Line items
        doc.moveDown(2);
        doc.fontSize(12).text('LINE ITEMS', 50, doc.y);
        
        // Table header
        const tableTop = doc.y + 20;
        doc.fontSize(10)
          .text('Item', 50, tableTop)
          .text('Qty', 250, tableTop)
          .text('Unit', 300, tableTop)
          .text('Price', 350, tableTop)
          .text('Total', 450, tableTop);

        // Draw line under header
        doc.moveTo(50, tableTop + 15)
          .lineTo(550, tableTop + 15)
          .stroke();

        // Items
        let yPosition = tableTop + 25;
        quote.items.forEach((item: any) => {
          if (yPosition > 700) {
            doc.addPage();
            yPosition = 50;
          }

          doc.fontSize(9)
            .text(item.name, 50, yPosition, { width: 190 })
            .text(item.quantity.toString(), 250, yPosition)
            .text(item.unit, 300, yPosition)
            .text(`$${item.unitPrice?.toFixed(2) || '0.00'}`, 350, yPosition)
            .text(`$${item.totalPrice?.toFixed(2) || '0.00'}`, 450, yPosition);

          if (item.description) {
            yPosition += 15;
            doc.fontSize(8)
              .fillColor('#666666')
              .text(item.description, 70, yPosition, { width: 400 })
              .fillColor('#000000');
          }

          yPosition += 25;
        });

        // Totals
        doc.fontSize(10);
        const subtotal = quote.subtotal || 0;
        const tax = quote.tax || 0;
        const discount = quote.discount || 0;
        const total = quote.total || 0;

        yPosition += 20;
        doc.text('Subtotal:', 350, yPosition)
          .text(`$${subtotal.toFixed(2)}`, 450, yPosition);

        if (discount > 0) {
          yPosition += 15;
          doc.text('Discount:', 350, yPosition)
            .text(`-$${discount.toFixed(2)}`, 450, yPosition);
        }

        yPosition += 15;
        doc.text(`Tax (${((quote.tax_rate || 0) * 100).toFixed(2)}%):`, 350, yPosition)
          .text(`$${tax.toFixed(2)}`, 450, yPosition);

        yPosition += 20;
        doc.fontSize(12).text('Total:', 350, yPosition)
          .text(`$${total.toFixed(2)}`, 450, yPosition);

        // Terms and conditions
        if (quote.terms_and_conditions) {
          doc.addPage();
          doc.fontSize(12).text('TERMS AND CONDITIONS', 50, 50);
          doc.fontSize(9).text(quote.terms_and_conditions, 50, 80, {
            width: 500,
            align: 'justify'
          });
        }

        doc.end();
      } catch (error) {
        reject(error);
      }
    });
  }
}