import { Router } from 'express';
import { z } from 'zod';
import { prisma } from '../database/prisma';
import { authenticate, authorize, AuthRequest } from '../middleware/auth';
import { AppError } from '../middleware/errorHandler';
import { CustomerSchema } from '@electrical/shared';

const router: Router = Router();

// Apply authentication to all routes
router.use(authenticate);

// Pagination schema
const paginationSchema = z.object({
  page: z.string().transform(Number).default('1'),
  limit: z.string().transform(Number).default('20'),
  search: z.string().optional(),
  sortBy: z.enum(['name', 'created_at', 'updated_at']).default('name'),
  sortOrder: z.enum(['asc', 'desc']).default('asc')
});

// Create customer schema (exclude computed fields)
const createCustomerSchema = CustomerSchema.omit({
  id: true,
  created_at: true,
  updated_at: true,
  deleted_at: true
});

// Update customer schema (all fields optional)
const updateCustomerSchema = createCustomerSchema.partial();

// Get all customers
router.get('/', async (req: AuthRequest, res, next) => {
  try {
    const params = paginationSchema.parse(req.query);
    const skip = (params.page - 1) * params.limit;
    
    // Build where clause
    const where = {
      deleted_at: null,
      ...(params.search && {
        OR: [
          { name: { contains: params.search, mode: 'insensitive' as const } },
          { email: { contains: params.search, mode: 'insensitive' as const } },
          { phone: { contains: params.search, mode: 'insensitive' as const } }
        ]
      })
    };
    
    // Get customers with pagination
    const [customers, total] = await Promise.all([
      prisma.customer.findMany({
        where,
        skip,
        take: params.limit,
        orderBy: { [params.sortBy]: params.sortOrder },
        include: {
          _count: {
            select: { projects: true }
          }
        }
      }),
      prisma.customer.count({ where })
    ]);
    
    res.json({
      data: customers,
      pagination: {
        page: params.page,
        limit: params.limit,
        total,
        totalPages: Math.ceil(total / params.limit)
      }
    });
  } catch (error) {
    next(error);
  }
});

// Get customer by ID
router.get('/:id', async (req: AuthRequest, res, next) => {
  try {
    const customer = await prisma.customer.findFirst({
      where: {
        id: req.params.id,
        deleted_at: null
      },
      include: {
        projects: {
          where: { status: { not: 'COMPLETED' } },
          orderBy: { created_at: 'desc' },
          take: 10
        },
        _count: {
          select: {
            projects: true
          }
        }
      }
    });
    
    if (!customer) {
      throw new AppError(404, 'Customer not found', true, 'CUSTOMER_NOT_FOUND');
    }
    
    res.json(customer);
  } catch (error) {
    next(error);
  }
});

// Create customer
router.post('/', authorize('admin', 'estimator'), async (req: AuthRequest, res, next) => {
  try {
    const data = createCustomerSchema.parse(req.body);
    
    const customer = await prisma.customer.create({
      data: {
        ...data,
        name: data.name!, // Ensure name is always provided
        payment_terms: data.payment_terms || 'NET30'
      },
      include: {
        _count: {
          select: { projects: true }
        }
      }
    });
    
    res.status(201).json(customer);
  } catch (error) {
    next(error);
  }
});

// Update customer
router.put('/:id', authorize('admin', 'estimator'), async (req: AuthRequest, res, next) => {
  try {
    const data = updateCustomerSchema.parse(req.body);
    
    // Check if customer exists
    const existing = await prisma.customer.findFirst({
      where: {
        id: req.params.id,
        deleted_at: null
      }
    });
    
    if (!existing) {
      throw new AppError(404, 'Customer not found', true, 'CUSTOMER_NOT_FOUND');
    }
    
    const customer = await prisma.customer.update({
      where: { id: req.params.id },
      data,
      include: {
        _count: {
          select: { projects: true }
        }
      }
    });
    
    res.json(customer);
  } catch (error) {
    next(error);
  }
});

// Soft delete customer
router.delete('/:id', authorize('admin'), async (req: AuthRequest, res, next) => {
  try {
    // Check if customer exists and has no active projects
    const customer = await prisma.customer.findFirst({
      where: {
        id: req.params.id,
        deleted_at: null
      },
      include: {
        projects: {
          where: {
            status: { notIn: ['COMPLETED', 'CANCELLED'] }
          }
        }
      }
    });
    
    if (!customer) {
      throw new AppError(404, 'Customer not found', true, 'CUSTOMER_NOT_FOUND');
    }
    
    if (customer.projects.length > 0) {
      throw new AppError(
        400,
        'Cannot delete customer with active projects',
        true,
        'ACTIVE_PROJECTS_EXIST'
      );
    }
    
    // Soft delete
    await prisma.customer.update({
      where: { id: req.params.id },
      data: { deleted_at: new Date() }
    });
    
    res.json({ message: 'Customer deleted successfully' });
  } catch (error) {
    next(error);
  }
});

// Get customer projects
router.get('/:id/projects', async (req: AuthRequest, res, next) => {
  try {
    const customer = await prisma.customer.findFirst({
      where: {
        id: req.params.id,
        deleted_at: null
      }
    });
    
    if (!customer) {
      throw new AppError(404, 'Customer not found', true, 'CUSTOMER_NOT_FOUND');
    }
    
    const projects = await prisma.project.findMany({
      where: { customer_id: req.params.id },
      orderBy: { created_at: 'desc' },
      include: {
        _count: {
          select: {
            estimates: true,
            calculations: true
          }
        }
      }
    });
    
    res.json(projects);
  } catch (error) {
    next(error);
  }
});

// Get customer credit status
router.get('/:id/credit-status', authorize('admin', 'estimator'), async (req: AuthRequest, res, next) => {
  try {
    const customer = await prisma.customer.findFirst({
      where: {
        id: req.params.id,
        deleted_at: null
      }
    });
    
    if (!customer) {
      throw new AppError(404, 'Customer not found', true, 'CUSTOMER_NOT_FOUND');
    }
    
    // Calculate outstanding balance from approved estimates
    const outstandingEstimates = await prisma.estimate.findMany({
      where: {
        project: {
          customer_id: req.params.id,
          status: { in: ['IN_PROGRESS', 'COMPLETED'] }
        },
        status: 'APPROVED'
      },
      select: {
        total_amount: true
      }
    });
    
    const totalOutstanding = outstandingEstimates.reduce(
      (sum, estimate) => sum + estimate.total_amount,
      0
    );
    
    const creditLimit = customer.credit_limit || 0;
    const availableCredit = creditLimit - totalOutstanding;
    
    res.json({
      credit_limit: creditLimit,
      outstanding_balance: totalOutstanding,
      available_credit: availableCredit,
      payment_terms: customer.payment_terms,
      status: availableCredit > 0 ? 'GOOD' : 'LIMIT_REACHED'
    });
  } catch (error) {
    next(error);
  }
});

export { router as customersRouter };