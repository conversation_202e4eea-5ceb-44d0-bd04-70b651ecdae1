-- AlterTable
-- Make customer_id and project_id optional
CREATE TABLE "Quote_new" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "quote_number" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "customer_id" TEXT,
    "project_id" TEXT,
    "company_id" TEXT NOT NULL,
    "version" INTEGER NOT NULL DEFAULT 1,
    "parent_quote_id" TEXT,
    "project_overview" TEXT,
    "scope_of_work" TEXT,
    "materials_included" TEXT,
    "exclusions" TEXT,
    "terms_and_conditions" TEXT,
    "items" TEXT NOT NULL,
    "ai_generation_data" TEXT,
    "subtotal" REAL NOT NULL,
    "tax_rate" REAL NOT NULL DEFAULT 0,
    "tax_amount" REAL NOT NULL DEFAULT 0,
    "discount" REAL NOT NULL DEFAULT 0,
    "discount_type" TEXT NOT NULL DEFAULT 'PERCENTAGE',
    "total" REAL NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'DRAFT',
    "expires_at" DATETIME NOT NULL,
    "customer_notes" TEXT,
    "internal_notes" TEXT,
    "created_by_id" TEXT NOT NULL,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL,
    CONSTRAINT "Quote_customer_id_fkey" FOREIGN KEY ("customer_id") REFERENCES "Customer" ("id") ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT "Quote_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "Project" ("id") ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT "Quote_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "Company" ("id") ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT "Quote_parent_quote_id_fkey" FOREIGN KEY ("parent_quote_id") REFERENCES "Quote" ("id") ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT "Quote_created_by_id_fkey" FOREIGN KEY ("created_by_id") REFERENCES "User" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- Copy data from old table to new table
INSERT INTO "Quote_new" SELECT * FROM "Quote";

-- Drop old table
DROP TABLE "Quote";

-- Rename new table
ALTER TABLE "Quote_new" RENAME TO "Quote";

-- Recreate indexes
CREATE UNIQUE INDEX "Quote_quote_number_key" ON "Quote"("quote_number");
CREATE INDEX "Quote_customer_id_idx" ON "Quote"("customer_id");
CREATE INDEX "Quote_project_id_idx" ON "Quote"("project_id");
CREATE INDEX "Quote_company_id_idx" ON "Quote"("company_id");
CREATE INDEX "Quote_created_by_id_idx" ON "Quote"("created_by_id");
CREATE INDEX "Quote_status_idx" ON "Quote"("status");
CREATE INDEX "Quote_created_at_idx" ON "Quote"("created_at");