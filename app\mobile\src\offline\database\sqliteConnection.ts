import SQLite, { SQLiteDatabase } from 'react-native-sqlite-storage';

// Enable debugging for development
SQLite.enablePromise(true);
SQLite.DEBUG(__DEV__);

const DATABASE_NAME = 'electrical_contractor.db';
const DATABASE_VERSION = 1;

interface TableSchema {
  name: string;
  columns: string[];
}

const TABLES: TableSchema[] = [
  {
    name: 'projects',
    columns: [
      'id TEXT PRIMARY KEY',
      'name TEXT NOT NULL',
      'description TEXT',
      'address TEXT',
      'clientName TEXT',
      'clientPhone TEXT',
      'clientEmail TEXT',
      'status TEXT CHECK(status IN ("active", "completed", "on-hold", "cancelled"))',
      'estimatedBudget REAL',
      'actualCost REAL',
      'startDate TEXT',
      'endDate TEXT',
      'notes TEXT',
      'metadata TEXT',
      'remoteId TEXT',
      'syncStatus TEXT DEFAULT "pending"',
      'lastSyncedAt TEXT',
      'conflictData TEXT',
      'version INTEGER DEFAULT 1',
      'isDeleted BOOLEAN DEFAULT 0',
      'createdAt TEXT DEFAULT CURRENT_TIMESTAMP',
      'updatedAt TEXT DEFAULT CURRENT_TIMESTAMP',
    ],
  },
  {
    name: 'panels',
    columns: [
      'id TEXT PRIMARY KEY',
      'projectId TEXT NOT NULL',
      'name TEXT NOT NULL',
      'type TEXT',
      'mainBreaker INTEGER',
      'voltage INTEGER',
      'phases INTEGER',
      'location TEXT',
      'notes TEXT',
      'remoteId TEXT',
      'syncStatus TEXT DEFAULT "pending"',
      'lastSyncedAt TEXT',
      'conflictData TEXT',
      'version INTEGER DEFAULT 1',
      'isDeleted BOOLEAN DEFAULT 0',
      'createdAt TEXT DEFAULT CURRENT_TIMESTAMP',
      'updatedAt TEXT DEFAULT CURRENT_TIMESTAMP',
      'FOREIGN KEY (projectId) REFERENCES projects(id) ON DELETE CASCADE',
    ],
  },
  {
    name: 'circuits',
    columns: [
      'id TEXT PRIMARY KEY',
      'panelId TEXT NOT NULL',
      'circuitNumber INTEGER NOT NULL',
      'name TEXT NOT NULL',
      'amperage INTEGER',
      'voltage INTEGER',
      'phase TEXT',
      'wireSize TEXT',
      'cableType TEXT',
      'length REAL',
      'load REAL',
      'notes TEXT',
      'remoteId TEXT',
      'syncStatus TEXT DEFAULT "pending"',
      'lastSyncedAt TEXT',
      'conflictData TEXT',
      'version INTEGER DEFAULT 1',
      'isDeleted BOOLEAN DEFAULT 0',
      'createdAt TEXT DEFAULT CURRENT_TIMESTAMP',
      'updatedAt TEXT DEFAULT CURRENT_TIMESTAMP',
      'FOREIGN KEY (panelId) REFERENCES panels(id) ON DELETE CASCADE',
    ],
  },
  {
    name: 'materials',
    columns: [
      'id TEXT PRIMARY KEY',
      'projectId TEXT NOT NULL',
      'name TEXT NOT NULL',
      'description TEXT',
      'sku TEXT',
      'unitPrice REAL',
      'quantity REAL',
      'unit TEXT',
      'vendor TEXT',
      'category TEXT',
      'notes TEXT',
      'remoteId TEXT',
      'syncStatus TEXT DEFAULT "pending"',
      'lastSyncedAt TEXT',
      'conflictData TEXT',
      'version INTEGER DEFAULT 1',
      'isDeleted BOOLEAN DEFAULT 0',
      'createdAt TEXT DEFAULT CURRENT_TIMESTAMP',
      'updatedAt TEXT DEFAULT CURRENT_TIMESTAMP',
      'FOREIGN KEY (projectId) REFERENCES projects(id) ON DELETE CASCADE',
    ],
  },
  {
    name: 'calculations',
    columns: [
      'id TEXT PRIMARY KEY',
      'projectId TEXT NOT NULL',
      'type TEXT NOT NULL',
      'name TEXT NOT NULL',
      'parameters TEXT NOT NULL',
      'results TEXT NOT NULL',
      'notes TEXT',
      'remoteId TEXT',
      'syncStatus TEXT DEFAULT "pending"',
      'lastSyncedAt TEXT',
      'conflictData TEXT',
      'version INTEGER DEFAULT 1',
      'isDeleted BOOLEAN DEFAULT 0',
      'createdAt TEXT DEFAULT CURRENT_TIMESTAMP',
      'updatedAt TEXT DEFAULT CURRENT_TIMESTAMP',
      'FOREIGN KEY (projectId) REFERENCES projects(id) ON DELETE CASCADE',
    ],
  },
  {
    name: 'photos',
    columns: [
      'id TEXT PRIMARY KEY',
      'projectId TEXT NOT NULL',
      'name TEXT NOT NULL',
      'localPath TEXT NOT NULL',
      'remotePath TEXT',
      'size INTEGER',
      'mimeType TEXT',
      'metadata TEXT',
      'thumbnail TEXT',
      'remoteId TEXT',
      'syncStatus TEXT DEFAULT "pending"',
      'lastSyncedAt TEXT',
      'conflictData TEXT',
      'version INTEGER DEFAULT 1',
      'isDeleted BOOLEAN DEFAULT 0',
      'createdAt TEXT DEFAULT CURRENT_TIMESTAMP',
      'updatedAt TEXT DEFAULT CURRENT_TIMESTAMP',
      'FOREIGN KEY (projectId) REFERENCES projects(id) ON DELETE CASCADE',
    ],
  },
  {
    name: 'sync_queue',
    columns: [
      'id TEXT PRIMARY KEY',
      'entityType TEXT NOT NULL',
      'entityId TEXT NOT NULL',
      'action TEXT NOT NULL',
      'retryCount INTEGER DEFAULT 0',
      'lastAttempt TEXT',
      'error TEXT',
      'priority INTEGER DEFAULT 1',
      'createdAt TEXT DEFAULT CURRENT_TIMESTAMP',
    ],
  },
];

class SQLiteConnection {
  private database: SQLiteDatabase | null = null;
  private isInitializing = false;

  async initialize(): Promise<void> {
    if (this.database || this.isInitializing) {
      return;
    }

    this.isInitializing = true;

    try {
      // Open database
      this.database = await SQLite.openDatabase({
        name: DATABASE_NAME,
        location: 'default',
      });

      console.log('[SQLite] Database opened successfully');

      // Create tables if they don't exist
      await this.createTables();

      // Set up triggers for updated_at
      await this.createTriggers();

      console.log('[SQLite] Database initialized successfully');
    } catch (error) {
      console.error('[SQLite] Failed to initialize database:', error);
      throw error;
    } finally {
      this.isInitializing = false;
    }
  }

  private async createTables(): Promise<void> {
    if (!this.database) {
      throw new Error('Database not initialized');
    }

    await this.database.transaction(async (tx) => {
      // Enable foreign keys
      await tx.executeSql('PRAGMA foreign_keys = ON;');

      // Create tables
      for (const table of TABLES) {
        const createTableSQL = `
          CREATE TABLE IF NOT EXISTS ${table.name} (
            ${table.columns.join(',\n            ')}
          );
        `;
        await tx.executeSql(createTableSQL);

        // Create indexes for common queries
        if (table.name !== 'sync_queue') {
          await tx.executeSql(
            `CREATE INDEX IF NOT EXISTS idx_${table.name}_sync_status ON ${table.name}(syncStatus);`
          );
          await tx.executeSql(
            `CREATE INDEX IF NOT EXISTS idx_${table.name}_is_deleted ON ${table.name}(isDeleted);`
          );
          if (table.columns.some((col) => col.startsWith('projectId'))) {
            await tx.executeSql(
              `CREATE INDEX IF NOT EXISTS idx_${table.name}_project_id ON ${table.name}(projectId);`
            );
          }
        }
      }

      // Create sync_queue indexes
      await tx.executeSql(
        'CREATE INDEX IF NOT EXISTS idx_sync_queue_entity ON sync_queue(entityType, entityId);'
      );
      await tx.executeSql(
        'CREATE INDEX IF NOT EXISTS idx_sync_queue_priority ON sync_queue(priority, createdAt);'
      );
    });
  }

  private async createTriggers(): Promise<void> {
    if (!this.database) {
      throw new Error('Database not initialized');
    }

    await this.database.transaction(async (tx) => {
      // Create updated_at triggers for each table (except sync_queue)
      for (const table of TABLES) {
        if (table.name !== 'sync_queue') {
          const triggerSQL = `
            CREATE TRIGGER IF NOT EXISTS update_${table.name}_updated_at
            AFTER UPDATE ON ${table.name}
            BEGIN
              UPDATE ${table.name} SET updatedAt = CURRENT_TIMESTAMP WHERE id = NEW.id;
            END;
          `;
          await tx.executeSql(triggerSQL);
        }
      }
    });
  }

  async getDatabase(): Promise<SQLiteDatabase> {
    if (!this.database) {
      await this.initialize();
    }
    if (!this.database) {
      throw new Error('Failed to initialize database');
    }
    return this.database;
  }

  async close(): Promise<void> {
    if (this.database) {
      await this.database.close();
      this.database = null;
      console.log('[SQLite] Database closed');
    }
  }

  async clearDatabase(): Promise<void> {
    const db = await this.getDatabase();
    await db.transaction(async (tx) => {
      // Delete all data from tables in reverse order (respecting foreign keys)
      const tablesToClear = ['photos', 'calculations', 'materials', 'circuits', 'panels', 'projects', 'sync_queue'];
      for (const table of tablesToClear) {
        await tx.executeSql(`DELETE FROM ${table};`);
      }
    });
    console.log('[SQLite] Database cleared');
  }

  async executeQuery<T = any>(sql: string, params: any[] = []): Promise<T[]> {
    const db = await this.getDatabase();
    const [result] = await db.executeSql(sql, params);
    
    const rows: T[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      rows.push(result.rows.item(i));
    }
    return rows;
  }

  async executeUpdate(sql: string, params: any[] = []): Promise<number> {
    const db = await this.getDatabase();
    const [result] = await db.executeSql(sql, params);
    return result.rowsAffected;
  }

  async runInTransaction<T>(callback: (tx: SQLite.Transaction) => Promise<T>): Promise<T> {
    const db = await this.getDatabase();
    let result: T;
    
    await db.transaction(async (tx) => {
      result = await callback(tx);
    });
    
    return result!;
  }
}

// Export singleton instance
export const sqliteConnection = new SQLiteConnection();

// Helper function to convert between TypeORM-style queries and SQLite
export function buildWhereClause(where: any): { sql: string; params: any[] } {
  if (!where || Object.keys(where).length === 0) {
    return { sql: '', params: [] };
  }

  const conditions: string[] = [];
  const params: any[] = [];

  for (const [key, value] of Object.entries(where)) {
    if (value === null) {
      conditions.push(`${key} IS NULL`);
    } else if (typeof value === 'object' && value._type) {
      // Handle special operators like In, Not, etc.
      switch (value._type) {
        case 'in':
          const placeholders = value.values.map(() => '?').join(',');
          conditions.push(`${key} IN (${placeholders})`);
          params.push(...value.values);
          break;
        case 'not':
          conditions.push(`${key} != ?`);
          params.push(value.value);
          break;
        case 'lessThan':
          conditions.push(`${key} < ?`);
          params.push(value.value);
          break;
        case 'moreThan':
          conditions.push(`${key} > ?`);
          params.push(value.value);
          break;
        case 'like':
          conditions.push(`${key} LIKE ?`);
          params.push(value.value);
          break;
        case 'between':
          conditions.push(`${key} BETWEEN ? AND ?`);
          params.push(value.from, value.to);
          break;
      }
    } else {
      conditions.push(`${key} = ?`);
      params.push(value);
    }
  }

  return {
    sql: conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '',
    params,
  };
}