import { useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAuthStore } from '../stores/auth';

export function useRouteDebug() {
  const location = useLocation();
  const navigate = useNavigate();
  const { isAuthenticated, user } = useAuthStore();

  useEffect(() => {
    // Only log in development
    if (import.meta.env.DEV) {
      console.group(`🔄 Route Navigation Debug`);
      console.log('Current Path:', location.pathname);
      console.log('Auth State:', {
        isAuthenticated,
        hasUser: !!user,
        userEmail: user?.email,
      });
      console.log('Location State:', location.state);
      console.log('Search Params:', location.search);
      
      // Log localStorage auth state
      try {
        const authStorage = localStorage.getItem('auth-storage');
        if (authStorage) {
          const parsed = JSON.parse(authStorage);
          console.log('Stored Auth:', {
            hasToken: !!parsed.state?.accessToken,
            hasUser: !!parsed.state?.user,
            userEmail: parsed.state?.user?.email,
          });
        } else {
          console.log('No stored auth state');
        }
      } catch (e) {
        console.error('Failed to parse auth storage:', e);
      }
      
      console.groupEnd();
    }
  }, [location, isAuthenticated, user]);

  return { location, navigate, isAuthenticated, user };
}