# Dependencies
node_modules/
.pnp
.pnp.js

# Environment variables
# .env (commented out to allow access)
.env.local
.env.*.local
.env.production
.env.development

# Production builds
dist/
build/
out/
.next/
.turbo/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# OS files
.DS_Store
*.swp
*.swo
*~
Thumbs.db

# IDE files
.idea/
.vscode/
*.sublime-project
*.sublime-workspace

# Testing
coverage/
.nyc_output/
*.lcov

# Temporary files
tmp/
temp/
.tmp/
*.tmp
*.temp

# Cache files
.cache/
.parcel-cache/
.eslintcache
.stylelintcache
*.tsbuildinfo

# Database files
*.db
*.sqlite
*.sqlite3
prisma/dev.db
prisma/dev.db-journal

# Upload directories
uploads/
public/uploads/

# Mobile specific
*.ipa
*.apk
*.aab
android/.gradle/
android/app/build/
ios/build/
ios/Pods/
ios/*.xcworkspace
*.pbxuser
*.xcuserstate
app/mobile/android/.gradle/
app/mobile/android/build/
app/mobile/android/app/build/
app/mobile/ios/build/
app/mobile/ios/Pods/

# React Native
.expo/
.expo-shared/

# Security
*.pem
*.key
*.crt
*.p12
*.pfx
*.jks
*.keystore

# Backup files
*.backup
*.bak
backup/

# Lock files (keep these in git)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# Generated files
generated/
__generated__/

# Documentation build
docs/_build/
docs/.vuepress/dist/

# Monitoring
.grafana-storage/

# Docker volumes
docker-volumes/

# Kubernetes secrets
secrets/
*.secret.yaml
*.secret.yml

# Test artifacts
test-results/
playwright-report/
e2e/screenshots/
e2e/videos/

# Misc
.history/
.ionide/
*.seed
*.pid
*.pid.lock

# Claude AI
.claude/
.claude_code/

# Redis data
dump.rdb

# SQLite databases
app/backend/database/*.db
app/backend/database/*.sqlite
app/backend/database/*.sqlite3

# Agent memory stores
app/agents/memory/
app/agents/chroma/
app/agents/neo4j/

# Certificates (keep example files)
*.crt
*.key
*.pem
!*.example.crt
!*.example.key
!*.example.pem