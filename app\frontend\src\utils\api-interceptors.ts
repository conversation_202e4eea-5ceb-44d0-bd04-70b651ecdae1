import axios, { AxiosError, AxiosInstance, AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import { errorService } from '../services/errorService';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from './error-handler';
import { requestThrottler } from './request-throttler';

interface RetryConfig {
  retries: number;
  retryDelay: number;
  retryCondition?: (error: AxiosError) => boolean;
}

interface RequestQueueItem {
  config: InternalAxiosRequestConfig;
  resolve: (value: AxiosResponse) => void;
  reject: (error: any) => void;
}

export class ApiInterceptors {
  private static correlationId?: string;
  private static requestQueue: RequestQueueItem[] = [];
  private static isRefreshing = false;

  /**
   * Setup request interceptors with throttling
   */
  static setupRequestInterceptors(axiosInstance: AxiosInstance): void {
    // Add request interceptor with throttling
    axiosInstance.interceptors.request.use(
      async (config: InternalAxiosRequestConfig) => {
        // Ensure config and headers exist
        if (!config.headers) {
          config.headers = {} as any;
        }
        
        // Generate or use existing correlation ID
        if (!this.correlationId) {
          this.correlationId = errorService.generateCorrelationId();
        }
        
        config.headers['X-Correlation-ID'] = this.correlationId;
        
        // Add request timestamp
        (config as any).metadata = {
          startTime: Date.now(),
          correlationId: this.correlationId,
        };

        // Add auth token if available
        const token = this.getAuthToken();
        if (token) {
          config.headers['Authorization'] = `Bearer ${token}`;
        } else {
          // Check if Authorization header was already set by setAuthToken
          const existingAuth = config.headers['Authorization'];
          if (!existingAuth) {
            console.warn('[API Interceptor] No auth token found for request:', config.url);
          }
        }

        // Apply throttling for non-auth requests
        if (!config.url?.includes('/auth/refresh')) {
          // Determine request priority based on endpoint
          let priority = 0;
          if (config.url?.includes('/auth/')) {
            priority = 10; // High priority for auth
          } else if (config.url?.includes('/calculations/')) {
            priority = -5; // Lower priority for calculations
          } else if (config.url?.includes('/exports/')) {
            priority = -10; // Lowest priority for exports
          }

          // Wait for throttling before proceeding
          await requestThrottler.waitForSlot(priority);
        }

        return config;
      },
      (error) => {
        errorService.logError(error, {
          phase: 'request-interceptor',
          correlationId: this.correlationId,
        });
        return Promise.reject(error);
      }
    );
  }

  /**
   * Setup response interceptors
   */
  static setupResponseInterceptors(axiosInstance: AxiosInstance): void {
    axiosInstance.interceptors.response.use(
      (response) => {
        // Log response time with proper null checks
        const requestMetadata = response?.config ? (response.config as any).metadata : undefined;
        if (requestMetadata?.startTime) {
          const duration = Date.now() - requestMetadata.startTime;
          
          // Performance thresholds based on 2024 standards
          const PERFORMANCE_THRESHOLDS = {
            WARNING: 1000,   // Log warning after 1s
            ERROR: 5000,     // Log error after 5s
          };
          
          // Log performance issues based on severity
          if (duration > PERFORMANCE_THRESHOLDS.ERROR) {
            errorService.logError(`Critical performance issue: API request took ${duration}ms`, {
              url: response.config?.url,
              method: response.config?.method,
              duration,
              correlationId: requestMetadata.correlationId || this.correlationId,
              severity: 'critical',
            });
          } else if (duration > PERFORMANCE_THRESHOLDS.WARNING && 
                     // Only log warnings in development or for non-GET requests
                     (import.meta.env.DEV || response.config?.method !== 'GET')) {
            errorService.logInfo(`Slow API request detected: ${duration}ms`, {
              url: response.config?.url,
              method: response.config?.method,
              duration,
              correlationId: requestMetadata.correlationId || this.correlationId,
            });
          }
        }

        return response;
      },
      async (error: AxiosError) => {
        const originalRequest = error.config as InternalAxiosRequestConfig & { _retry?: boolean };

        // Handle 401 errors (token refresh)
        if (error.response?.status === 401 && originalRequest && !originalRequest._retry) {
          return this.handle401Error(error, axiosInstance);
        }

        // Handle rate limiting
        if (error.response?.status === 429) {
          return this.handleRateLimitError(error, axiosInstance);
        }

        // Handle network errors with retry
        if (!error.response && ErrorHandler.isNetworkError(error)) {
          return this.handleNetworkError(error, axiosInstance);
        }

        // Log error with proper null checks
        const requestMetadata = originalRequest ? (originalRequest as any).metadata : undefined;
        errorService.logError(error, {
          phase: 'response-interceptor',
          url: originalRequest?.url,
          method: originalRequest?.method,
          status: error.response?.status,
          correlationId: requestMetadata?.correlationId || this.correlationId,
        });

        return Promise.reject(error);
      }
    );
  }

  /**
   * Handle 401 unauthorized errors
   */
  private static async handle401Error(
    error: AxiosError,
    axiosInstance: AxiosInstance
  ): Promise<AxiosResponse> {
    const originalRequest = error.config as InternalAxiosRequestConfig & { _retry?: boolean };

    console.log('[API Interceptor] 401 Error Handler:', {
      url: originalRequest?.url,
      hasRetried: originalRequest?._retry,
      currentPath: window.location.pathname
    });
    
    // Special handling for /quotes/new route - don't immediately logout
    if (window.location.pathname === '/quotes/new' && !originalRequest?._retry) {
      console.log('[API Interceptor] Special handling for /quotes/new - attempting to recover');
      
      // Try to get fresh auth state
      const authState = localStorage.getItem('auth-storage');
      if (authState) {
        try {
          const parsed = JSON.parse(authState);
          const token = parsed.state?.accessToken;
          if (token && originalRequest) {
            console.log('[API Interceptor] Found token in storage, retrying request');
            originalRequest._retry = true;
            originalRequest.headers = originalRequest.headers || {} as any;
            originalRequest.headers['Authorization'] = `Bearer ${token}`;
            return axiosInstance(originalRequest);
          }
        } catch (e) {
          console.error('[API Interceptor] Failed to recover auth for /quotes/new:', e);
        }
      }
    }

    if (!originalRequest) {
      console.log('[API Interceptor] No original request, rejecting');
      return Promise.reject(error);
    }

    if (originalRequest._retry) {
      console.log('[API Interceptor] Already tried to refresh, logging out');
      // Already tried to refresh, logout user
      this.handleLogout();
      return Promise.reject(error);
    }

    originalRequest._retry = true;

    if (!this.isRefreshing) {
      this.isRefreshing = true;

      try {
        // Attempt to refresh token
        const newToken = await this.refreshAuthToken();
        
        if (newToken) {
          // Update token in storage
          this.setAuthToken(newToken);
          
          // Retry all queued requests
          this.processRequestQueue(newToken);
          
          // Retry original request
          originalRequest.headers = originalRequest.headers || {} as any;
          originalRequest.headers['Authorization'] = `Bearer ${newToken}`;
          return axiosInstance(originalRequest);
        }
      } catch (refreshError) {
        // Refresh failed, logout user
        this.handleLogout();
        this.processRequestQueue(null);
        return Promise.reject(refreshError);
      } finally {
        this.isRefreshing = false;
      }
    }

    // Add request to queue while refreshing
    return new Promise((resolve, reject) => {
      this.requestQueue.push({
        config: originalRequest,
        resolve,
        reject,
      });
    });
  }

  /**
   * Handle rate limit errors with exponential backoff
   */
  private static async handleRateLimitError(
    error: AxiosError,
    axiosInstance: AxiosInstance
  ): Promise<AxiosResponse> {
    const config = error.config as InternalAxiosRequestConfig & {
      _retryCount?: number;
      _rateLimitRetries?: number;
    };

    if (!config) {
      return Promise.reject(error);
    }

    // Initialize retry count for rate limits
    config._rateLimitRetries = config._rateLimitRetries || 0;
    const maxRetries = 3;

    if (config._rateLimitRetries >= maxRetries) {
      errorService.logError('Max rate limit retries exceeded', {
        url: config.url,
        retries: config._rateLimitRetries,
      });
      return Promise.reject(error);
    }

    config._rateLimitRetries++;

    // Get retry delay from header or calculate exponential backoff
    const retryAfter = ErrorHandler.getRetryAfter(error);
    const exponentialDelay = Math.min(
      1000 * Math.pow(2, config._rateLimitRetries - 1) + Math.random() * 1000,
      30000 // Max 30 seconds
    );
    const delay = retryAfter || exponentialDelay;

    errorService.logWarning('Rate limit hit, retrying with backoff', {
      url: config.url,
      attempt: config._rateLimitRetries,
      maxAttempts: maxRetries,
      delay,
      hasRetryAfter: !!retryAfter,
    });

    // Adjust throttler to be more conservative
    requestThrottler.adjustThrottle('decrease');

    // Wait for the calculated delay
    await new Promise(resolve => setTimeout(resolve, delay));
    
    // Retry the request directly
    return axiosInstance(config);
  }

  /**
   * Handle network errors with retry
   */
  private static async handleNetworkError(
    error: AxiosError,
    axiosInstance: AxiosInstance
  ): Promise<AxiosResponse> {
    const config = error.config as InternalAxiosRequestConfig & { 
      _retryCount?: number;
      _retryConfig?: RetryConfig;
    };

    if (!config) {
      return Promise.reject(error);
    }

    const retryConfig: RetryConfig = config._retryConfig || {
      retries: 3,
      retryDelay: 1000,
    };

    config._retryCount = config._retryCount || 0;

    if (config._retryCount < retryConfig.retries) {
      config._retryCount++;

      const delay = retryConfig.retryDelay * Math.pow(2, config._retryCount - 1);
      
      errorService.logWarning('Network error, retrying request', {
        url: config.url,
        attempt: config._retryCount,
        maxAttempts: retryConfig.retries,
        delay,
      });

      await new Promise(resolve => setTimeout(resolve, delay));
      
      return axiosInstance(config);
    }

    return Promise.reject(error);
  }

  /**
   * Process queued requests after token refresh
   */
  private static processRequestQueue(token: string | null): void {
    this.requestQueue.forEach(({ config, resolve, reject }) => {
      if (token) {
        config.headers = config.headers || {} as any;
        config.headers['Authorization'] = `Bearer ${token}`;
        // Use axios directly to avoid circular dependency
        axios(config).then(resolve).catch(reject);
      } else {
        reject(new Error('Token refresh failed'));
      }
    });

    this.requestQueue = [];
  }

  /**
   * Get auth token from storage
   */
  private static getAuthToken(): string | null {
    try {
      // Fallback to reading from localStorage directly
      // The auth token should already be set in headers by setAuthToken
      const authState = localStorage.getItem('auth-storage');
      if (authState) {
        const parsed = JSON.parse(authState);
        const token = parsed.state?.accessToken || null;
        if (token) {
          console.log('[API Interceptor] Found token in localStorage');
        }
        return token;
      }
    } catch (error) {
      console.error('[API Interceptor] Error getting auth token:', error);
    }
    return null;
  }

  /**
   * Set auth token in storage
   */
  private static setAuthToken(token: string): void {
    try {
      const authState = localStorage.getItem('auth-storage');
      if (authState) {
        const parsed = JSON.parse(authState);
        parsed.state.accessToken = token;
        localStorage.setItem('auth-storage', JSON.stringify(parsed));
      }
    } catch {
      // Ignore storage errors
    }
  }

  /**
   * Refresh authentication token
   */
  private static async refreshAuthToken(): Promise<string | null> {
    try {
      const refreshToken = this.getRefreshToken();
      if (!refreshToken) {
        return null;
      }
      
      // Use the same base URL logic as the main client
      const refreshBaseURL = import.meta.env.DEV 
        ? '/api'
        : (import.meta.env.VITE_API_URL || '/api');
      
      const response = await axios.post(`${refreshBaseURL}/auth/refresh`, {
        refreshToken,
      });
      
      return response.data.accessToken;
    } catch (error) {
      errorService.logError(error, {
        context: 'token-refresh',
      });
      return null;
    }
  }

  /**
   * Get refresh token from storage
   */
  private static getRefreshToken(): string | null {
    try {
      const authState = localStorage.getItem('auth-storage');
      if (authState) {
        const parsed = JSON.parse(authState);
        return parsed.state?.refreshToken || null;
      }
    } catch {
      // Ignore parsing errors
    }
    return null;
  }

  /**
   * Handle logout
   */
  private static handleLogout(): void {
    console.log('[API Interceptor] handleLogout called - checking current route');
    const currentPath = window.location.pathname;
    console.log('[API Interceptor] Current path:', currentPath);
    
    // Use dynamic import to avoid circular dependency
    import('../stores/auth').then(({ useAuthStore }) => {
      console.log('[API Interceptor] About to call logout from auth store');
      useAuthStore.getState().logout();
      
      // Only redirect if we're not already on the login page
      if (currentPath !== '/login') {
        console.log('[API Interceptor] Redirecting to login after logout');
        window.location.href = '/login';
      }
    });
  }

  /**
   * Create axios instance with interceptors
   */
  static createAxiosInstance(baseURL: string): AxiosInstance {
    const instance = axios.create({
      baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupRequestInterceptors(instance);
    this.setupResponseInterceptors(instance);

    return instance;
  }

  /**
   * Reset correlation ID (call this on route changes)
   */
  static resetCorrelationId(): void {
    this.correlationId = errorService.generateCorrelationId();
  }
}

// Export configured axios instance
// Use relative path to leverage Vite proxy in development
const baseURL = import.meta.env.DEV 
  ? '/api'  // This will be proxied by Vite to http://localhost:3001
  : (import.meta.env.VITE_API_URL || '/api');

export const apiClient = ApiInterceptors.createAxiosInstance(baseURL);