// Debug script to check what's being rendered
import React, { useEffect } from 'react';

export function DebugApp() {
  useEffect(() => {
    console.log('[DEBUG] DebugApp component mounted');
    console.log('[DEBUG] Window location:', window.location.href);
    console.log('[DEBUG] Document title:', document.title);
    console.log('[DEBUG] Root element:', document.getElementById('root'));
    
    // Check if there's any script modifying the DOM
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        console.log('[DEBUG] DOM mutation:', mutation);
      });
    });
    
    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true
    });
    
    // Check for service workers
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.getRegistrations().then((registrations) => {
        console.log('[DEBUG] Service worker registrations:', registrations);
      });
    }
    
    // Check localStorage/sessionStorage
    console.log('[DEBUG] localStorage:', { ...localStorage });
    console.log('[DEBUG] sessionStorage:', { ...sessionStorage });
    
    return () => observer.disconnect();
  }, []);
  
  return (
    <div style={{ padding: '20px', fontFamily: 'monospace' }}>
      <h1>Debug App</h1>
      <p>Check console for debug information</p>
      <button onClick={() => window.location.reload()}>Reload</button>
    </div>
  );
}