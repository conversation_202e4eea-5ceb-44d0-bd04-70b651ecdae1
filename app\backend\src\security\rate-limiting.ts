import { Request, Response, NextFunction } from 'express';
import { RateLimiterRedis, RateLimiterRes, RateLimiterMemory } from 'rate-limiter-flexible';
import Redis from 'ioredis';
import { AppError } from '../middleware/errorHandler';
import { createAuditLog, AUDIT_ACTIONS } from './audit';
import { redisManager } from '../services/redis-manager';

// Rate limiting state
let rateLimitersInitialized = false;
let useMemoryFallback = false;

// Initialize rate limiters when Redis manager is ready
function initializeRateLimiters() {
  const redis = redisManager.getClient();
  
  if (!redis) {
    console.warn('Redis not available for rate limiting - using memory fallback');
    useMemoryFallback = true;
    initializeMemoryRateLimiters();
    return;
  }
  
  try {
    initializeRedisRateLimiters(redis);
    rateLimitersInitialized = true;
    useMemoryFallback = false;
  } catch (error) {
    console.warn('Failed to initialize Redis rate limiters:', (error as Error).message);
    useMemoryFallback = true;
    initializeMemoryRateLimiters();
  }
}

// Listen for Redis connection changes
redisManager.on('connected', () => {
  console.log('Redis connected - reinitializing rate limiters');
  initializeRateLimiters();
});

redisManager.on('disconnected', () => {
  console.log('Redis disconnected - switching to memory rate limiters');
  useMemoryFallback = true;
  initializeMemoryRateLimiters();
});

// Rate limiter configurations
let rateLimiters: any = {};

// Initialize Redis-based rate limiters
function initializeRedisRateLimiters(redis: Redis) {
  rateLimiters = {
    // General API rate limit
    api: new RateLimiterRedis({
      storeClient: redis,
      keyPrefix: 'rl:api',
      points: 100, // requests
      duration: 900, // per 15 minutes
      blockDuration: 900, // block for 15 minutes
    }),
    
    // Authentication endpoints
    auth: new RateLimiterRedis({
      storeClient: redis,
      keyPrefix: 'rl:auth',
      points: 5,
      duration: 900, // per 15 minutes
      blockDuration: 3600, // block for 1 hour
    }),
    
    // Password reset
    passwordReset: new RateLimiterRedis({
      storeClient: redis,
      keyPrefix: 'rl:password',
      points: 3,
      duration: 3600, // per hour
      blockDuration: 7200, // block for 2 hours
    }),
    
    // Data export
    export: new RateLimiterRedis({
      storeClient: redis,
      keyPrefix: 'rl:export',
      points: 10,
      duration: 3600, // per hour
      blockDuration: 3600,
    }),
    
    // Calculation endpoints
    calculation: new RateLimiterRedis({
      storeClient: redis,
      keyPrefix: 'rl:calc',
      points: 50,
      duration: 300, // per 5 minutes
      blockDuration: 300,
    }),
    
    // File upload
    upload: new RateLimiterRedis({
      storeClient: redis,
      keyPrefix: 'rl:upload',
      points: 20,
      duration: 3600, // per hour
      blockDuration: 3600,
    }),
    
    // API key usage
    apiKey: new RateLimiterRedis({
      storeClient: redis,
      keyPrefix: 'rl:apikey',
      points: 1000,
      duration: 3600, // per hour
      blockDuration: 3600,
    }),
  };
}

// Initialize memory-based rate limiters as fallback
function initializeMemoryRateLimiters() {
  // In development or when Redis is not available, use more lenient limits
  const isDev = process.env.NODE_ENV === 'development';
  const multiplier = isDev ? 10 : 1; // 10x multiplier for development
  
  rateLimiters = {
    // General API rate limit
    api: new RateLimiterMemory({
      keyPrefix: 'rl:api',
      points: 100 * multiplier, // requests
      duration: 900, // per 15 minutes
      blockDuration: isDev ? 60 : 900, // shorter blocks in dev
    }),
    
    // Authentication endpoints
    auth: new RateLimiterMemory({
      keyPrefix: 'rl:auth',
      points: 5 * multiplier,
      duration: 900, // per 15 minutes
      blockDuration: isDev ? 300 : 3600, // 5 min in dev, 1 hour in prod
    }),
    
    // Password reset
    passwordReset: new RateLimiterMemory({
      keyPrefix: 'rl:password',
      points: 3 * multiplier,
      duration: 3600, // per hour
      blockDuration: isDev ? 600 : 7200, // 10 min in dev, 2 hours in prod
    }),
    
    // Data export
    export: new RateLimiterMemory({
      keyPrefix: 'rl:export',
      points: 10 * multiplier,
      duration: 3600, // per hour
      blockDuration: isDev ? 300 : 3600,
    }),
    
    // Calculation endpoints
    calculation: new RateLimiterMemory({
      keyPrefix: 'rl:calc',
      points: 50 * multiplier,
      duration: 300, // per 5 minutes
      blockDuration: isDev ? 60 : 300,
    }),
    
    // File upload
    upload: new RateLimiterMemory({
      keyPrefix: 'rl:upload',
      points: 20 * multiplier,
      duration: 3600, // per hour
      blockDuration: isDev ? 300 : 3600,
    }),
    
    // API key usage
    apiKey: new RateLimiterMemory({
      keyPrefix: 'rl:apikey',
      points: 1000 * multiplier,
      duration: 3600, // per hour
      blockDuration: isDev ? 300 : 3600,
    }),
  };
  
  rateLimitersInitialized = true;
}

// Initialize rate limiters on startup
initializeRateLimiters();

// Progressive rate limiting (increases penalty for repeated violations)
const progressivePenalty = new Map<string, number>();

// Get client identifier
function getClientId(req: Request): string {
  // Priority: authenticated user > API key > IP address
  if ((req as any).user?.userId) {
    return `user:${(req as any).user.userId}`;
  }
  
  if ((req as any).apiKey?.id) {
    return `apikey:${(req as any).apiKey.id}`;
  }
  
  // Get IP address
  const forwarded = req.headers['x-forwarded-for'];
  const ip = forwarded 
    ? (forwarded as string).split(',')[0].trim()
    : req.socket.remoteAddress || 'unknown';
  
  return `ip:${ip}`;
}

// Create rate limiter middleware
export function createRateLimiter(
  limiterName: string,
  options?: {
    skipSuccessfulRequests?: boolean;
    skipFailedRequests?: boolean;
    customKeyGenerator?: (req: Request) => string;
    customErrorMessage?: string;
    progressivePenalty?: boolean;
  }
) {
  return async (req: Request, res: Response, next: NextFunction) => {
    // Skip rate limiting if not initialized
    if (!rateLimitersInitialized || !rateLimiters[limiterName]) {
      if (process.env.NODE_ENV === 'development') {
        // In development, silently skip if not initialized
        return next();
      } else {
        // In production, log a warning
        console.warn(`Rate limiting skipped for ${limiterName} - not initialized`);
        return next();
      }
    }
    
    const limiter = rateLimiters[limiterName];
    const key = options?.customKeyGenerator 
      ? options.customKeyGenerator(req)
      : getClientId(req);
    
    try {
      await limiter.consume(key);
      
      // Reset progressive penalty on successful request
      if (options?.progressivePenalty) {
        progressivePenalty.delete(key);
      }
      
      // Add rate limit headers
      const rateLimiterRes = await limiter.get(key);
      if (rateLimiterRes) {
        res.setHeader('X-RateLimit-Limit', limiter.points);
        res.setHeader('X-RateLimit-Remaining', rateLimiterRes.remainingPoints);
        res.setHeader('X-RateLimit-Reset', 
          new Date(Date.now() + rateLimiterRes.msBeforeNext).toISOString()
        );
      }
      
      next();
    } catch (rateLimiterRes) {
      // Handle rate limit exceeded
      if (rateLimiterRes instanceof RateLimiterRes) {
        // Apply progressive penalty
        if (options?.progressivePenalty) {
          const penaltyCount = (progressivePenalty.get(key) || 0) + 1;
          progressivePenalty.set(key, penaltyCount);
          
          // Double the block duration for each violation
          const additionalBlockDuration = limiter.blockDuration * Math.pow(2, penaltyCount - 1);
          await limiter.block(key, additionalBlockDuration);
        }
        
        // Log rate limit violation
        await createAuditLog({
          action: AUDIT_ACTIONS.RATE_LIMIT_EXCEEDED,
          userId: (req as any).user?.userId,
          resourceType: 'rate_limit',
          details: {
            limiter: limiterName,
            key,
            points: rateLimiterRes.points,
            consumedPoints: rateLimiterRes.consumedPoints,
          }
        });
        
        // Set retry headers
        res.setHeader('Retry-After', Math.ceil(rateLimiterRes.msBeforeNext / 1000));
        res.setHeader('X-RateLimit-Limit', limiter.points);
        res.setHeader('X-RateLimit-Remaining', rateLimiterRes.remainingPoints);
        res.setHeader('X-RateLimit-Reset', 
          new Date(Date.now() + rateLimiterRes.msBeforeNext).toISOString()
        );
        
        const message = options?.customErrorMessage || 
          `Too many requests. Please retry after ${Math.ceil(rateLimiterRes.msBeforeNext / 1000)} seconds.`;
        
        next(new AppError(429, message, true, 'RATE_LIMIT_EXCEEDED'));
      } else {
        // If rate limiter fails, log warning and continue
        console.warn('Rate limiter error:', rateLimiterRes);
        next();
      }
    }
  };
}

// Middleware for different endpoints
export const rateLimitMiddleware = {
  api: createRateLimiter('api'),
  
  auth: createRateLimiter('auth', {
    progressivePenalty: true,
    customErrorMessage: 'Too many authentication attempts. Account temporarily locked.'
  }),
  
  passwordReset: createRateLimiter('passwordReset', {
    progressivePenalty: true,
    customErrorMessage: 'Too many password reset attempts. Please try again later.'
  }),
  
  export: createRateLimiter('export', {
    customErrorMessage: 'Export rate limit exceeded. Please wait before exporting more data.'
  }),
  
  calculation: createRateLimiter('calculation'),
  
  upload: createRateLimiter('upload', {
    customErrorMessage: 'Upload rate limit exceeded. Please wait before uploading more files.'
  }),
  
  apiKey: createRateLimiter('apiKey'),
};

// Dynamic rate limiting based on user tier
export function tierBasedRateLimit(req: Request & { user?: any }) {
  return async (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return rateLimitMiddleware.api(req, res, next);
    }
    
    // Get user tier (you would fetch this from database)
    const userTier = req.user.tier || 'basic';
    
    // Create tier-specific rate limiter
    const tierLimits = {
      basic: { points: 100, duration: 900 },
      pro: { points: 500, duration: 900 },
      enterprise: { points: 2000, duration: 900 },
    };
    
    const limits = tierLimits[userTier as keyof typeof tierLimits] || tierLimits.basic;
    const redis = redisManager.getClient();
    
    // Create appropriate rate limiter based on Redis availability
    const tierLimiter = redis
      ? new RateLimiterRedis({
          storeClient: redis,
          keyPrefix: `rl:tier:${userTier}`,
          points: limits.points,
          duration: limits.duration,
        })
      : new RateLimiterMemory({
          keyPrefix: `rl:tier:${userTier}`,
          points: limits.points * (process.env.NODE_ENV === 'development' ? 10 : 1),
          duration: limits.duration,
        });
    
    try {
      await tierLimiter.consume(`user:${req.user.userId}`);
      next();
    } catch (rateLimiterRes) {
      if (rateLimiterRes instanceof RateLimiterRes) {
        res.setHeader('Retry-After', Math.ceil(rateLimiterRes.msBeforeNext / 1000));
        next(new AppError(429, 
          `Rate limit exceeded for ${userTier} tier. Upgrade for higher limits.`, 
          true, 
          'TIER_RATE_LIMIT_EXCEEDED'
        ));
      } else {
        next(new AppError(500, 'Rate limiter error', true, 'RATE_LIMITER_ERROR'));
      }
    }
  };
}

// Distributed rate limiting for multi-instance deployments
export class DistributedRateLimiter {
  private script: string;
  
  constructor() {
    // Lua script for atomic rate limit check and increment
    this.script = `
      local key = KEYS[1]
      local limit = tonumber(ARGV[1])
      local window = tonumber(ARGV[2])
      local current_time = tonumber(ARGV[3])
      
      local current = redis.call('get', key)
      if current == false then
        redis.call('setex', key, window, 1)
        return {1, limit - 1}
      end
      
      current = tonumber(current)
      if current >= limit then
        local ttl = redis.call('ttl', key)
        return {-1, ttl}
      end
      
      local new_value = redis.call('incr', key)
      return {new_value, limit - new_value}
    `;
  }
  
  async checkLimit(
    key: string, 
    limit: number, 
    window: number
  ): Promise<{ allowed: boolean; remaining: number; resetIn?: number }> {
    const redis = redisManager.getClient();
    
    if (!redis) {
      // Allow all requests if Redis is not available in development
      if (process.env.NODE_ENV === 'development') {
        return { allowed: true, remaining: limit };
      }
      // In production, use a simple in-memory counter as fallback
      return this.memoryFallback(key, limit, window);
    }
    
    const result = await redis.eval(
      this.script,
      1,
      key,
      limit.toString(),
      window.toString(),
      Date.now().toString()
    ) as [number, number];
    
    if (result[0] === -1) {
      return { allowed: false, remaining: 0, resetIn: result[1] };
    }
    
    return { allowed: true, remaining: result[1] };
  }
  
  // Simple memory-based fallback for production when Redis is down
  private memoryStore = new Map<string, { count: number; resetAt: number }>();
  
  private memoryFallback(
    key: string,
    limit: number,
    window: number
  ): { allowed: boolean; remaining: number; resetIn?: number } {
    const now = Date.now();
    const windowMs = window * 1000;
    
    const entry = this.memoryStore.get(key);
    
    if (!entry || entry.resetAt < now) {
      // Start new window
      this.memoryStore.set(key, { count: 1, resetAt: now + windowMs });
      return { allowed: true, remaining: limit - 1 };
    }
    
    if (entry.count >= limit) {
      return { 
        allowed: false, 
        remaining: 0, 
        resetIn: Math.ceil((entry.resetAt - now) / 1000) 
      };
    }
    
    entry.count++;
    return { allowed: true, remaining: limit - entry.count };
  }
}