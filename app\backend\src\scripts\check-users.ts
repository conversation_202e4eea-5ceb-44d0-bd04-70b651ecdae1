import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  const users = await prisma.user.findMany({
    select: {
      email: true,
      name: true,
      role: true,
      company_id: true
    }
  });
  
  console.log('Users in database:');
  users.forEach(user => {
    console.log(`- Email: ${user.email}, Name: ${user.name}, Role: ${user.role}, Company: ${user.company_id}`);
  });
  
  console.log('\n\nDefault passwords for seeded users:');
  console.log('- <EMAIL>: Admin123!');
  console.log('- <EMAIL>: Electric123!');
  console.log('- <EMAIL>: Estimate123!');
  console.log('- <EMAIL>: Test123! (if created)');
}

main()
  .catch(e => console.error(e))
  .finally(() => prisma.$disconnect());