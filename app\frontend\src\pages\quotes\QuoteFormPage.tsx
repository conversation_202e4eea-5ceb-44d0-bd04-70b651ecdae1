import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'react-hot-toast';
import { api, setAuthToken } from '../../services/api';
import { ArrowLeftIcon, PlusIcon, TrashIcon } from '@heroicons/react/24/outline';
import { useRouteDebug } from '../../hooks/useRouteDebug';
import { useAuthStore } from '../../stores/auth';
import { RedirectDebugger } from '../../utils/debug-redirect';

// Form validation schema for quote items
const quoteItemSchema = z.object({
  name: z.string().min(1, 'Item name is required'),
  description: z.string().optional(),
  category: z.enum(['material', 'labor', 'equipment', 'other']).default('material'),
  quantity: z.number().positive('Quantity must be positive'),
  unit: z.string().min(1, 'Unit is required'),
  unitPrice: z.number().min(0, 'Price must be non-negative').optional(),
  totalPrice: z.number().optional(),
  notes: z.string().optional(),
  isOptional: z.boolean().default(false),
});

// Form validation schema for quote
const quoteFormSchema = z.object({
  name: z.string().min(1, 'Quote name is required').max(255),
  customer_id: z.string().optional(),
  project_id: z.string().optional(),
  projectOverview: z.string().optional(),
  scopeOfWork: z.string().optional(),
  materialsIncluded: z.string().optional(),
  exclusions: z.string().optional(),
  termsAndConditions: z.string().optional(),
  taxRate: z.number().min(0).max(1).default(0.08),
  discount: z.number().min(0).default(0),
  discountType: z.enum(['PERCENTAGE', 'FIXED']).default('PERCENTAGE'),
  expiresAt: z.string().optional(),
  customerNotes: z.string().optional(),
  internalNotes: z.string().optional(),
});

type QuoteFormData = z.infer<typeof quoteFormSchema>;
type QuoteItem = z.infer<typeof quoteItemSchema>;

export const QuoteFormPage: React.FC = () => {
  console.log('[QuoteFormPage] ⭐ Component function called!');
  console.log('[QuoteFormPage] Component rendering start');
  RedirectDebugger.log('QuoteFormPage render', { timestamp: new Date().toISOString() });
  
  const navigate = useNavigate();
  const { id } = useParams();
  const isEdit = !!id;
  
  console.log('[QuoteFormPage] Initial render state:', { 
    id, 
    isEdit, 
    pathname: window.location.pathname,
    timestamp: new Date().toISOString()
  });
  
  RedirectDebugger.log('QuoteFormPage initial state', {
    id,
    isEdit,
    pathname: window.location.pathname
  });
  
  // Debug route access
  const routeDebug = useRouteDebug();
  console.log('[QuoteFormPage] Route debug info:', routeDebug);
  
  const [loading, setLoading] = useState(false);
  const [customers, setCustomers] = useState<any[]>([]);
  const [projects, setProjects] = useState<any[]>([]);
  const [items, setItems] = useState<QuoteItem[]>([]);
  const [subtotal, setSubtotal] = useState(0);
  const [taxAmount, setTaxAmount] = useState(0);
  const [discountAmount, setDiscountAmount] = useState(0);
  const [total, setTotal] = useState(0);
  const [errorOccurred, setErrorOccurred] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    reset,
    watch,
  } = useForm<QuoteFormData>({
    resolver: zodResolver(quoteFormSchema),
    defaultValues: {
      taxRate: 0.08,
      discount: 0,
      discountType: 'PERCENTAGE',
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    },
  });

  const taxRate = watch('taxRate');
  const discount = watch('discount');
  const discountType = watch('discountType');
  const selectedCustomerId = watch('customer_id');

  useEffect(() => {
    console.log('[QuoteFormPage] Main useEffect triggered', { 
      id, 
      isEdit,
      errorOccurred,
      timestamp: new Date().toISOString()
    });
    
    // Add error boundary and delay to ensure auth is stable
    const loadData = async () => {
      try {
        // Ensure auth token is set
        const authState = useAuthStore.getState();
        if (authState.accessToken) {
          setAuthToken(authState.accessToken);
          console.log('[QuoteFormPage] Auth token re-set before API calls');
        }
        
        // Add a small delay to ensure everything is stable
        await new Promise(resolve => setTimeout(resolve, 100));
        
        console.log('[QuoteFormPage] About to load customers');
        await loadCustomers();
        
        if (isEdit) {
          console.log('[QuoteFormPage] Edit mode - loading quote');
          await loadQuote();
        }
      } catch (error) {
        console.error('[QuoteFormPage] Error in useEffect:', error);
        setErrorOccurred(true);
      }
    };
    
    loadData();
  }, [id]);

  useEffect(() => {
    if (selectedCustomerId) {
      loadProjects(selectedCustomerId);
    } else {
      setProjects([]);
    }
  }, [selectedCustomerId]);

  useEffect(() => {
    calculateTotals();
  }, [items, taxRate, discount, discountType]);

  const loadCustomers = async () => {
    try {
      console.log('[QuoteFormPage] Starting loadCustomers API call');
      console.log('[QuoteFormPage] Current auth state:', {
        hasToken: !!api.defaults.headers.common['Authorization'],
        authHeader: api.defaults.headers.common['Authorization']?.substring(0, 20)
      });
      
      const response = await api.get('/customers');
      
      console.log('[QuoteFormPage] Customers API response:', {
        status: response.status,
        hasData: !!response.data,
        dataLength: response.data?.data?.length || 0
      });
      
      setCustomers(response.data?.data || []);
      console.log('[QuoteFormPage] Customers state updated successfully');
    } catch (error: any) {
      console.error('[QuoteFormPage] Failed to load customers:', {
        error,
        message: error.message,
        response: error.response,
        status: error.response?.status,
        data: error.response?.data
      });
      
      // Check if this is an auth error that might trigger a redirect
      if (error.response?.status === 401) {
        console.error('[QuoteFormPage] 401 error - this might trigger auth redirect');
        // Don't set error state for 401, let the auth interceptor handle it
        return;
      }
      
      toast.error('Failed to load customers');
      setErrorOccurred(true);
    }
  };

  const loadProjects = async (customerId: string) => {
    try {
      const response = await api.get(`/projects?customerId=${customerId}`);
      setProjects(response.data?.data || []);
    } catch (error) {
      console.error('Failed to load projects:', error);
    }
  };

  const loadQuote = async () => {
    if (!id) return;
    try {
      setLoading(true);
      const response = await api.get(`/quotes/${id}`);
      const quote = response.data;
      
      reset({
        name: quote.name,
        customer_id: quote.customer_id,
        project_id: quote.project_id,
        projectOverview: quote.project_overview,
        scopeOfWork: quote.scope_of_work,
        materialsIncluded: quote.materials_included,
        exclusions: quote.exclusions,
        termsAndConditions: quote.terms_and_conditions,
        taxRate: quote.tax_rate,
        discount: quote.discount,
        discountType: quote.discount_type,
        expiresAt: quote.expires_at ? new Date(quote.expires_at).toISOString().split('T')[0] : undefined,
        customerNotes: quote.customer_notes,
        internalNotes: quote.internal_notes,
      });
      
      setItems(quote.items || []);
    } catch (error) {
      toast.error('Failed to load quote');
      navigate('/quotes');
    } finally {
      setLoading(false);
    }
  };

  const calculateTotals = () => {
    const sub = items.reduce((sum, item) => sum + (item.totalPrice || item.quantity * (item.unitPrice || 0)), 0);
    setSubtotal(sub);
    
    const tax = sub * taxRate;
    setTaxAmount(tax);
    
    let disc = 0;
    if (discountType === 'PERCENTAGE') {
      disc = sub * (discount / 100);
    } else {
      disc = discount;
    }
    setDiscountAmount(disc);
    
    setTotal(sub + tax - disc);
  };

  const addItem = () => {
    setItems([...items, {
      name: '',
      description: '',
      category: 'material',
      quantity: 1,
      unit: 'EA',
      unitPrice: 0,
      totalPrice: 0,
      notes: '',
      isOptional: false,
    }]);
  };

  const updateItem = (index: number, field: keyof QuoteItem, value: any) => {
    const newItems = [...items];
    newItems[index] = { ...newItems[index], [field]: value };
    
    // Recalculate total price when quantity or unit price changes
    if (field === 'quantity' || field === 'unitPrice') {
      newItems[index].totalPrice = newItems[index].quantity * (newItems[index].unitPrice || 0);
    }
    
    setItems(newItems);
  };

  const removeItem = (index: number) => {
    setItems(items.filter((_, i) => i !== index));
  };

  const onSubmit = async (data: QuoteFormData) => {
    if (items.length === 0) {
      toast.error('Please add at least one item to the quote');
      return;
    }

    try {
      setLoading(true);
      
      const quoteData = {
        ...data,
        items,
        taxRate: Number(data.taxRate),
        discount: Number(data.discount),
      };

      if (isEdit) {
        await api.put(`/quotes/${id}`, quoteData);
        toast.success('Quote updated successfully');
      } else {
        await api.post('/quotes', quoteData);
        toast.success('Quote created successfully');
      }
      
      navigate('/quotes');
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Failed to save quote');
    } finally {
      setLoading(false);
    }
  };

  // Add early return check
  console.log('[QuoteFormPage] About to render component', {
    errorOccurred,
    loading,
    customersLength: customers.length,
    pathname: window.location.pathname
  });
  
  if (errorOccurred) {
    console.log('[QuoteFormPage] Error occurred, showing error state');
    return (
      <div className="px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <h3 className="text-red-800 font-medium">Error Loading Quote Form</h3>
          <p className="text-red-600 mt-2">There was an error loading the quote form. Please try refreshing the page.</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Refresh Page
          </button>
        </div>
      </div>
    );
  }
  
  return (
    <div className="px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <button
          onClick={() => navigate('/quotes')}
          className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
        >
          <ArrowLeftIcon className="h-4 w-4 mr-1" />
          Back to Quotes
        </button>
        <h1 className="mt-2 text-2xl font-bold text-gray-900 dark:text-white">
          {isEdit ? 'Edit Quote' : 'Create New Quote'}
        </h1>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Basic Information</h2>
          
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Quote Name
              </label>
              <input
                type="text"
                {...register('name')}
                className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:ring-blue-500 focus:border-blue-500"
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Customer
              </label>
              <select
                {...register('customer_id')}
                className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select a customer</option>
                {customers.map((customer) => (
                  <option key={customer.id} value={customer.id}>
                    {customer.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Project
              </label>
              <select
                {...register('project_id')}
                disabled={!selectedCustomerId}
                className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"
              >
                <option value="">Select a project</option>
                {projects.map((project) => (
                  <option key={project.id} value={project.id}>
                    {project.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Expires On
              </label>
              <input
                type="date"
                {...register('expiresAt')}
                className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Project Details</h2>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Project Overview
              </label>
              <textarea
                {...register('projectOverview')}
                rows={3}
                className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Scope of Work
              </label>
              <textarea
                {...register('scopeOfWork')}
                rows={3}
                className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Materials Included
              </label>
              <textarea
                {...register('materialsIncluded')}
                rows={2}
                className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Exclusions
              </label>
              <textarea
                {...register('exclusions')}
                rows={2}
                className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white">Line Items</h2>
            <button
              type="button"
              onClick={addItem}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <PlusIcon className="h-4 w-4 mr-1" />
              Add Item
            </button>
          </div>

          {items.length === 0 ? (
            <p className="text-center text-gray-500 dark:text-gray-400 py-8">
              No items added yet. Click "Add Item" to get started.
            </p>
          ) : (
            <div className="space-y-4">
              {items.map((item, index) => (
                <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-6">
                    <div className="sm:col-span-2">
                      <input
                        type="text"
                        placeholder="Item name"
                        value={item.name}
                        onChange={(e) => updateItem(index, 'name', e.target.value)}
                        className="block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    
                    <div>
                      <select
                        value={item.category}
                        onChange={(e) => updateItem(index, 'category', e.target.value)}
                        className="block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="material">Material</option>
                        <option value="labor">Labor</option>
                        <option value="equipment">Equipment</option>
                        <option value="other">Other</option>
                      </select>
                    </div>
                    
                    <div>
                      <input
                        type="number"
                        placeholder="Quantity"
                        value={item.quantity}
                        onChange={(e) => updateItem(index, 'quantity', Number(e.target.value))}
                        className="block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    
                    <div>
                      <input
                        type="text"
                        placeholder="Unit"
                        value={item.unit}
                        onChange={(e) => updateItem(index, 'unit', e.target.value)}
                        className="block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    
                    <div>
                      <input
                        type="number"
                        placeholder="Unit Price"
                        value={item.unitPrice || ''}
                        onChange={(e) => updateItem(index, 'unitPrice', Number(e.target.value))}
                        className="block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                  </div>
                  
                  <div className="mt-2 grid grid-cols-1 gap-4 sm:grid-cols-6">
                    <div className="sm:col-span-4">
                      <input
                        type="text"
                        placeholder="Description (optional)"
                        value={item.description || ''}
                        onChange={(e) => updateItem(index, 'description', e.target.value)}
                        className="block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    
                    <div className="sm:col-span-1">
                      <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mt-2">
                        Total: ${(item.totalPrice || 0).toFixed(2)}
                      </p>
                    </div>
                    
                    <div className="sm:col-span-1">
                      <button
                        type="button"
                        onClick={() => removeItem(index)}
                        className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-red-600 hover:text-red-700 focus:outline-none"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Pricing & Totals</h2>
          
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Tax Rate (%)
              </label>
              <input
                type="number"
                step="0.01"
                {...register('taxRate', { valueAsNumber: true })}
                className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Discount
              </label>
              <input
                type="number"
                step="0.01"
                {...register('discount', { valueAsNumber: true })}
                className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Discount Type
              </label>
              <select
                {...register('discountType')}
                className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="PERCENTAGE">Percentage</option>
                <option value="FIXED">Fixed Amount</option>
              </select>
            </div>
          </div>

          <div className="mt-6 border-t border-gray-200 dark:border-gray-700 pt-4">
            <dl className="space-y-2">
              <div className="flex justify-between text-sm">
                <dt className="text-gray-600 dark:text-gray-400">Subtotal</dt>
                <dd className="text-gray-900 dark:text-white">${subtotal.toFixed(2)}</dd>
              </div>
              <div className="flex justify-between text-sm">
                <dt className="text-gray-600 dark:text-gray-400">Tax</dt>
                <dd className="text-gray-900 dark:text-white">${taxAmount.toFixed(2)}</dd>
              </div>
              <div className="flex justify-between text-sm">
                <dt className="text-gray-600 dark:text-gray-400">Discount</dt>
                <dd className="text-gray-900 dark:text-white">-${discountAmount.toFixed(2)}</dd>
              </div>
              <div className="flex justify-between text-lg font-medium">
                <dt className="text-gray-900 dark:text-white">Total</dt>
                <dd className="text-gray-900 dark:text-white">${total.toFixed(2)}</dd>
              </div>
            </dl>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Additional Information</h2>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Terms and Conditions
              </label>
              <textarea
                {...register('termsAndConditions')}
                rows={3}
                className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Customer Notes
              </label>
              <textarea
                {...register('customerNotes')}
                rows={2}
                className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:ring-blue-500 focus:border-blue-500"
                placeholder="Notes visible to the customer"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Internal Notes
              </label>
              <textarea
                {...register('internalNotes')}
                rows={2}
                className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm focus:ring-blue-500 focus:border-blue-500"
                placeholder="Private notes for internal use only"
              />
            </div>
          </div>
        </div>

        <div className="flex justify-end space-x-4">
          <button
            type="button"
            onClick={() => navigate('/quotes')}
            className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {loading ? 'Saving...' : (isEdit ? 'Update Quote' : 'Create Quote')}
          </button>
        </div>
      </form>
    </div>
  );
};