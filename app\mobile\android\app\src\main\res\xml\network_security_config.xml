<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <!-- Development configuration -->
    <debug-overrides>
        <trust-anchors>
            <!-- Allow user-installed certificates for debugging -->
            <certificates src="user" />
            <certificates src="system" />
        </trust-anchors>
    </debug-overrides>
    
    <!-- Default configuration for all domains -->
    <!-- For development, we allow cleartext traffic -->
    <base-config cleartextTrafficPermitted="true">
        <trust-anchors>
            <certificates src="system" />
            <certificates src="user" />
        </trust-anchors>
    </base-config>
    
    <!-- Domain specific configurations can be added here later -->
    <!-- Example for production:
    <domain-config cleartextTrafficPermitted="false">
        <domain includeSubdomains="true">api.electrical-contractor.com</domain>
        <trust-anchors>
            <certificates src="system" />
        </trust-anchors>
    </domain-config>
    -->
</network-security-config>