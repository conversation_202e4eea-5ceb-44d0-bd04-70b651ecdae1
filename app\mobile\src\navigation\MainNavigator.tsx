import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Icon } from 'native-base';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import { MainTabParamList } from '@types/navigation';

import DashboardScreen from '@screens/dashboard/DashboardScreen';
import ProjectNavigator from './ProjectNavigator';
import CalculationsScreen from '@screens/calculators/CalculatorsScreen';
import SettingsScreen from '@screens/settings/SettingsScreen';

const Tab = createBottomTabNavigator<MainTabParamList>();

const MainNavigator: React.FC = () => {
  return (
    <Tab.Navigator
      screenOptions={{
        headerShown: false,
        tabBarActiveTintColor: '#2196f3',
        tabBarInactiveTintColor: '#757575',
        tabBarStyle: {
          height: 60,
          paddingBottom: 5,
          paddingTop: 5,
        },
        tabBarLabelStyle: {
          fontSize: 12,
        },
      }}
    >
      <Tab.Screen
        name="Dashboard"
        component={DashboardScreen}
        options={{
          tabBarIcon: ({ color, size }) => (
            <Icon as={MaterialIcons} name="dashboard" size={size} color={color} />
          ),
        }}
      />
      <Tab.Screen
        name="Projects"
        component={ProjectNavigator}
        options={{
          tabBarIcon: ({ color, size }) => (
            <Icon as={MaterialIcons} name="work" size={size} color={color} />
          ),
        }}
      />
      <Tab.Screen
        name="Calculations"
        component={CalculationsScreen}
        options={{
          tabBarIcon: ({ color, size }) => (
            <Icon as={MaterialIcons} name="calculate" size={size} color={color} />
          ),
        }}
      />
      <Tab.Screen
        name="Settings"
        component={SettingsScreen}
        options={{
          tabBarIcon: ({ color, size }) => (
            <Icon as={MaterialIcons} name="settings" size={size} color={color} />
          ),
        }}
      />
    </Tab.Navigator>
  );
};

export default MainNavigator;