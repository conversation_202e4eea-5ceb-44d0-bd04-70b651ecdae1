diff --git a/node_modules/react-native-sqlite-storage/platforms/android/build.gradle b/node_modules/react-native-sqlite-storage/platforms/android/build.gradle
index 1234567..abcdefg 100644
--- a/node_modules/react-native-sqlite-storage/platforms/android/build.gradle
+++ b/node_modules/react-native-sqlite-storage/platforms/android/build.gradle
@@ -1,14 +1,3 @@
-buildscript {
-    repositories {
-        google()
-        jcenter()
-    }
-
-    dependencies {
-        classpath 'com.android.tools.build:gradle:3.1.4'
-    }
-}
-
 apply plugin: 'com.android.library'
 
 def safeExtGet(prop, fallback) {
@@ -16,12 +5,12 @@ def safeExtGet(prop, fallback) {
 }
 
 android {
-    compileSdkVersion safeExtGet('compileSdkVersion', 23)
-    buildToolsVersion safeExtGet('buildToolsVersion', '27.0.3')
-
+    namespace "io.sqlc"
+    compileSdkVersion safeExtGet('compileSdkVersion', 33)
+    
     defaultConfig {
-        minSdkVersion safeExtGet('minSdkVersion', 16)
-        targetSdkVersion safeExtGet('targetSdkVersion', 22)
+        minSdkVersion safeExtGet('minSdkVersion', 21)
+        targetSdkVersion safeExtGet('targetSdkVersion', 33)
         versionCode 1
         versionName "1.0"
     }
@@ -33,11 +22,8 @@ android {
 repositories {
     mavenCentral()
     google()
-    maven {
-        url "https://maven.google.com"
-    }
 }
 
 dependencies {
     implementation 'com.facebook.react:react-native:+'
 }
diff --git a/node_modules/react-native-sqlite-storage/platforms/android/src/main/AndroidManifest.xml b/node_modules/react-native-sqlite-storage/platforms/android/src/main/AndroidManifest.xml
index 1234567..abcdefg 100644
--- a/node_modules/react-native-sqlite-storage/platforms/android/src/main/AndroidManifest.xml
+++ b/node_modules/react-native-sqlite-storage/platforms/android/src/main/AndroidManifest.xml
@@ -1,3 +1,3 @@
 <?xml version="1.0" encoding="utf-8"?>
-<manifest xmlns:android="http://schemas.android.com/apk/res/android" package="org.pgsqlite" >
+<manifest xmlns:android="http://schemas.android.com/apk/res/android" >
 </manifest>