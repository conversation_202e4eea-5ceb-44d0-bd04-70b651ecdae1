#!/bin/bash
# Claude Code Hook System Diagnostic Script

echo "=== Claude Code Hook System Diagnostic ==="
echo "Date: $(date)"
echo ""

# Check Claude version
echo "1. <PERSON> Code Version:"
claude --version 2>/dev/null || echo "   ERROR: <PERSON> command not found"
echo ""

# Check configuration file
echo "2. Configuration File:"
if [ -f ~/.claude/settings.json ]; then
    echo "   ✓ Settings file exists"
    echo "   Hooks configured: $(jq -r '.hooks | keys[]' ~/.claude/settings.json 2>/dev/null | wc -l) event types"
else
    echo "   ✗ Settings file not found"
fi
echo ""

# Check hook scripts
echo "3. Hook Scripts:"
HOOK_DIR="$HOME/.claude/hooks"
if [ -d "$HOOK_DIR" ]; then
    echo "   ✓ Hooks directory exists"
    for script in "$HOOK_DIR"/*.sh; do
        if [ -f "$script" ]; then
            if [ -x "$script" ]; then
                echo "   ✓ $(basename "$script") - executable"
            else
                echo "   ✗ $(basename "$script") - not executable"
            fi
        fi
    done
else
    echo "   ✗ Hooks directory not found"
fi
echo ""

# Check logs
echo "4. Log Files:"
LOG_DIR="$HOME/.claude/logs"
if [ -d "$LOG_DIR" ]; then
    echo "   ✓ Logs directory exists"
    LOG_COUNT=$(find "$LOG_DIR" -name "*.log" -type f 2>/dev/null | wc -l)
    echo "   Log files found: $LOG_COUNT"
else
    echo "   ✗ Logs directory not found"
fi

# Check session progress
PROGRESS_LOG="$HOME/.claude/session_progress.log"
if [ -f "$PROGRESS_LOG" ]; then
    echo "   ✓ Session progress log exists"
    echo "   Last entry: $(tail -1 "$PROGRESS_LOG")"
else
    echo "   ✗ Session progress log not found"
fi
echo ""

# Check environment
echo "5. Environment:"
echo "   CLAUDECODE: ${CLAUDECODE:-not set}"
echo "   CLAUDE_CODE_ENTRYPOINT: ${CLAUDE_CODE_ENTRYPOINT:-not set}"
echo "   PATH contains Claude: $(echo "$PATH" | grep -q claude && echo "Yes" || echo "No")"
echo ""

# Check process
echo "6. Running Process:"
CLAUDE_PID=$(ps aux | grep -i 'claude' | grep -v grep | awk '{print $2}' | head -1)
if [ -n "$CLAUDE_PID" ]; then
    echo "   ✓ Claude process found (PID: $CLAUDE_PID)"
    # Check if running with debug
    if ps aux | grep -i 'claude.*--debug' | grep -v grep > /dev/null; then
        echo "   Running in debug mode"
    else
        echo "   NOT running in debug mode"
        echo "   To see hook output, restart with: claude --debug"
    fi
else
    echo "   ✗ No Claude process found"
fi
echo ""

# Test hook execution
echo "7. Hook Execution Test:"
echo "   Creating test trigger file..."
TEST_FILE="/tmp/claude_hook_test_$(date +%s).txt"
echo "test" > "$TEST_FILE"

# Wait a moment
sleep 2

# Check if hooks created any new logs
NEW_LOGS=$(find "$HOME/.claude" -name "*.log" -newer "$TEST_FILE" -type f 2>/dev/null | wc -l)
if [ "$NEW_LOGS" -gt 0 ]; then
    echo "   ✓ Hooks appear to be creating logs"
else
    echo "   ✗ No new logs detected after trigger"
fi

# Clean up
rm -f "$TEST_FILE"
echo ""

# Recommendations
echo "8. Recommendations:"
if ! ps aux | grep -i 'claude.*--debug' | grep -v grep > /dev/null; then
    echo "   • Restart Claude Code with debug flag to see hook output:"
    echo "     claude --debug"
fi
echo "   • Check hook execution manually:"
echo "     $HOOK_DIR/session_init.sh"
echo "   • Monitor logs:"
echo "     tail -f $HOME/.claude/session_progress.log"
echo ""

echo "=== Diagnostic Complete ==="