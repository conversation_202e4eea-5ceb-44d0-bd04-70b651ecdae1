#!/bin/bash

echo "============================================================"
echo "      Electrical Contracting Application - macOS/Linux"
echo "============================================================"
echo

# Check Node.js
if ! command -v node &> /dev/null; then
    echo "ERROR: Node.js is not installed!"
    echo "Please install Node.js 18+ from https://nodejs.org/"
    exit 1
fi

# Navigate to app directory
cd "$(dirname "$0")/app"

# Install dependencies if needed
if [ ! -d "node_modules/express" ]; then
    echo "Installing dependencies (first time setup)..."
    npm install
fi

# Setup database if needed
if [ ! -f "backend/prisma/dev.db" ]; then
    echo "Setting up database..."
    cd backend
    npx prisma generate
    npx prisma migrate deploy
    npm run db:seed
    cd ..
fi

# Start the application
echo
echo "Starting application..."
echo

# Open new terminal windows based on OS
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    osascript -e 'tell app "Terminal" to do script "cd '"$(pwd)"'/backend && npm run dev"'
    sleep 2
    osascript -e 'tell app "Terminal" to do script "cd '"$(pwd)"'/frontend && npm run dev"'
else
    # Linux
    if command -v gnome-terminal &> /dev/null; then
        gnome-terminal -- bash -c "cd backend && npm run dev; exec bash"
        sleep 2
        gnome-terminal -- bash -c "cd frontend && npm run dev; exec bash"
    else
        # Fallback - run in background
        cd backend && npm run dev &
        cd ../frontend && npm run dev &
    fi
fi

echo
echo "============================================================"
echo "Application is starting!"
echo
echo "Frontend: http://localhost:3000"
echo "Backend:  http://localhost:3001"
echo
echo "Login: <EMAIL> / password123"
echo "============================================================"
echo