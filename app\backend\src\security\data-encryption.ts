import { encryptField, decrypt<PERSON><PERSON> } from './crypto';
import { prisma } from '../database/prisma';

// Fields that should be encrypted in the database
export const ENCRYPTED_FIELDS = {
  User: ['social_security_number', 'bank_account', 'routing_number'],
  Customer: ['tax_id', 'credit_card_number'],
  Employee: ['ssn', 'driver_license'],
  BankInfo: ['account_number', 'routing_number']
} as const;

// Prisma middleware for automatic encryption/decryption
export function setupEncryptionMiddleware() {
  // Encrypt on create/update
  prisma.$use(async (params, next) => {
    const modelFields = ENCRYPTED_FIELDS[params.model as keyof typeof ENCRYPTED_FIELDS];
    
    if (modelFields && (params.action === 'create' || params.action === 'update')) {
      const data = params.args.data;
      
      for (const field of modelFields) {
        if (data && data[field]) {
          data[field] = encryptField(data[field]);
        }
      }
    }
    
    if (modelFields && params.action === 'updateMany') {
      const data = params.args.data;
      
      for (const field of modelFields) {
        if (data && data[field]) {
          data[field] = encryptField(data[field]);
        }
      }
    }
    
    const result = await next(params);
    
    // Decrypt on read
    if (modelFields && result) {
      const decryptData = (item: Record<string, unknown>) => {
        for (const field of modelFields) {
          if (item && item[field]) {
            try {
              item[field] = decryptField(item[field]);
            } catch (error) {
              // Log decryption error but don't expose it
              console.error(`Failed to decrypt ${params.model}.${field}:`, error);
              item[field] = null;
            }
          }
        }
        return item;
      };
      
      if (Array.isArray(result)) {
        result.forEach(decryptData);
      } else {
        decryptData(result);
      }
    }
    
    return result;
  });
}

// Manual encryption for specific use cases
export async function encryptSensitiveData(
  model: string,
  id: string,
  data: Record<string, unknown>
): Promise<Record<string, unknown>> {
  const encryptedData = { ...data };
  const fields = ENCRYPTED_FIELDS[model as keyof typeof ENCRYPTED_FIELDS];
  
  if (fields) {
    for (const field of fields) {
      if (encryptedData[field]) {
        encryptedData[field] = encryptField(encryptedData[field]);
      }
    }
  }
  
  return encryptedData;
}

// Decrypt sensitive data with access logging
export async function decryptSensitiveData(
  model: string,
  id: string,
  data: Record<string, unknown>,
  userId: string,
  reason: string
): Promise<Record<string, unknown>> {
  // Log access to sensitive data
  await prisma.$executeRaw`
    INSERT INTO sensitive_data_access_log (
      id, user_id, model, record_id, fields_accessed, reason, accessed_at
    )
    VALUES (
      ${crypto.randomUUID()}, ${userId}, ${model}, ${id}, 
      ${JSON.stringify(Object.keys(data))}, ${reason}, ${new Date()}
    )
  `;
  
  const decryptedData = { ...data };
  const fields = ENCRYPTED_FIELDS[model as keyof typeof ENCRYPTED_FIELDS];
  
  if (fields) {
    for (const field of fields) {
      if (decryptedData[field]) {
        try {
          decryptedData[field] = decryptField(decryptedData[field]);
        } catch (error) {
          // Failed to decrypt field - log through proper logging service if configured
          decryptedData[field] = null;
        }
      }
    }
  }
  
  return decryptedData;
}

// Mask sensitive data for display
export function maskSensitiveData(value: string, type: string): string {
  if (!value) return '';
  
  switch (type) {
    case 'ssn':
      // Show only last 4 digits: ***-**-1234
      return value.replace(/^(\d{3})(\d{2})(\d{4})$/, '***-**-$3');
    
    case 'credit_card':
      // Show only last 4 digits: **** **** **** 1234
      return value.replace(/^(\d{4})(\d{4})(\d{4})(\d{4})$/, '**** **** **** $4');
    
    case 'bank_account':
      // Show only last 4 digits: ****1234
      const last4 = value.slice(-4);
      return '*'.repeat(value.length - 4) + last4;
    
    case 'email':
      // Partially mask email: j***@example.com
      const [localPart, domain] = value.split('@');
      if (localPart.length <= 2) {
        return `**@${domain}`;
      }
      return `${localPart[0]}${'*'.repeat(localPart.length - 2)}${localPart[localPart.length - 1]}@${domain}`;
    
    case 'phone':
      // Show only last 4 digits: (***) ***-1234
      return value.replace(/^(\d{3})(\d{3})(\d{4})$/, '(***) ***-$3');
    
    default:
      // Generic masking: show first and last character
      if (value.length <= 2) return '*'.repeat(value.length);
      return value[0] + '*'.repeat(value.length - 2) + value[value.length - 1];
  }
}

// Redact sensitive data from logs
export function redactSensitiveData(obj: unknown, depth: number = 0): unknown {
  if (depth > 10) return '[MAX_DEPTH]'; // Prevent infinite recursion
  
  if (Array.isArray(obj)) {
    return obj.map(item => redactSensitiveData(item, depth + 1));
  }
  
  if (obj !== null && typeof obj === 'object') {
    const redacted: Record<string, unknown> = {};
    
    for (const [key, value] of Object.entries(obj)) {
      const lowerKey = key.toLowerCase();
      
      // Check if field should be redacted
      if (
        lowerKey.includes('password') ||
        lowerKey.includes('secret') ||
        lowerKey.includes('token') ||
        lowerKey.includes('ssn') ||
        lowerKey.includes('social_security') ||
        lowerKey.includes('credit_card') ||
        lowerKey.includes('bank_account') ||
        lowerKey.includes('routing_number') ||
        lowerKey.includes('api_key') ||
        lowerKey.includes('private_key')
      ) {
        redacted[key] = '[REDACTED]';
      } else if (typeof value === 'object') {
        redacted[key] = redactSensitiveData(value, depth + 1);
      } else {
        redacted[key] = value;
      }
    }
    
    return redacted;
  }
  
  return obj;
}