@echo off
echo ============================================================
echo     Electrical Contracting App - Android Development
echo ============================================================
echo.

REM Check Java
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Java is not installed!
    echo Please install Java 17+ from https://adoptium.net/
    pause
    exit /b 1
)

REM Navigate to mobile directory
cd /d "%~dp0app\mobile"

REM Install dependencies if needed
if not exist "node_modules\react-native" (
    echo Installing mobile dependencies...
    npm install --legacy-peer-deps
)

REM Start Metro bundler and run Android
echo.
echo Starting React Native for Android...
echo.
echo 1. Make sure Android emulator is running or device is connected
echo 2. Metro bundler will start automatically
echo.
pause

REM Start Metro in new window
start "Metro Bundler" cmd /c "npx react-native start"

REM Wait for Metro to initialize
timeout /t 5 /nobreak > nul

REM Run Android app
echo.
echo Building and installing Android app...
npx react-native run-android

echo.
echo ============================================================
echo If the app doesn't appear on your device:
echo 1. Make sure Metro bundler is running (check other window)
echo 2. Shake device or press 'r' in Metro to reload
echo 3. Check device is connected: adb devices
echo ============================================================
echo.
pause