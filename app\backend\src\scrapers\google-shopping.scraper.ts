import { Page } from 'playwright';
import { BaseScraper, SearchResult } from './base.scraper';
import { logger } from '../utils/logger';

export class GoogleShoppingScraper extends BaseScraper {
  protected searchUrl(query: string): string {
    const encoded = encodeURIComponent(query + ' electrical');
    return `https://www.google.com/search?q=${encoded}&tbm=shop`;
  }

  protected async waitForResults(page: Page) {
    try {
      // Accept cookies if prompted
      const acceptButton = await page.$('button:has-text("Accept all")');
      if (acceptButton) {
        await acceptButton.click();
        await this.delay(1000);
      }

      // Wait for shopping results to load
      await page.waitForSelector('.sh-dgr__content, .sh-pr__product-results', {
        timeout: 10000,
        state: 'visible'
      });
    } catch (error) {
      // Try alternative selectors
      await page.waitForSelector('.commercial-unit, .sh-dlr__list-result', {
        timeout: 10000,
        state: 'visible'
      });
    }
  }

  protected async extractResults(page: Page): Promise<SearchResult[]> {
    const results: SearchResult[] = [];

    try {
      // Take screenshot for debugging
      await this.takeScreenshot(page, 'google-shopping-search');

      // Extract product information
      const products = await page.evaluate(() => {
        const items: any[] = [];
        
        // Try multiple selectors for products
        const productElements = document.querySelectorAll(
          '.sh-dgr__content, ' +
          '.sh-dlr__list-result, ' +
          '.sh-pr__product-results > div, ' +
          '.commercial-unit'
        );

        productElements.forEach((element) => {
          try {
            // Title
            const titleElement = element.querySelector(
              'h3, ' +
              '.sh-np__product-title, ' +
              '[class*="product-title"], ' +
              'a[aria-label]'
            );
            const title = titleElement?.textContent?.trim() || 
                         titleElement?.getAttribute('aria-label') || '';
            
            // URL
            const linkElement = element.querySelector('a[href*="/shopping/product"]');
            const url = linkElement?.getAttribute('href') || '';

            // Price
            const priceElement = element.querySelector(
              '.a8Pemb, ' +
              '[class*="price"], ' +
              'span:has-text("$")'
            );
            const priceText = priceElement?.textContent?.trim() || '';

            // Store/Seller
            const storeElement = element.querySelector(
              '.aULzUe, ' +
              '.sh-np__seller-name, ' +
              '[class*="merchant-name"], ' +
              '.LbUacb'
            );
            const store = storeElement?.textContent?.trim() || '';

            // Image
            const imageElement = element.querySelector('img');
            const imageUrl = imageElement?.getAttribute('src') || '';

            // Rating
            const ratingElement = element.querySelector(
              '[aria-label*="stars"], ' +
              '.Rsc7Yb, ' +
              '[class*="rating"]'
            );
            const ratingText = ratingElement?.getAttribute('aria-label') || '';
            const ratingMatch = ratingText.match(/(\d+\.?\d*)\s*star/i);
            const rating = ratingMatch ? parseFloat(ratingMatch[1]) : undefined;

            // Review count
            const reviewElement = element.querySelector(
              '.NzUzee, ' +
              '[class*="reviews"], ' +
              '.sh-np__reviews'
            );
            const reviewText = reviewElement?.textContent?.trim() || '';
            const reviewMatch = reviewText.match(/\(?([\d,]+)\)?/);
            const reviewCount = reviewMatch ? 
              parseInt(reviewMatch[1].replace(/,/g, '')) : undefined;

            // Additional details (shipping, features)
            const detailElements = element.querySelectorAll(
              '.sh-ds__trunc-txt, ' +
              '.sh-np__feature, ' +
              '[class*="shipping"]'
            );
            const details: string[] = [];
            detailElements.forEach(detail => {
              const text = detail.textContent?.trim();
              if (text) details.push(text);
            });

            if (title && priceText) {
              items.push({
                title,
                priceText,
                url: url.startsWith('http') ? url : `https://www.google.com${url}`,
                store,
                imageUrl,
                rating,
                reviewCount,
                details: details.join(' • ')
              });
            }
          } catch (err) {
            console.error('Error extracting product:', err);
          }
        });

        return items;
      });

      // Process extracted data
      for (const product of products) {
        const result: SearchResult = {
          title: product.title,
          price: this.parsePrice(product.priceText),
          url: product.url,
          imageUrl: product.imageUrl,
          manufacturer: this.extractManufacturer(product.title, product.store),
          availability: this.extractAvailability(product.details),
          rating: product.rating,
          reviewCount: product.reviewCount,
          unit: this.extractUnit(product.title, product.priceText),
          category: 'Electrical',
          confidence: 0.85, // Slightly lower confidence for Google Shopping
          description: product.details
        };

        results.push(result);
      }

      logger.info(`Extracted ${results.length} results from Google Shopping`);
    } catch (error) {
      logger.error('Error extracting Google Shopping results:', error);
      await this.takeScreenshot(page, 'google-shopping-error');
    }

    return results;
  }

  protected async extractProductDetails(page: Page, url: string): Promise<SearchResult> {
    try {
      // Google Shopping product pages vary by merchant, so we'll extract basic info
      await page.waitForSelector('h1, [data-sh-cr="title"], .sh-t__title', {
        timeout: 10000
      });

      const details = await page.evaluate(() => {
        // Title
        const titleElement = document.querySelector(
          'h1, ' +
          '[data-sh-cr="title"], ' +
          '.sh-t__title, ' +
          '[class*="product-title"]'
        );
        const title = titleElement?.textContent?.trim() || '';

        // Price
        const priceElement = document.querySelector(
          '[data-sh-cr="price"], ' +
          '.sh-pricebar__price, ' +
          '[class*="price"]:has-text("$")'
        );
        const priceText = priceElement?.textContent?.trim() || '';

        // Description
        const descElement = document.querySelector(
          '[data-sh-ds="desc"], ' +
          '.sh-ds__full-txt, ' +
          '[class*="description"]'
        );
        const description = descElement?.textContent?.trim() || '';

        // Image
        const imageElement = document.querySelector(
          '.sh-div__image img, ' +
          '[data-sh-cr="image"] img, ' +
          'img[alt*="product"]'
        );
        const imageUrl = imageElement?.getAttribute('src') || '';

        // Merchant info
        const merchantElement = document.querySelector(
          '.sh-osd__merchant-name, ' +
          '[data-sh-cr="merchant"], ' +
          '[class*="seller-name"]'
        );
        const merchant = merchantElement?.textContent?.trim() || '';

        // Specifications (if available)
        const specs: Record<string, string> = {};
        const specElements = document.querySelectorAll(
          '.sh-tsd__spec-row, ' +
          '.specs-group__row, ' +
          'tr:has(td)'
        );
        specElements.forEach(row => {
          const label = row.querySelector('td:first-child, .sh-tsd__spec-name')?.textContent?.trim();
          const value = row.querySelector('td:last-child, .sh-tsd__spec-value')?.textContent?.trim();
          if (label && value) {
            specs[label] = value;
          }
        });

        // Rating
        const ratingElement = document.querySelector(
          '[data-sh-cr="rating"], ' +
          '.sh-npr__reviews-section [aria-label*="stars"]'
        );
        const ratingText = ratingElement?.getAttribute('aria-label') || '';
        const ratingMatch = ratingText.match(/(\d+\.?\d*)/);
        const rating = ratingMatch ? parseFloat(ratingMatch[1]) : undefined;

        return {
          title,
          priceText,
          description,
          imageUrl,
          merchant,
          specifications: specs,
          rating
        };
      });

      return {
        title: details.title,
        price: this.parsePrice(details.priceText),
        url,
        description: details.description,
        imageUrl: details.imageUrl,
        manufacturer: this.extractManufacturer(details.title, details.merchant),
        unit: this.extractUnit(details.title, details.priceText),
        category: 'Electrical',
        confidence: 0.85,
        rating: details.rating
      };

    } catch (error) {
      logger.error('Error extracting Google Shopping product details:', error);
      throw error;
    }
  }

  private extractManufacturer(title: string, store: string): string {
    // Common electrical manufacturers
    const manufacturers = [
      'Southwire', 'Leviton', 'Eaton', 'Square D', 'Siemens',
      'GE', 'Lutron', 'Legrand', 'Pass & Seymour', 'Hubbell',
      'Klein Tools', 'Fluke', 'Milwaukee', 'DeWalt', 'Greenlee',
      'Ideal', 'Panduit', 'Cooper', '3M', 'Thomas & Betts'
    ];

    // Check title for manufacturer
    for (const mfg of manufacturers) {
      if (title.toLowerCase().includes(mfg.toLowerCase())) {
        return mfg;
      }
    }

    // Check if store name might be manufacturer
    if (store && !store.toLowerCase().includes('depot') && 
        !store.toLowerCase().includes('lowes') &&
        !store.toLowerCase().includes('amazon')) {
      return store;
    }

    return '';
  }

  private extractAvailability(details: string): string {
    const detailsLower = details.toLowerCase();
    
    if (detailsLower.includes('in stock')) return 'In Stock';
    if (detailsLower.includes('out of stock')) return 'Out of Stock';
    if (detailsLower.includes('ships') || detailsLower.includes('delivery')) {
      return 'Available for Shipping';
    }
    
    return '';
  }

  private extractUnit(title: string, priceText: string): string {
    // Check for common units in title or price
    const text = `${title} ${priceText}`.toLowerCase();
    
    if (text.includes('/ft') || text.includes('per foot') || text.includes('foot')) return 'FT';
    if (text.includes('/in') || text.includes('per inch')) return 'IN';
    if (text.includes('/yd') || text.includes('per yard')) return 'YD';
    if (text.includes('/box') || text.includes('box of')) return 'BOX';
    if (text.includes('/case') || text.includes('case of')) return 'CASE';
    if (text.includes('/roll') || text.includes('roll')) return 'ROLL';
    if (text.includes('/pkg') || text.includes('/pack') || text.includes('pack of')) return 'PKG';
    if (text.includes('/pair') || text.includes('pair')) return 'PAIR';
    if (text.includes('/set') || text.includes('set of')) return 'SET';
    if (text.includes('/bag') || text.includes('bag of')) return 'BAG';
    if (text.includes('/spool')) return 'SPOOL';
    
    // Check for quantity indicators
    const qtyMatch = text.match(/\b(\d+)[- ]?(pack|pk|count|ct|pc|pcs|pieces?)\b/i);
    if (qtyMatch) return 'PKG';
    
    // Check for length indicators
    if (text.match(/\b\d+[- ]?(ft|feet|foot)\b/i)) return 'FT';
    if (text.match(/\b\d+[- ]?(in|inch|inches)\b/i)) return 'IN';
    
    // Default to each
    return 'EA';
  }
}