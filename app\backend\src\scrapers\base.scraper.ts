import { Page } from 'playwright';
import { chrom<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>rowser<PERSON>ontext } from 'playwright';
import { logger } from '../utils/logger';
import UserAgent from 'user-agents';

export interface ScraperOptions {
  headless?: boolean;
  timeout?: number;
  retryAttempts?: number;
  retryDelay?: number;
  userAgent?: string;
  viewport?: {
    width: number;
    height: number;
  };
}

export interface SearchResult {
  title: string;
  price: number;
  url: string;
  sku?: string;
  imageUrl?: string;
  description?: string;
  availability?: string;
  manufacturer?: string;
  unit?: string;
  rating?: number;
  reviewCount?: number;
  category?: string;
  confidence?: number;
}

export abstract class BaseScraper {
  protected browser: Browser | null = null;
  protected context: BrowserContext | null = null;
  protected options: ScraperOptions;
  protected userAgentGenerator: any;

  constructor(options: ScraperOptions = {}) {
    this.options = {
      headless: true,
      timeout: 30000,
      retryAttempts: 3,
      retryDelay: 1000,
      viewport: { width: 1920, height: 1080 },
      ...options,
    };
    this.userAgentGenerator = new UserAgent();
  }

  protected abstract searchUrl(query: string): string;
  protected abstract extractResults(page: Page): Promise<SearchResult[]>;
  protected abstract extractProductDetails(page: Page, url: string): Promise<SearchResult>;

  async initialize() {
    if (!this.browser) {
      this.browser = await chromium.launch({
        headless: this.options.headless,
        args: [
          '--disable-blink-features=AutomationControlled',
          '--disable-features=IsolateOrigins,site-per-process',
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu',
        ],
      });
    }

    if (!this.context) {
      const userAgent = this.options.userAgent || this.userAgentGenerator.toString();
      
      this.context = await this.browser.newContext({
        userAgent,
        viewport: this.options.viewport,
        locale: 'en-US',
        timezoneId: 'America/New_York',
        extraHTTPHeaders: {
          'Accept-Language': 'en-US,en;q=0.9',
          'Accept-Encoding': 'gzip, deflate, br',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        },
      });

      // Add stealth scripts
      await this.context.addInitScript(() => {
        // This code runs in the browser context
        // @ts-ignore - browser context
        Object.defineProperty(navigator, 'webdriver', {
          get: () => undefined,
        });

        // @ts-ignore - browser context
        Object.defineProperty(navigator, 'plugins', {
          get: () => [1, 2, 3, 4, 5],
        });

        // @ts-ignore - browser context
        Object.defineProperty(navigator, 'languages', {
          get: () => ['en-US', 'en'],
        });

        // Override permissions
        // @ts-ignore - browser context
        const originalQuery = window.navigator.permissions.query;
        // @ts-ignore - browser context
        window.navigator.permissions.query = (parameters: any) => (
          parameters.name === 'notifications' ?
            // @ts-ignore - browser context
            Promise.resolve({ state: 'denied' }) :
            originalQuery(parameters)
        );
      });
    }
  }

  async search(query: string, options: { limit?: number } = {}): Promise<SearchResult[]> {
    const limit = options.limit || 10;
    let attempts = 0;
    
    while (attempts < this.options.retryAttempts!) {
      try {
        await this.initialize();
        
        const page = await this.context!.newPage();
        
        // Random delay to appear more human
        await this.randomDelay(500, 1500);
        
        // Navigate to search page
        const url = this.searchUrl(query);
        logger.info(`Scraping ${url}`);
        
        await page.goto(url, {
          waitUntil: 'networkidle',
          timeout: this.options.timeout,
        });
        
        // Wait for results to load
        await this.waitForResults(page);
        
        // Extract results
        const results = await this.extractResults(page);
        
        await page.close();
        
        // Limit results
        return results.slice(0, limit);
        
      } catch (error: any) {
        attempts++;
        logger.error(`Scraping attempt ${attempts} failed:`, error);
        
        if (attempts >= this.options.retryAttempts!) {
          throw error;
        }
        
        // Wait before retry
        await this.delay(this.options.retryDelay! * attempts);
      }
    }
    
    return [];
  }

  async getProductDetails(url: string): Promise<SearchResult> {
    let attempts = 0;
    
    while (attempts < this.options.retryAttempts!) {
      try {
        await this.initialize();
        
        const page = await this.context!.newPage();
        
        // Random delay
        await this.randomDelay(500, 1500);
        
        await page.goto(url, {
          waitUntil: 'networkidle',
          timeout: this.options.timeout,
        });
        
        const result = await this.extractProductDetails(page, url);
        
        await page.close();
        
        return result;
        
      } catch (error: any) {
        attempts++;
        logger.error(`Product detail scraping attempt ${attempts} failed:`, error);
        
        if (attempts >= this.options.retryAttempts!) {
          throw error;
        }
        
        await this.delay(this.options.retryDelay! * attempts);
      }
    }
    
    throw new Error('Failed to get product details');
  }

  protected async waitForResults(page: Page) {
    // Default implementation - override in subclasses
    await page.waitForTimeout(2000);
  }

  protected async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  protected async randomDelay(min: number, max: number): Promise<void> {
    const delay = Math.floor(Math.random() * (max - min + 1)) + min;
    return this.delay(delay);
  }

  protected parsePrice(priceText: string): number {
    // Remove currency symbols and convert to number
    const cleaned = priceText.replace(/[^0-9.,]/g, '');
    const normalized = cleaned.replace(/,/g, '');
    return parseFloat(normalized) || 0;
  }

  protected async scrollPage(page: Page, scrolls: number = 3) {
    for (let i = 0; i < scrolls; i++) {
      await page.evaluate(() => {
        // @ts-ignore - browser context
        window.scrollBy(0, window.innerHeight / 2);
      });
      await this.delay(500);
    }
  }

  protected async takeScreenshot(page: Page, name: string) {
    if (process.env.DEBUG_SCRAPING === 'true') {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      await page.screenshot({ 
        path: `./debug/scraping/${name}-${timestamp}.png`,
        fullPage: true 
      });
    }
  }

  async close() {
    if (this.context) {
      await this.context.close();
      this.context = null;
    }
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
  }
}