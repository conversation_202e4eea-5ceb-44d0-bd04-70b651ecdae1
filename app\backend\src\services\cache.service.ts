import { redisManager } from './redis-manager';
import { createHash } from 'crypto';

export class CacheService {
  private static readonly DEFAULT_TTL = 3600; // 1 hour
  private static readonly CALCULATION_TTL = 86400; // 24 hours
  private static readonly PRICE_TTL = 300; // 5 minutes

  /**
   * Generate a cache key based on the type and parameters
   */
  static generateKey(type: string, params: Record<string, unknown>): string {
    const sortedParams = Object.keys(params)
      .sort()
      .reduce((obj, key) => {
        obj[key] = params[key];
        return obj;
      }, {} as Record<string, unknown>);
    
    const hash = createHash('sha256')
      .update(JSON.stringify(sortedParams))
      .digest('hex')
      .substring(0, 16);
    
    return `${type}:${hash}`;
  }

  /**
   * Cache electrical calculation results
   */
  static async cacheCalculation(
    type: string,
    input: Record<string, unknown>,
    output: Record<string, unknown>,
    ttl: number = CacheService.CALCULATION_TTL
  ): Promise<void> {
    const key = CacheService.generateKey(`calc:${type}`, input);
    const data = {
      input,
      output,
      cachedAt: new Date().toISOString(),
      type
    };
    
    await redisManager.set(key, JSON.stringify(data), ttl);
  }

  /**
   * Get cached calculation result
   */
  static async getCachedCalculation(
    type: string,
    input: Record<string, unknown>
  ): Promise<Record<string, unknown> | null> {
    const key = CacheService.generateKey(`calc:${type}`, input);
    const cached = await redisManager.get(key);
    
    if (!cached) return null;
    
    try {
      return JSON.parse(cached);
    } catch {
      return null;
    }
  }

  /**
   * Cache material price data
   */
  static async cacheMaterialPrice(
    catalogNumber: string,
    priceData: Record<string, unknown>,
    ttl: number = CacheService.PRICE_TTL
  ): Promise<void> {
    const key = `price:${catalogNumber}`;
    await redisManager.set(key, JSON.stringify({
      ...priceData,
      cachedAt: new Date().toISOString()
    }), ttl);
  }

  /**
   * Get cached material price
   */
  static async getCachedMaterialPrice(
    catalogNumber: string
  ): Promise<Record<string, unknown> | null> {
    const key = `price:${catalogNumber}`;
    const cached = await redisManager.get(key);
    
    if (!cached) return null;
    
    try {
      return JSON.parse(cached);
    } catch {
      return null;
    }
  }

  /**
   * Cache batch material prices
   */
  static async cacheMaterialPrices(
    prices: Array<{ catalogNumber: string; data: Record<string, unknown> }>,
    ttl: number = CacheService.PRICE_TTL
  ): Promise<void> {
    if (prices.length === 0) return;
    
    const operations: Array<[string, ...any[]]> = prices.map(({ catalogNumber, data }) => [
      'setex',
      `price:${catalogNumber}`,
      ttl,
      JSON.stringify({
        ...data,
        cachedAt: new Date().toISOString()
      })
    ]);
    
    await redisManager.multi(operations);
  }

  /**
   * Get batch material prices from cache
   */
  static async getCachedMaterialPrices(
    catalogNumbers: string[]
  ): Promise<Record<string, unknown>> {
    if (catalogNumbers.length === 0) return {};
    
    const operations: Array<[string, ...any[]]> = catalogNumbers.map(catalogNumber => [
      'get',
      `price:${catalogNumber}`
    ]);
    
    const results = await redisManager.multi(operations);
    const prices: Record<string, unknown> = {};
    
    if (!results) return prices;
    
    catalogNumbers.forEach((catalogNumber, index) => {
      const value = results[index];
      if (value) {
        try {
          prices[catalogNumber] = JSON.parse(value as string);
        } catch {
          // Skip invalid JSON
        }
      }
    });
    
    return prices;
  }

  /**
   * Cache panel load calculation
   */
  static async cachePanelLoadCalculation(
    panelId: string,
    calculation: Record<string, any>,
    ttl: number = CacheService.CALCULATION_TTL
  ): Promise<void> {
    const key = `panel:load:${panelId}`;
    await redisManager.set(key, JSON.stringify({
      ...calculation,
      cachedAt: new Date().toISOString()
    }), ttl);
  }

  /**
   * Get cached panel load calculation
   */
  static async getCachedPanelLoadCalculation(
    panelId: string
  ): Promise<Record<string, any> | null> {
    const key = `panel:load:${panelId}`;
    const cached = await redisManager.get(key);
    
    if (!cached) return null;
    
    try {
      return JSON.parse(cached);
    } catch {
      return null;
    }
  }

  /**
   * Invalidate panel load calculation cache
   */
  static async invalidatePanelLoadCalculation(panelId: string): Promise<void> {
    const key = `panel:load:${panelId}`;
    await redisManager.del(key);
  }

  /**
   * Cache estimate totals
   */
  static async cacheEstimateTotals(
    estimateId: string,
    totals: Record<string, any>,
    ttl: number = CacheService.DEFAULT_TTL
  ): Promise<void> {
    const key = `estimate:totals:${estimateId}`;
    await redisManager.set(key, JSON.stringify({
      ...totals,
      cachedAt: new Date().toISOString()
    }), ttl);
  }

  /**
   * Get cached estimate totals
   */
  static async getCachedEstimateTotals(
    estimateId: string
  ): Promise<Record<string, any> | null> {
    const key = `estimate:totals:${estimateId}`;
    const cached = await redisManager.get(key);
    
    if (!cached) return null;
    
    try {
      return JSON.parse(cached);
    } catch {
      return null;
    }
  }

  /**
   * Invalidate estimate totals cache
   */
  static async invalidateEstimateTotals(estimateId: string): Promise<void> {
    const key = `estimate:totals:${estimateId}`;
    await redisManager.del(key);
  }

  /**
   * Cache list query results
   */
  static async cacheListQuery(
    type: string,
    params: Record<string, any>,
    data: any,
    ttl: number = 60 // 1 minute for list queries
  ): Promise<void> {
    const key = CacheService.generateKey(`list:${type}`, params);
    await redisManager.set(key, JSON.stringify({
      data,
      cachedAt: new Date().toISOString()
    }), ttl);
  }

  /**
   * Get cached list query results
   */
  static async getCachedListQuery(
    type: string,
    params: Record<string, any>
  ): Promise<any | null> {
    const key = CacheService.generateKey(`list:${type}`, params);
    const cached = await redisManager.get(key);
    
    if (!cached) return null;
    
    try {
      const result = JSON.parse(cached);
      return result.data;
    } catch {
      return null;
    }
  }

  /**
   * Invalidate all caches for a specific type
   */
  static async invalidatePattern(pattern: string): Promise<void> {
    const client = redisManager.getClient();
    if (!client) return;
    
    try {
      const keys = await client.keys(`${pattern}:*`);
      if (keys.length > 0) {
        await redisManager.del(keys);
      }
    } catch (error) {
      // Pattern invalidation failed, but don't throw
      console.warn('Failed to invalidate pattern:', pattern, error);
    }
  }

  /**
   * Warm up cache with frequently used data
   */
  static async warmUpCache(): Promise<void> {
    // This method can be called on server start to pre-load frequently used data
    // Cache warmup initiated - log this through proper logging service if needed
    
    // Add specific warmup logic here based on usage patterns
    // For example: load common wire sizes, breaker ratings, etc.
  }

  /**
   * Get cache statistics
   */
  static async getCacheStats(): Promise<Record<string, any>> {
    const client = redisManager.getClient();
    if (!client) return { dbSize: 0, info: {}, health: redisManager.getHealth() };
    
    try {
      const info = await client.info('stats');
      const dbSize = await client.dbsize();
      
      return {
        dbSize,
        info: info.split('\n').reduce((acc, line) => {
          const [key, value] = line.split(':');
          if (key && value) {
            acc[key.trim()] = value.trim();
          }
          return acc;
        }, {} as Record<string, string>),
        health: redisManager.getHealth()
      };
    } catch (error) {
      return { dbSize: 0, info: {}, health: redisManager.getHealth(), error: (error as Error).message };
    }
  }
}