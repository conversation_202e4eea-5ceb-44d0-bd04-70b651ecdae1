import { Switch } from '@headlessui/react';
import clsx from 'clsx';

interface ToggleProps {
  label?: string;
  checked: boolean;
  onChange: (checked: boolean) => void;
  disabled?: boolean;
  className?: string;
}

export function Toggle({ label, checked, onChange, disabled = false, className }: ToggleProps) {
  return (
    <Switch.Group as="div" className={clsx("flex items-center", className)}>
      <Switch
        checked={checked}
        onChange={onChange}
        disabled={disabled}
        className={clsx(
          checked ? 'bg-primary-600' : 'bg-gray-200 dark:bg-gray-700',
          disabled && 'opacity-50 cursor-not-allowed',
          'relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2'
        )}
      >
        <span
          className={clsx(
            checked ? 'translate-x-6' : 'translate-x-1',
            'inline-block h-4 w-4 transform rounded-full bg-white transition-transform'
          )}
        />
      </Switch>
      {label && (
        <Switch.Label className="ml-3 text-sm font-medium text-gray-700 dark:text-gray-300">
          {label}
        </Switch.Label>
      )}
    </Switch.Group>
  );
}