# Redis Authentication Configuration

## Overview

The electrical contracting application is designed to work with or without <PERSON><PERSON>. When Redis is not available or not configured, the application automatically falls back to in-memory alternatives for caching and rate limiting.

## Running Without Redis (Default Development Setup)

By default, the application runs without Redis in development. Simply leave the `REDIS_HOST` environment variable empty in your `.env` file:

```env
REDIS_HOST=
REDIS_PORT=6379
REDIS_PASSWORD=
```

When Red<PERSON> is not configured:
- ✅ Application starts normally
- ✅ Rate limiting uses in-memory storage
- ✅ Caching is disabled
- ⚠️ Job queues are not available
- ⚠️ Real-time features may be limited

## Configuring Redis with Authentication

If you have a Redis server that requires authentication, update your `.env` file:

```env
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password_here
```

## Common Redis Authentication Errors

### NOAUTH Authentication Required

This error occurs when:
1. Your Redis server requires a password
2. No password is provided in the configuration

**Solution**: Either:
- Add the correct password to `REDIS_PASSWORD` in `.env`
- OR leave `REDIS_HOST` empty to run without Redis

### Connection Refused

This error occurs when:
1. Redis server is not running
2. Wrong host/port configuration

**Solution**: 
- Start Redis server: `redis-server`
- Or use Docker: `docker run -p 6379:6379 redis`
- Or leave `REDIS_HOST` empty to run without Redis

## Testing Redis Connection

You can test your Redis configuration using the provided script:

```bash
cd app/backend
npx tsx src/scripts/test-redis.ts
```

## Production Recommendations

For production environments:

1. **Always use authentication**: Set a strong password for Redis
2. **Use Redis persistence**: Configure AOF or RDB backups
3. **Monitor connection health**: Check `/health` endpoint regularly
4. **Set up Redis Sentinel**: For high availability
5. **Use SSL/TLS**: For encrypted connections (requires additional configuration)

## Environment Variables Reference

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `REDIS_HOST` | Redis server hostname | none | No |
| `REDIS_PORT` | Redis server port | 6379 | No |
| `REDIS_PASSWORD` | Redis authentication password | none | No |

## Troubleshooting

1. **Check application logs**: Look for Redis connection messages during startup
2. **Verify health endpoint**: `curl http://localhost:3001/health`
3. **Test Redis directly**: `redis-cli -h localhost -p 6379 -a your_password ping`
4. **Review Redis logs**: Check Redis server logs for authentication failures

## Related Documentation

- [Redis Setup Guide](./REDIS_SETUP.md)
- [Environment Configuration](./.env.example)