import { z } from 'zod';
import { Decimal } from 'decimal.js';

// Configure Decimal for electrical precision (4 decimal places)
Decimal.set({ precision: 10, rounding: 4 }); // 4 = ROUND_UP

// Customer types
export const CustomerSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1).max(255),
  email: z.string().email().optional(),
  phone: z.string().regex(/^\+?1?\d{10}$/).optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().length(2).optional(),
  zip: z.string().regex(/^\d{5}(-\d{4})?$/).optional(),
  license_number: z.string().optional(),
  insurance_expiry: z.date().optional(),
  credit_limit: z.number().positive().optional(),
  payment_terms: z.enum(['NET15', 'NET30', 'NET45', 'COD']).default('NET30'),
  created_at: z.date(),
  updated_at: z.date(),
  deleted_at: z.date().nullable()
});

export type Customer = z.infer<typeof CustomerSchema>;

// Project types with electrical specifications
export const ProjectSchema = z.object({
  id: z.string().uuid(),
  customer_id: z.string().uuid(),
  name: z.string().min(1).max(255),
  address: z.string(),
  city: z.string(),
  state: z.string().length(2),
  zip: z.string().regex(/^\d{5}(-\d{4})?$/),
  type: z.enum(['RESIDENTIAL', 'COMMERCIAL', 'INDUSTRIAL']),
  status: z.enum(['PLANNING', 'APPROVED', 'IN_PROGRESS', 'COMPLETED', 'ON_HOLD']),
  voltage_system: z.enum(['120/240V_1PH', '208V_3PH', '240V_3PH', '480V_3PH', '277/480V_3PH']),
  service_size: z.number().int().positive(), // In amperes
  square_footage: z.number().int().positive().optional(),
  permit_number: z.string().optional(),
  permit_expiry: z.date().optional(),
  inspection_status: z.enum(['PENDING', 'ROUGH_IN_PASSED', 'FINAL_PASSED', 'FAILED']).optional(),
  created_at: z.date(),
  updated_at: z.date()
});

export type Project = z.infer<typeof ProjectSchema>;

// Material types with electrical specifications
export const MaterialItemSchema = z.object({
  id: z.string().uuid(),
  estimate_id: z.string().uuid(),
  catalog_number: z.string(),
  description: z.string(),
  category: z.enum(['WIRE', 'CONDUIT', 'DEVICES', 'PANELS', 'FIXTURES', 'MISC']),
  unit: z.enum(['FT', 'EA', 'BOX', 'ROLL', 'HR']),
  quantity: z.number().positive(),
  unit_cost: z.number().positive(),
  markup_percent: z.number().min(0).max(100),
  waste_percent: z.number().min(0).max(25),
  tax_rate: z.number().min(0).max(0.15),
  extended_cost: z.number(), // Computed: quantity * unit_cost * (1 + markup_percent/100) * (1 + waste_percent/100)
  tax_amount: z.number(), // Computed: extended_cost * tax_rate
  total_amount: z.number(), // Computed: extended_cost + tax_amount
  supplier: z.string().optional(),
  wire_size: z.string().optional(), // AWG or kcmil
  wire_type: z.enum(['THHN', 'THWN', 'XHHW', 'NM', 'MC', 'USE']).optional(),
  conduit_size: z.string().optional(), // Trade size
  conduit_type: z.enum(['EMT', 'RMC', 'PVC', 'LFMC', 'LFNC']).optional(),
  voltage_rating: z.number().optional(),
  amperage_rating: z.number().optional(),
  phase: z.enum(['1PH', '3PH']).optional(),
  created_at: z.date(),
  updated_at: z.date()
});

export type MaterialItem = z.infer<typeof MaterialItemSchema>;

// Labor types with trade classifications
export const LaborItemSchema = z.object({
  id: z.string().uuid(),
  estimate_id: z.string().uuid(),
  description: z.string(),
  trade: z.enum(['ELECTRICIAN', 'APPRENTICE', 'HELPER', 'FOREMAN']),
  hours: z.number().positive(),
  rate: z.number().positive(),
  overtime_hours: z.number().min(0).default(0),
  overtime_rate: z.number().positive(), // Typically 1.5x or 2x regular rate
  burden_percent: z.number().min(0).max(50), // Labor burden (taxes, insurance, etc.)
  extended_cost: z.number(), // Computed: (hours * rate) + (overtime_hours * overtime_rate)
  burden_amount: z.number(), // Computed: extended_cost * burden_percent / 100
  total_amount: z.number(), // Computed: extended_cost + burden_amount
  created_at: z.date(),
  updated_at: z.date()
});

export type LaborItem = z.infer<typeof LaborItemSchema>;

// Estimate with version control
export const EstimateSchema = z.object({
  id: z.string().uuid(),
  project_id: z.string().uuid(),
  version: z.number().int().positive(),
  status: z.enum(['DRAFT', 'SENT', 'APPROVED', 'REJECTED', 'EXPIRED']),
  valid_until: z.date(),
  subtotal: z.number(), // Computed from material and labor items
  tax_total: z.number(), // Computed from material tax
  total_amount: z.number(), // Computed: subtotal + tax_total
  profit_margin: z.number().min(0).max(50),
  contingency_percent: z.number().min(0).max(25),
  notes: z.string().optional(),
  terms: z.string().optional(),
  created_by: z.string().uuid(),
  approved_by: z.string().uuid().optional(),
  approved_at: z.date().optional(),
  created_at: z.date(),
  updated_at: z.date()
});

export type Estimate = z.infer<typeof EstimateSchema>;

// Electrical calculation types
export const LoadCalculationSchema = z.object({
  project_id: z.string().uuid(),
  calculation_type: z.enum(['STANDARD', 'OPTIONAL', 'EXISTING']),
  general_lighting_va: z.number(), // 3 VA/sq ft residential, varies commercial
  small_appliance_va: z.number(), // 1500W per circuit, min 2 circuits
  laundry_va: z.number(), // 1500W
  appliance_loads: z.array(z.object({
    name: z.string(),
    va: z.number(),
    demand_factor: z.number().min(0).max(1)
  })),
  heating_cooling_va: z.number(),
  largest_motor_va: z.number(),
  other_loads_va: z.number(),
  total_computed_va: z.number(),
  demand_factor: z.number().min(0).max(1),
  total_demand_va: z.number(),
  required_amperage: z.number(), // total_demand_va / voltage
  recommended_service: z.number(), // Next standard size up
  nec_article_reference: z.string(),
  created_at: z.date()
});

export type LoadCalculation = z.infer<typeof LoadCalculationSchema>;

// Voltage drop calculation
export const VoltageDropSchema = z.object({
  project_id: z.string().uuid(),
  circuit_name: z.string(),
  voltage: z.number(),
  phase: z.enum(['1PH', '3PH']),
  amperage: z.number(),
  distance: z.number(), // One-way distance in feet
  conductor_size: z.string(), // AWG or kcmil
  conductor_type: z.enum(['CU', 'AL']),
  conduit_type: z.enum(['STEEL', 'PVC', 'AL']),
  power_factor: z.number().min(0).max(1).default(0.9),
  ambient_temp_f: z.number().default(86), // 30°C
  calculated_vd_volts: z.number(),
  calculated_vd_percent: z.number(),
  nec_limit_percent: z.number(), // 3% branch, 5% total
  passes_nec: z.boolean(),
  created_at: z.date()
});

export type VoltageDrop = z.infer<typeof VoltageDropSchema>;

// Price history for materials
export const MaterialPriceHistorySchema = z.object({
  id: z.string().uuid(),
  catalog_number: z.string(),
  supplier: z.string(),
  unit_cost: z.number().positive(),
  effective_date: z.date(),
  created_at: z.date()
});

export type MaterialPriceHistory = z.infer<typeof MaterialPriceHistorySchema>;

// Agent message types
export const AgentMessageSchema = z.object({
  id: z.string().uuid(),
  sender: z.enum(['project_manager', 'coding_agent', 'ui_designer', 'frontend_agent', 
                  'backend_agent', 'research_agent', 'debugging_agent', 'memory_agent', 
                  'prompt_engineering_agent']),
  recipient: z.enum(['project_manager', 'coding_agent', 'ui_designer', 'frontend_agent', 
                     'backend_agent', 'research_agent', 'debugging_agent', 'memory_agent', 
                     'prompt_engineering_agent', 'all']),
  priority: z.enum(['critical', 'high', 'medium', 'low']),
  task_type: z.enum(['feature_implementation', 'bug_fix', 'research', 'optimization', 
                     'testing', 'documentation', 'code_review']),
  message: z.string(),
  context: z.record(z.unknown()).optional(),
  status: z.enum(['pending', 'in_progress', 'completed', 'failed']),
  created_at: z.date(),
  updated_at: z.date()
});

export type AgentMessage = z.infer<typeof AgentMessageSchema>;

// Calculation log for audit trail
export const CalculationLogSchema = z.object({
  id: z.string().uuid(),
  calculation_type: z.enum(['LOAD_CALC', 'VOLTAGE_DROP', 'CONDUIT_FILL', 
                           'WIRE_SIZE', 'BREAKER_SIZE', 'GROUNDING']),
  input_data: z.record(z.unknown()),
  output_data: z.record(z.unknown()),
  nec_references: z.array(z.string()),
  performed_by: z.string().uuid(),
  project_id: z.string().uuid().optional(),
  created_at: z.date()
});

export type CalculationLog = z.infer<typeof CalculationLogSchema>;

// Panel and circuit types for panel schedules
export const PanelSchema = z.object({
  id: z.string().uuid().optional(),
  project_id: z.string().uuid(),
  name: z.string().min(1).max(255),
  location: z.string(),
  panel_type: z.enum(['MAIN', 'SUB', 'DISTRIBUTION']),
  manufacturer: z.string().optional(),
  model_number: z.string().optional(),
  catalog_number: z.string().optional(),
  voltage_system: z.enum(['120/240V_1PH', '208V_3PH', '240V_3PH', '480V_3PH', '277/480V_3PH']),
  ampere_rating: z.number().int().positive(),
  bus_rating: z.number().int().positive(),
  main_breaker_size: z.number().int().positive().optional(),
  phase_config: z.enum(['SINGLE_PHASE', 'THREE_PHASE_3W', 'THREE_PHASE_4W']),
  mounting_type: z.enum(['SURFACE', 'FLUSH', 'RECESSED']),
  enclosure_type: z.enum(['NEMA_1', 'NEMA_3R', 'NEMA_4', 'NEMA_4X']),
  spaces_total: z.number().int().positive(),
  spaces_used: z.number().int().min(0).optional(),
  fed_from_panel_id: z.string().uuid().optional(),
  fed_from_circuit: z.number().int().positive().optional(),
  notes: z.string().optional(),
  created_at: z.date().optional(),
  updated_at: z.date().optional()
});

export type Panel = z.infer<typeof PanelSchema>;

export const CircuitSchema = z.object({
  id: z.string().uuid().optional(),
  panel_id: z.string().uuid(),
  circuit_number: z.number().int().positive(),
  description: z.string().min(1).max(255),
  breaker_size: z.number().int().positive(),
  breaker_type: z.enum(['STANDARD', 'GFCI', 'AFCI', 'GFCI_AFCI', 'SPACE_ONLY']),
  poles: z.number().int().min(1).max(3),
  phase_connection: z.string().optional(),
  wire_size: z.string(),
  wire_type: z.enum(['THHN', 'THWN-2', 'XHHW-2', 'NM', 'MC', 'USE-2', 'RHH', 'RHW-2']),
  wire_count: z.number().int().positive(),
  conduit_type: z.enum(['EMT', 'PVC', 'RIGID', 'MC_CABLE', 'NM_CABLE', 'FLEX']).optional(),
  conduit_size: z.string().optional(),
  voltage: z.number().int().positive(),
  load_type: z.enum(['LIGHTING', 'RECEPTACLE', 'MOTOR', 'HVAC', 'APPLIANCE', 'FEEDER', 'EQUIPMENT']),
  continuous_load: z.boolean().default(false),
  connected_load: z.number().positive(),
  demand_factor: z.number().min(0).max(1).default(1),
  calculated_load: z.number().positive().optional(),
  room_area: z.string().optional(),
  control_type: z.enum(['SWITCH', 'TIMER', 'PHOTOCELL', 'OCCUPANCY', 'DIMMER', 'CONTACTOR']).optional(),
  is_spare: z.boolean().default(false),
  is_space: z.boolean().default(false),
  notes: z.string().optional(),
  created_at: z.date().optional(),
  updated_at: z.date().optional()
});

export type Circuit = z.infer<typeof CircuitSchema>;

export const PanelLoadCalculationSchema = z.object({
  id: z.string().uuid().optional(),
  panel_id: z.string().uuid(),
  calculation_date: z.date(),
  phase_a_load: z.number().min(0),
  phase_b_load: z.number().min(0),
  phase_c_load: z.number().min(0),
  neutral_load: z.number().min(0),
  total_connected_load: z.number().min(0),
  total_demand_load: z.number().min(0),
  load_percentage: z.number().min(0).max(100),
  phase_imbalance_percent: z.number().min(0),
  power_factor: z.number().min(0).max(1),
  ambient_temperature: z.number(),
  derating_factor: z.number().min(0).max(1),
  notes: z.string().optional(),
  created_by: z.string().uuid()
});

export type PanelLoadCalculation = z.infer<typeof PanelLoadCalculationSchema>;

// User and authentication types
export const UserSchema = z.object({
  id: z.string().uuid(),
  email: z.string().email(),
  name: z.string().min(1).max(255),
  role: z.enum(['ADMIN', 'ELECTRICIAN', 'APPRENTICE', 'ESTIMATOR', 'VIEWER']),
  companyId: z.string().optional(), // Add companyId to the schema
  license_number: z.string().optional(),
  license_state: z.string().length(2).optional(),
  license_expiry: z.date().optional(),
  phone: z.string().optional(),
  is_active: z.boolean().default(true),
  last_login: z.date().optional(),
  created_at: z.date(),
  updated_at: z.date()
});

export type User = z.infer<typeof UserSchema>;