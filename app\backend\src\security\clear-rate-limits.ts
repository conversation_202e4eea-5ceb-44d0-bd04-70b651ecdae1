import { rateLimitMiddleware } from './rate-limiting';
import { logger } from '../utils/logger';

/**
 * Utility to clear rate limits - useful for development/testing
 */
export async function clearRateLimits() {
  logger.info('Rate limit clearing is not available for in-memory rate limiters');
  logger.info('Please restart the server to clear in-memory rate limits');
}

// For development mode, add a reset endpoint
export function addRateLimitResetEndpoint(app: any) {
  if (process.env.NODE_ENV === 'development') {
    app.post('/api/dev/reset-rate-limits', async (req: any, res: any) => {
      // In development, we can just restart the rate limiters
      logger.debug('Rate limit reset requested in development mode');
      res.json({ 
        message: 'Rate limits are in-memory. Restart the server to clear them.',
        note: 'This endpoint is only available in development mode'
      });
    });
  }
}