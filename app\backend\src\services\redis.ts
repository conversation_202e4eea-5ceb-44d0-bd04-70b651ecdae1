import Redis from 'ioredis';
import dotenv from 'dotenv';

// Load environment variables first
dotenv.config();

import { redisManager } from './redis-manager';
import { logger } from '../utils/logger';

// Initialize Redis connection
export let redis: Redis | null = null;

export async function initializeRedis(): Promise<void> {
  // Use the new Redis manager
  const connected = await redisManager.connect();
  
  if (connected) {
    redis = redisManager.getClient();
    logger.info('Redis initialized via RedisManager');
  } else {
    redis = null;
    logger.warn('Redis not available - application will continue without caching');
  }
}

export function getRedis(): Redis | null {
  // Always get the current client from the manager
  return redisManager.getClient();
}