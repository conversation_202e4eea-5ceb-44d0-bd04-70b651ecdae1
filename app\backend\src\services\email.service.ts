import nodemailer from 'nodemailer';
import { config } from '../config';
import { logger } from '../utils/logger';

export class EmailService {
  private transporter: nodemailer.Transporter | null = null;

  constructor() {
    // Initialize email transporter if email config is provided
    if (config.email?.host) {
      this.transporter = nodemailer.createTransport({
        host: config.email.host,
        port: config.email.port || 587,
        secure: config.email.secure || false,
        auth: config.email.auth ? {
          user: config.email.auth.user,
          pass: config.email.auth.pass
        } : undefined
      });
    } else {
      logger.warn('Email service not configured - emails will not be sent');
    }
  }

  async sendQuoteEmail(
    to: string,
    quote: any,
    pdfBuffer: Buffer,
    viewUrl: string
  ): Promise<void> {
    if (!this.transporter) {
      logger.warn(`Email not sent to ${to} - email service not configured`);
      return;
    }

    const mailOptions = {
      from: config.email?.from || '<EMAIL>',
      to,
      subject: `Quote #${quote.quote_number} - ${quote.name}`,
      html: `
        <h2>Quote #${quote.quote_number}</h2>
        <p>Dear ${quote.customer?.name || 'Valued Customer'},</p>
        <p>Please find attached your quote for: ${quote.name}</p>
        <p><strong>Total Amount: $${quote.total.toFixed(2)}</strong></p>
        <p>This quote is valid until: ${new Date(quote.expires_at).toLocaleDateString()}</p>
        <p>You can view and approve this quote online at: <a href="${viewUrl}">${viewUrl}</a></p>
        <br>
        <p>Thank you for your business!</p>
      `,
      attachments: [
        {
          filename: `quote-${quote.quote_number}.pdf`,
          content: pdfBuffer
        }
      ]
    };

    try {
      await this.transporter.sendMail(mailOptions);
      logger.info(`Quote email sent to ${to} for quote ${quote.quote_number}`);
    } catch (error) {
      logger.error('Failed to send quote email:', error);
      throw new Error('Failed to send email');
    }
  }

  async sendQuoteStatusEmail(
    to: string,
    quote: any,
    status: 'approved' | 'rejected',
    reason?: string
  ): Promise<void> {
    if (!this.transporter) {
      logger.warn(`Email not sent to ${to} - email service not configured`);
      return;
    }

    const subject = status === 'approved' 
      ? `Quote #${quote.quote_number} Approved!`
      : `Quote #${quote.quote_number} Status Update`;

    const mailOptions = {
      from: config.email?.from || '<EMAIL>',
      to,
      subject,
      html: `
        <h2>Quote #${quote.quote_number} - ${status.toUpperCase()}</h2>
        <p>The quote "${quote.name}" has been ${status}.</p>
        ${reason ? `<p>Reason: ${reason}</p>` : ''}
        ${status === 'approved' ? '<p>We will contact you shortly to proceed with the next steps.</p>' : ''}
        <br>
        <p>Thank you!</p>
      `
    };

    try {
      await this.transporter.sendMail(mailOptions);
      logger.info(`Quote status email sent to ${to} for quote ${quote.quote_number}`);
    } catch (error) {
      logger.error('Failed to send quote status email:', error);
      throw new Error('Failed to send email');
    }
  }
}