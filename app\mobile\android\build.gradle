// Disable NetBeans tooling to prevent ConcurrentModificationException
System.setProperty('netbeans.gradle.project.disabled', 'true')
System.setProperty('netbeans.ignore.gradle.build', 'true')
System.setProperty('netbeans.tooling.disabled', 'true')
System.setProperty('netbeans.gradle.maven.disabled', 'true')

buildscript {
    ext {
        buildToolsVersion = "34.0.0"
        minSdkVersion = 23
        compileSdkVersion = 35
        targetSdkVersion = 34
        ndkVersion = "26.1.10909125"
        kotlinVersion = "1.8.0"
        // React Native 0.73.x compatibility
        RNNKotlinVersion = kotlinVersion
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle:8.6.0")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion")
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url "https://www.jitpack.io" }
        maven {
            url("${project(':react-native-background-fetch').projectDir}/libs")
        }
    }
}

apply plugin: "com.facebook.react.rootproject"