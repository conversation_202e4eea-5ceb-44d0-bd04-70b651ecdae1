# Dependencies
node_modules/
.pnp
.pnp.js

# Production builds
dist/
build/
out/

# Environment files
# .env (commented out to allow access)
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.idea/
.vscode/
*.swp
*.swo
*~

# Test coverage
coverage/
.nyc_output/

# Database
*.db
*.sqlite
*.sqlite3
database/*.db-journal
prisma/migrations/dev/

# Cache
.cache/
.parcel-cache/
.next/
.nuxt/
.turbo/
.vercel/
.vite/

# Temporary files
tmp/
temp/
.tmp/

# Package manager files
.yarn/
.pnpm-store/

# TypeScript
*.tsbuildinfo

# PWA files
public/sw.js
public/workbox-*.js
public/worker-*.js
public/fallback-*.js

# SSL certificates
*.pem
*.key
*.crt

# Agent system
agents/memory/
agents/logs/