import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../stores/auth';

export function useAuthRedirect() {
  const navigate = useNavigate();
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);

  useEffect(() => {
    // Subscribe to auth state changes
    const unsubscribe = useAuthStore.subscribe((state, prevState) => {
      // If user was authenticated and is now not authenticated, redirect to login
      if (prevState.isAuthenticated && !state.isAuthenticated) {
        navigate('/login', { replace: true });
      }
    });

    return unsubscribe;
  }, [navigate]);

  return { isAuthenticated };
}